import React from "react";
import shareIcon from "./share.png";
import "./styles.css";
import Divider from "@mui/material/Divider";
import Button from "@mui/material/Button";
import Cookie from "js-cookie";

import { Link } from "react-router-dom";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
const style = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  // width: 400,
  bgcolor: "background.paper",
  border: "2px solid #000",
  boxShadow: 24,
  pt: 2,
  px: 4,
  pb: 3,
};

const Packages = ({ data, linkText, percent, bucketId }) => {
  console.log(data);
  const {
    group_name,
    price,
    description,
    gid,
    scheduleLink,
    bucket,
    isNormalPackage,
  } = data;
  const [open, setOpen] = React.useState(false);
  const [share, setshare] = React.useState(false);
  const handleOpen = () => {
    setOpen(!open);
  };
  const handleShare = () => {
    setshare(!share);
  };
  const renderPopUp = () => {
    return (
      <Dialog open={open} onClose={handleOpen} maxWidth={"sm"} fullWidth>
        <DialogTitle>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <p style={{ fontSize: 14 }}>{group_name}</p>
            <div
              style={{
                backgroundColor: "red",
                color: "#fff",
                height: 30,
                width: 30,
                textAlign: "center",
                // borderRadius: 35,
              }}
              onClick={handleOpen}
            >
              X
            </div>
          </div>
        </DialogTitle>
        <DialogContent>
          {" "}
          <div
            // style={{ flexWrap: "wrap", width: "100%" }}
            dangerouslySetInnerHTML={{ __html: description }}
          ></div>
        </DialogContent>
      </Dialog>
    );
  };

  const sharePopUp = () => {
    return (
      <Dialog open={share} onClose={handleShare} maxWidth={"sm"} fullWidth>
        <DialogTitle>
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <p>Share & Earn</p>
            <div
              style={{
                backgroundColor: "red",
                color: "#fff",
                height: 30,
                width: 30,
                textAlign: "center",
                // borderRadius: 35,
              }}
              onClick={handleShare}
            >
              X
            </div>
          </div>
        </DialogTitle>
        <DialogContent>
          <p>
            క్రింది షేర్ బటన్ పై క్లిక్ చేసి మీ ఫ్రెండ్స్ కు ఈ ప్యాకేజి గురించిన
            వివరాలను, తెలియచేయండి. <br />
            ఒకవేళ మీ ఫ్రెండ్ ఈ ప్యాకేజికోసం పేమెంట్ చేసినట్లయితే అతను చేసిన
            పేమెంట్ నుంచి {percent}% మీకు రిఫరల్ బోనస్ గా మీ వాలెట్ కు
            చేర్చబడుతుంది.
          </p>
          <a
            href={linkText}
            target="_blank"
            rel="noopener"
            aria-label="Share on WhatsApp"
            data-action="share/whatsapp/share"
          >
            <div className="winscratch2">Share on WhatsApp</div>
          </a>
        </DialogContent>
      </Dialog>
    );
  };
  const token = Cookie.get("jwt_token");
  return (
    <div className="packages-container packagedesk">
      <div
        className="packages-inner-container"
        style={{
          marginTop: 20,
          border:
            isNormalPackage == "2"
              ? "3px solid yellow"
              : isNormalPackage == "3"
              ? "3px solid orange"
              : "",
        }}
      >
        <p className="menu-parar package-name">{group_name}</p>
        <Divider color="white" width="100%" />
        <div className="package-btns-containers">
          <Button
            variant="contained"
            className="btn header-btns packagebtn"
            onClick={handleOpen}
            style={{ width: isNormalPackage === "1" ? "100%" : "60%" }}
          >
            Details
          </Button>

          {isNormalPackage === "1" && (
            <Button
              variant="contained"
              className="btn header-btns packagebtn btnexam schedulebtn linksche"
            >
              <a href={scheduleLink} target="_blank" className="linkto ">
                Syllabus
              </a>
            </Button>
          )}
          {token !== undefined && (
            <img
              src={shareIcon}
              onClick={handleShare}
              style={{
                width: 40,
                height: 40,
                marginTop: 11,
                cursor: "pointer",
              }}
            />
          )}
        </div>
        {renderPopUp()}
        {sharePopUp()}
        {/* <Modal
          hideBackdrop
          open={open}
          onClose={handleClose}
          aria-labelledby="child-modal-title"
          aria-describedby="child-modal-description"
        >
          <Box sx={{ ...style, width: "80%" }}>
            <div className="closebtn">
              <h2 id="child-modal-title">{group_name}</h2>
              <div className="closebtn-modal" onClick={handleClose}>
                <CloseIcon className="clsebtn" />
              </div>
            </div>
            <p
              style={{ flexWrap: "wrap" }}
              id="child-modal-description"
              dangerouslySetInnerHTML={{ __html: description }}
            ></p>
          </Box>
        </Modal> */}
        <Divider color="white" width="100%" />
        <div className="package-btns-containers">
          <Button
            variant="contained"
            className="btn header-btns packagebtn buy amount"
          >
            {"₹ "}
            {price}
          </Button>

          <Link
            to={`/payumoney/payment/checkout/${gid}${
              bucket !== "0" ? "/aG9tZQ==" : "/" + window.btoa(bucket)
            }`}
            className="linkto"
          >
            <Button
              variant="contained"
              className="btn header-btns packagebtn buy buypack "
            >
              BUY NOW
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default Packages;
