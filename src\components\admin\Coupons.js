import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
import TextField from "@mui/material/TextField";
import ListItemText from "@mui/material/ListItemText";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import Checkbox from "@mui/material/Checkbox";
import "./styles.css";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
class Coupons extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    CouponData: [],
    page: 0,
    popUpOpen: false,
    couponId: null,
    editorLoading: false,
    search: "",
    CouponCount: 0,
    couponName: "",
    popupType: "",
    cCount: "",
    selectedPackages: [],
    searchClicked: false,
    packagesAllList: [],
    validityDays: "", // New state for validity days
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const { page, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CouponsALL",
      search: search,
      qid: page * 25,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      const packagesList = await axios.get(
        `${commonData["api"]}/adminmasterdata/packagelist`,
        {
          headers,
        }
      );
      console.log(data);
      this.setState({
        CouponData: data.data[0],
        CouponCount: data.data[1][0].count,
        isLoading: false,
        packagesAllList: packagesList.data[0],
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  searchCoupon = (e) => {
    e.preventDefault();
    const { search } = this.state;

    if (search === "") {
      NotificationManager.error(`Please Enter Search Value`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
      });
      this.getData();
    }
  };
  categoriesTable = () => {
    const { CouponData, CouponCount, searchClicked, search } = this.state;
    // console.log(CouponData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All Coupons</h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            {!searchClicked ? (
              <form
                onSubmit={this.searchCoupon}
                style={{ cursor: "pointer", display: "flex", marginBottom: 10 }}
              >
                <input
                  type={"text"}
                  value={search}
                  id="search"
                  onChange={(e) => this.setState({ search: e.target.value })}
                  placeholder="Enter Coupon Name"
                />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    // marginLeft: 5,
                    backgroundColor: "green",
                    padding: 5,
                    color: "#fff",
                    borderRadius: 10,
                  }}
                >
                  <button
                    type="submit"
                    style={{
                      backgroundColor: "transparent",
                      borderWidth: 0,
                      color: "#fff",
                    }}
                  >
                    <i className="bi bi-search"></i>
                  </button>
                </div>
              </form>
            ) : (
              <i
                className="bi bi-search"
                style={{
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                }}
                onClick={() => {
                  this.setState(
                    {
                      search: "",
                      searchClicked: false,
                      isLoading: true,
                    },
                    this.getData
                  );
                }}
              >
                Search Again
              </i>
            )}
          </div>
          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  popUpOpen: !p.popUpOpen,
                  couponName: "",
                  popupType: "",
                  cCount: "",
                  selectedPackages: [],
                  validityDays: "",
                  popupType: "ADD NEW COUPON",
                }));
              }}
            >
              Add New Coupon
            </Button>
          </div>
        </div>
        <table>
          <thead>
            <tr>
              <th>Coupon Name</th>
              <th style={{ textAlign: "center" }}>Percentage Discount</th>
              <th>Groups</th>
              <th style={{ textAlign: "center" }}>Validity (Days)</th>
              <th style={{ textAlign: "center" }}>Action</th>
            </tr>
          </thead>
          <tbody>
            {CouponData.length > 0 ? (
              CouponData.map((e, i) => {
                return (
                  <tr key={"Coupon" + e.coupon_id}>
                    <td>
                      <div style={{ display: "flex" }}>
                        <p>{e.coupon_id + ") " + e.name}</p>
                      </div>
                    </td>

                    <td style={{ textAlign: "center" }}>{e.amount + " %"}</td>
                    <td>{e.groupIds}</td>
                    <td style={{ textAlign: "center" }}>
                      {e.validity_days ? e.validity_days : "No limit"}
                    </td>
                    <td style={{ textAlign: "center" }}>
                      <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 10, cursor: "pointer" }}
                        onClick={() =>
                          this.setState({
                            popUpOpen: true,
                            editorLoading: true,
                            couponId: e.coupon_id,
                            couponName: e.name,
                            cCount: e.amount,
                            validityDays: e.validity_days || "",
                            selectedPackages:
                              e.groupIds !== null
                                ? e.groupIds
                                    .split(",")
                                    .map((e) => e != "" && parseInt(e))
                                : [],
                            popupType: "EDIT COUPON",
                          })
                        }
                      ></i>
                      <i
                        className="bi bi-trash-fill"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          this.setState(
                            {
                              couponId: e.coupon_id,
                            },
                            () => this.deleteCoupon()
                          )
                        }
                      ></i>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: 20,
                }}
              >
                <td colSpan={5}>
                  <p style={{ color: "black", textAlign: "center" }}>
                    No Coupons are Available...
                  </p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {this.renderPaginationButtons(CouponCount)}
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      couponId: "",
      couponName: "",
      popupType: "",
      validityDays: "",
    }));
  };
  onChangePackageData = (e) => {
    console.log(e);
    const { packagesAllList } = this.state;
    const allGids = packagesAllList.map((e) => e.gid).join(",");
    // if (e.target.value === "all") {
    //   this.setState({
    //     selectedPackages: allGids.split(","),
    //   });
    // } else {
    this.setState({
      selectedPackages:
        typeof value === "string" ? e.target.value.split(",") : e.target.value,
    });
    // }
  };

  renderPopUp = () => {
    const {
      popUpOpen,
      couponName,
      popupType,
      packagesAllList,
      selectedPackages,
      cCount,
      validityDays,
    } = this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <TextField
            required
            className="input-box register catgroy"
            label="Coupon Name"
            variant="filled"
            value={couponName}
            onChange={(e) => this.setState({ couponName: e.target.value })}
            focused
          />
          <TextField
            required
            className="input-box register catgroy"
            label="Percentage Discount"
            variant="filled"
            value={cCount}
            onChange={(e) => this.setState({ cCount: e.target.value })}
            focused
          />
          <TextField
            className="input-box register catgroy"
            label="Validity Days (Optional)"
            variant="filled"
            type="number"
            value={validityDays}
            onChange={(e) => this.setState({ validityDays: e.target.value })}
            helperText="Leave empty for no expiration"
            focused
          />
          <Select
            required
            id="Please-Select-District"
            className="input-box dis"
            // label="Please Select Package"
            style={{ width: "96%" }}
            value={selectedPackages}
            name="selectedPackages"
            multiple
            displayEmpty
            onChange={this.onChangePackageData}
            renderValue={(selectedPackagesList) => {
              if (selectedPackagesList.length === 0) {
                return <em>Please Select Package</em>;
              }

              return selectedPackagesList.join(",");
            }}
            MenuProps={MenuProps}
          >
            <MenuItem disabled value="" className="attempt-option-select">
              Please Select Package
            </MenuItem>
            {/* <MenuItem className="selectionbox" value={allGids}>
              <Checkbox
                indeterminate={
                  selectedPackages.length > 0 &&
                  selectedPackages.length < packagesAllList.length
                }
                checked={
                  selectedPackages.length > 0 &&
                  selectedPackages.length === packagesAllList.length
                }
              />
              <ListItemText primary={"Select All"} />
            </MenuItem> */}
            {packagesAllList.map((e, i) => (
              <MenuItem
                className="selectionbox"
                value={e.gid}
                key={"multiselect" + i}
              >
                <Checkbox checked={selectedPackages.indexOf(e.gid) > -1} />
                <ListItemText primary={e.group_name} />
              </MenuItem>
            ))}
          </Select>
        </DialogContent>
        <DialogActions
          style={{ display: "flex", justifyContent: "center", marginLeft: -10 }}
        >
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={
              popupType === "EDIT COUPON" ? this.saveCoupon : this.addCoupon
            }
          >
            Save changes
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  deleteCoupon = async () => {
    const { couponId, CouponData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CouponDelete",
      search: "",
      qid: couponId,
    };
    if (window.confirm("Do you really want to remove this Coupon ?")) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        // console.log(CouponData);
        this.setState(
          {
            CouponData: CouponData.filter((e) => e.coupon_id != couponId),
          },
          NotificationManager.success(`Coupon Deleted Succesfully...`)
        );
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  renderPaginationButtons = (totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    console.log(paidcount);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount - 1}
        >
          Next
        </Button>
      </div>
    );
  };
  saveCoupon = async () => {
    const { couponId, CouponData, couponName, cCount, selectedPackages, validityDays } =
      this.state;
    console.log(selectedPackages);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CouponEdit",
      search: couponName + "%%" + cCount + "%%" + selectedPackages + "%%" + validityDays,
      qid: couponId,
    };
    if (couponName === "") {
      NotificationManager.error(`Please Enter Coupon Name`);
    } else if (couponName.length < 5) {
      NotificationManager.error(`Please Enter Coupon Name with Min. 5 Letters`);
    } else if (cCount === "") {
      NotificationManager.error(`Please Enter % Discount Value`);
    } else if (selectedPackages.length === 0) {
      NotificationManager.error(`Please Select Atleast 1 Package`);
    } else {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        const newCoupon = {
          coupon_id: couponId,
          name: couponName.toUpperCase(),
          amount: cCount,
          groupIds: selectedPackages.join(","),
          validity_days: validityDays,
        };
        // console.log(data.data[0][0].result);
        if (data.data[0][0].result === "success") {
          const index = CouponData.findIndex(
            (ek) => ek == CouponData.filter((e) => e.cid === couponId)[0]
          );

          const removedData = CouponData.filter((e) => e.cid !== couponId);
          const newQUestionsList = removedData.splice(index, 1, newCoupon);
          this.setState(
            {
              popUpOpen: false,
              CouponData: removedData,
            },
            NotificationManager.success(`Coupon Updated Succesfully...`)
          );
        } else {
          NotificationManager.error(`Coupon Name Already Exists..`);
        }
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  addCoupon = async () => {
    const { CouponData, couponName, cCount, selectedPackages, validityDays } = this.state;
    console.log(selectedPackages);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CouponAdd",
      search: couponName + "%%" + cCount + "%%" + selectedPackages + "%%" + validityDays,
      qid: 0,
    };
    if (couponName === "") {
      NotificationManager.error(`Please Enter Coupon Name`);
    } else if (couponName.length < 5) {
      NotificationManager.error(`Please Enter Coupon Name with Min. 5 Letters`);
    } else if (cCount === "") {
      NotificationManager.error(`Please Enter % Discount Value`);
    } else if (selectedPackages.length === 0) {
      NotificationManager.error(`Please Select Atleast 1 Package`);
    } else {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        // console.log(data);
        const newCoupon = {
          coupon_id: data.data[1][0].coupon_id,
          name: couponName,
          amount: cCount,
          groupIds: selectedPackages.join(","),
          validity_days: validityDays,
        };

        if (data.data[0][0].result === "success") {
          let newCoupons = CouponData;
          newCoupons.unshift(newCoupon);
          this.setState(
            {
              popUpOpen: false,
              CouponData: newCoupons,
              couponId: "",
              couponName: "",
              cCount: 0,
              groupIds: [],
              validityDays: "",
            },
            NotificationManager.success(`Coupon Added Succesfully...`)
          );
        } else {
          NotificationManager.error(`Coupon Name Already Exists..`);
        }
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  render() {
    const { isLoading, login, popUpOpen } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.categoriesTable()}
                {popUpOpen && this.renderPopUp()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default Coupons;
