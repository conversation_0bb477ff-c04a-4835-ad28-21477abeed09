import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'build',
    sourcemap: true
  },  define: {
    global: 'globalThis',
    process: JSON.stringify({ env: {} }),
  },  resolve: {
    alias: {
      '@': '/src',
      'react-is': 'react-is/index.js',
      'hoist-non-react-statics': 'hoist-non-react-statics/dist/hoist-non-react-statics.cjs.js'
    },
    dedupe: ['react', 'react-dom', 'prop-types', 'react-is']
  },
  esbuild: {
    loader: 'jsx',
    include: /src\/.*\.js$/,
    exclude: []
  },  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-redux',
      '@reduxjs/toolkit',
      'prop-types',
      'react-router-dom',
      'axios',
      '@material-ui/core',
      'react-toastify',
      'jssha',
      'uuid',
      'js-cookie',
      'react-is',
      'hoist-non-react-statics'
    ],
    exclude: [
      '@material-ui/pickers',
      '@date-io/date-fns'
    ],    esbuildOptions: {
      loader: {
        '.js': 'jsx',
      },
      define: {
        global: 'globalThis',
      },
    },
  },
})
