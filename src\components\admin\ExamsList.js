import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import <PERSON>ie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
// import { Editor } from "@tinymce/tinymce-react";
// import AddNewExam from "./AddNewExam";
// import JSZip from "jszip";
// import Docxtemplater from "docxtemplater";
// import xml_data from "./Q.xml";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import FilesUploadComponent from "./FileUpload";
import ExportCSV, { exportToCSVD } from "./ExportCsv";
import { ExcelRenderer } from "react-excel-renderer";
import ReportedQuestions from "./ReportedQuestions";
import Editor from "./Editor";
import Questions from "./Questions";
import Checkbox from "@mui/material/Checkbox";
import FormControlLabel from "@mui/material/FormControlLabel";
import Box from "@mui/material/Box";

class ExamsList extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    qCount: null,
    examsData: [],
    page: 0,
    popUpOpen: false,
    qNum: null,
    category: null,
    questionText: "",
    op1: "",
    op2: "",
    op3: "",
    op4: "",
    op1Num: 0,
    op2Num: 0,
    op3Num: 0,
    op4Num: 0,
    correctOption: "",
    selectedCat: "",
    categoryList: [],
    editorLoading: false,
    optionsData: [],
    popupType: null,
    search: "",
    examsCount: 0,
    showImportDialog: false,
    selectedQuid: 0,
    exportResultInfo: [],
    isPdfPackage: false,
    importPopUp: false,
    importUsersClicked: false,
    cols: [],
    rows: [],
    excel: [],
    videos: false,
    englishQidsCount: {},
    selectedExams: [],
  };

  componentDidMount() {
    try {
      const { location } = this.props;
      const { search } = location;
      this.setState({
        isPdfPackage: search === "?type=pdfs" || search === "?type=videos",
        videos: search === "?type=videos",
      });
    } catch (error) {}

    this.getData();
  }

  getData = async () => {
    const { page, search } = this.state;
    const { location } = this.props;
    const { search: queryKey } = location;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type:
        queryKey === "?type=pdfs"
          ? "PDFALL"
          : queryKey === "?type=videos"
          ? "videosALL"
          : "examsall",
      search: search,
      videos: search === "?type=videos",

      qid: page * 25,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({
        isLoading: false,
        examsData: data.data[0],
        examsCount: data.data[1][0].count,
        categoryList: data.data[2],
      });
      if (body.type === "examsall") {
        data.data[0].map(({ quid }) => this.getEnglishQuestionsCount(quid));
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  getEnglishQuestionsCount = async (quid) => {
    this.setState((p) => {
      const prev = p.englishQidsCount;
      prev[quid] = "loading";
      return {
        englishQidsCount: prev,
      };
    });
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getEnglishQuestionsCountBasedOnExamId",
      search: "",

      qid: quid,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );

      // englishQidsCount store the state value with count and quid ,its an object,i will call same api with differnt quids so,we need to store all in the same object
      if (data.data.length > 0) {
        this.setState((p) => {
          const prev = p.englishQidsCount;
          prev[quid] = data.data[0][0].count;
          return {
            englishQidsCount: prev,
          };
        });
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  getQuizAttemptsResultData = async (selectedQuid, examName) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "examsresultInfoall",
      search: selectedQuid,
      qid: 0,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({
        isLoading: false,
        // exportResultInfo: data.data[0],
      });
      exportToCSVD(data.data[0], examName + "_Student Results Report");
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  getQuizListData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "examsExportall",
      search: "",
      qid: 0,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({
        isLoading: false,
        // exportResultInfo: data.data[0],
      });
      exportToCSVD(data.data[0], "Exams List");
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  importUsersUpdateDb = async (data) => {
    const {
      ExamID,
      Clone,
      ExamName,
      AssignedtoGroup,
      StartDate,
      EndDate,
      Syllabus,Day
    } = data;
    // console.log(data);

    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "importExcelExams",
      examid: ExamID,
      name: ExamName,
      startdate: StartDate,

      attempts: 0,
      selectedGids: AssignedtoGroup,
      description: Clone,
      selectedqids: Syllabus,
      noq: Day,
      enddate: EndDate,
      minPercent: 0,
      duration: 0,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/add-edit-exam`,
        body,
        { headers }
      );
      return true;
      // console.log(data);
    } catch (err) {
      return false;
    }
  };
  importUsers = async () => {
    const { cols, rows } = this.state;
    // console.log(rows);
    if (rows != undefined) {
      const error = [];
      this.setState({ importUsersClicked: true });
      for (let each of rows) {
        if (each.length > 0) {
          const resposse = await this.importUsersUpdateDb({
            ExamID: each[cols.indexOf("ExamID")],
            Clone: each[cols.indexOf("Clone")]
              ? each[cols.indexOf("Clone")].toLowerCase() === "yes"
                ? "1"
                : "0"
              : "0",
            ExamName: each[cols.indexOf("ExamName")],
            AssignedtoGroup: each[cols.indexOf("AssignedtoGroup")],
            StartDate: each[cols.indexOf("StartDate")],
            EndDate: each[cols.indexOf("EndDate")],
            Syllabus: each[cols.indexOf("Syllabus")],
            Day: each[cols.indexOf("Day")],
          });

          if (!resposse) {
            error.push({
              ExamID: each[cols.indexOf("ExamID")],
              Clone: each[cols.indexOf("Clone")]
                ? each[cols.indexOf("Clone")].toLowerCase() === "yes"
                  ? 1
                  : 0
                : 0,
              ExamName: each[cols.indexOf("ExamName")],
              AssignedtoGroup: each[cols.indexOf("AssignedtoGroup")],
              StartDate: each[cols.indexOf("StartDate")],
              Day: each[cols.indexOf("Day")],
            });
          }
        }
      }

      if (error.length > 0) {
        exportToCSVD(error, "Error Exams List");
      }
      this.getData();
      // console.log(existedUsers, insertedUsers);
      this.setState({
        existedUsers: 0,
        insertedUsers: 0,
        cols: [],
        rows: [],
        importPopUp: false,
        importUsersClicked: false,
      });

      NotificationManager.success(`Data Imported Succesfully...`);
    } else {
      NotificationManager.error(`Please Select valid file to import`);
    }
  };

  importVideos = async () => {
    const { cols, rows } = this.state;
    // console.log(rows);
    if (rows != undefined) {
      const error = [];
      this.setState({ importUsersClicked: true });
      for (let each of rows) {
        if (each.length > 0) {
          const resposse = {
            name: each[cols.indexOf("name")],
            link: each[cols.indexOf("link")],
            day: each[cols.indexOf("day")],
            AssignedtoGroup: each[cols.indexOf("AssignedtoGroup")],
            StartDate: each[cols.indexOf("StartDate")],
            EndDate: each[cols.indexOf("EndDate")],
            Syllabus: each[cols.indexOf("Syllabus")],
            type: each[cols.indexOf("type")],
          };

          const body = {
            type: "Add",
            examid: 0,
            name: resposse.name,
            startdate: resposse.StartDate,

            attempts: 5,
            selectedGids:
              resposse.AssignedtoGroup +
              "&&$" +
              1 +
              "&&$" +
              "1" +
              "&&$" +
              0 +
              "&&$" +
              resposse.day,
            description: resposse.name,
            selectedqids: "",
            noq: 0,
            enddate: resposse.EndDate,
            minPercent: 60,
            duration: resposse.type === "pdf" ? "2" : "3",
          };
          const token = Cookie.get("jwt_token");
          const headers = {
            "Content-Type": "application/json",
            authorization: token,
            "Access-Control-Allow-Origin": "*",
          };
          try {
            await axios.post(`${commonData["api"]}/admin/add-edit-exam`, body, {
              headers,
            });
          } catch (err) {
            NotificationManager.error(`Something Went Wrong`);
            this.setState({ isLoading: false });
          }
        }
      }

      this.getData();
      this.setState({
        existedUsers: 0,
        insertedUsers: 0,
        cols: [],
        rows: [],
        importPopUp: false,
        importUsersClicked: false,
      });

      NotificationManager.success(`Data Imported Succesfully...`);
    } else {
      NotificationManager.error(`Please Select valid file to import`);
    }
  };

  fileHandler = (event) => {
    let fileObj = event.target.files[0];
    //just pass the fileObj as parameter
    // console.log(fileObj.name.split(".xlsx"));
    ExcelRenderer(fileObj, (err, resp) => {
      if (err) {
        console.log(err);
      } else {
        // console.log(resp);
        this.setState({
          cols: resp.rows.slice(0, 1)[0],
          rows: resp.rows.slice(1, resp.rows.length),
        });
      }
    });
  };
  renderImportPopUp = () => {
    const { importPopUp, importUsersClicked, videos } = this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    const sampleData = [
      {
        ExamID: "123",
        Clone: "YES",
        ExamName: "Test new exam",
        AssignedtoGroup: "12,152",
        StartDate: "2023-10-20 09:00:00",
        EndDate: "2023-10-20 09:00:00",
        Syllabus: "Syllabus",
        Day: 1,
      },
    ];
    const videosSamep = [
      {
        name: "Testing",
        link: "https://www.youtube.com/",
        AssignedtoGroup: "12,152",
        StartDate: "2023-10-20 09:00:00",
        EndDate: "2023-10-20 09:00:00",
        day: "1",
        type: "video",
      },
    ];
    return (
      <Dialog
        open={importPopUp}
        onClose={this.handleImportPopup}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Import {videos ? "Videos" : ""}</p>
            </div>
            <Button
              onClick={this.handleImportPopup}
              style={{ color: "#fff", fontSize: 20 }}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <div
            style={{
              marginTop: "20px",
              padding: 20,
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
            }}
          >
            <input
              type="file"
              onChange={this.fileHandler}
              style={{ padding: "10px" }}
            />
            <Button
              className="btn"
              onClick={videos ? this.importVideos : this.importUsers}
              style={{ color: "#fff" }}
              disabled={importUsersClicked}
            >
              Import Now
            </Button>
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
              marginTop: 10,
            }}
          >
            <ExportCSV
              csvData={videos ? videosSamep : sampleData}
              fileName={"Download Sample Import File"}
              title={"Download Sample Import File"}
              emptyFn={() => {}}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  handleImportPopup = () => {
    this.setState((p) => ({
      selectedPackages: [],
      importPopUp: !p.importPopUp,
      rows: [],
      excel: [],
      importUsersClicked: false,
      popUpOpen: false,
    }));
  };
  deleteQuestion = async () => {
    const { qNum, examsData, isPdfPackage } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "DELETEEXAM",
      search: "",
      qid: qNum,
    };
    if (window.confirm("Do you really want to remove entry?")) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        //   console.log(data);
        this.setState({
          examsData: examsData.filter((e) => e.quid !== qNum),
        });
        NotificationManager.success(
          `${isPdfPackage ? "PDF/Video" : "Exam "} Deleted Succesfully...`
        );
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };
  getOptions = async () => {
    const { qNum, examsData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getOptions",
      search: "",
      qid: qNum,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );

      this.setState({
        optionsData: data.data[0],
        editorLoading: false,
        correctOption: data.data[1][0].selectedOp,
        selectedCat: data.data[2][0].selectedCategory,
        op1: data.data[0][0].q_option,
        op2: data.data[0][1].q_option,
        op3: data.data[0][2].q_option,
        op4: data.data[0][3].q_option,
        op1Num: data.data[0][0].oid,
        op2Num: data.data[0][1].oid,
        op3Num: data.data[0][2].oid,
        op4Num: data.data[0][3].oid,
        questionText: data.data[2][0].question,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  searchUsers = () => {
    const { search } = this.state;
    console.log(search);
    if (search === 0) {
      NotificationManager.error(`Please Select Category`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
      });
      this.getData();
    }
  };
  renderPaginationButtons = (type, totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    // console.log(paidrows);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount}
        >
          Next
        </Button>
      </div>
    );
  };

  handleSelectExam = (quid) => {
    this.setState((prevState) => ({
      selectedExams: prevState.selectedExams.includes(quid)
        ? prevState.selectedExams.filter((id) => id !== quid)
        : [...prevState.selectedExams, quid],
    }));
  };

  handleSelectAll = (event) => {
    this.setState({
      selectedExams: event.target.checked
        ? this.state.examsData.map((exam) => exam.quid)
        : [],
    });
  };

  downloadBulkDocuments = async (type) => {
    const { selectedExams } = this.state;
    if (selectedExams.length === 0) {
      NotificationManager.warning("Please select at least one exam");
      return;
    }

    NotificationManager.info(`Processing ${selectedExams.length} exams...`);

    try {
      if (type === "teluguExamQuestions") {
        // Pass all selected exam IDs as a comma-separated string
        await this.downloadTeluguQuestions(selectedExams);
      } else {
        await this.downloadDocxFile(selectedExams.toString(), type);
      }
    } catch (error) {
      NotificationManager.error("Failed to process bulk download");
    }
  };

  examsTable = () => {
    const {
      examsData,
      searchClicked,
      categoryList,
      examsCount,
      isPdfPackage,
      videos,
      selectedExams,
    } = this.state;
    // console.log(examsData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height:auto;
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv" style={{ overflowX: "scroll" }}>
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All {videos ? "Videos" : isPdfPackage ? "PDF" : "Exams"}</h3>

          {/* Add Bulk Action Buttons */}
          {!isPdfPackage && selectedExams.length > 0 && (
            <Box sx={{ display: "flex", gap: 2, mb: 2 }}>
              <Button
                className="btn exportbtn"
                onClick={() =>
                  this.downloadBulkDocuments("englishExamQuestions")
                }
                sx={{ bgcolor: "#4caf50" }}
              >
                Download BLQ ({selectedExams.length})
              </Button>
              <Button
                className="btn exportbtn"
                onClick={() =>
                  this.downloadBulkDocuments("teluguExamQuestions")
                }
                sx={{ bgcolor: "#2196f3" }}
              >
                Download TM ({selectedExams.length})
              </Button>
            </Box>
          )}

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            {!searchClicked ? (
              <select
                style={{
                  width: "230px",
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                }}
                onChange={(e) =>
                  this.setState({ search: e.target.value }, this.searchUsers)
                }
              >
                <option value={0} selected>
                  {`Filter ${
                    videos ? "Video" : isPdfPackage ? "PDF" : "Exams"
                  } By Package`}
                </option>
                {categoryList.map((eachCateg) => (
                  <option value={eachCateg.gid}>{eachCateg.group_name}</option>
                ))}
              </select>
            ) : (
              <i
                className="bi bi-search"
                style={{
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                }}
                onClick={() => {
                  this.setState(
                    {
                      search: "",
                      searchClicked: false,
                      isLoading: true,
                      hidePagination: false,
                    },
                    this.getData
                  );
                }}
              >
                Search Again
              </i>
            )}
          </div>
          <div>
            <Link
              to={
                "/admin/exams/add-edit-exam/0" +
                (videos ? "?type=videos" : isPdfPackage ? "?type=pdfs" : "")
              }
              className="linkto"
            >
              <Button className="btn exportbtn">
                Add New {videos ? "Video" : isPdfPackage ? "PDF" : "Exam "}
              </Button>
            </Link>
          </div>
          {!isPdfPackage && (
            <div>
              <Button
                className="btn exportbtn"
                onClick={() =>
                  this.setState({
                    showImportDialog: true,
                    importAction: "Import Exams",
                  })
                }
                style={{ marginRight: 30 }}
              >
                Import Exam
              </Button>
              <Button
                className="btn exportbtn"
                onClick={() =>
                  this.setState({
                    showImportDialog: true,
                    importAction: "Add Qids to Exams",
                  })
                }
                style={{ marginRight: 30 }}
              >
                Add Qids to Exams
              </Button>
              <Button
                className="btn exportbtn"
                onClick={this.getQuizListData}
                style={{ marginRight: 30, background: "green" }}
              >
                Export all exams
              </Button>
              <Button
                className="btn exportbtn"
                onClick={this.handleImportPopup}
                style={{ background: "green" }}
              >
                Import Excel to Clone
              </Button>
              <a href="/todays-exams" target="__blank">
                <Button
                  className="btn exportbtn"
                  style={{ background: "green", marginLeft: 20 }}
                >
                  Today Exams
                </Button>
              </a>
            </div>
          )}
          {videos && (
            <Button
              className="btn exportbtn"
              onClick={this.handleImportPopup}
              style={{ background: "green" }}
            >
              Import Videos - Excel
            </Button>
          )}
        </div>
        <table>
          <thead>
            <tr>
              <th>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={selectedExams.length === examsData.length}
                      indeterminate={
                        selectedExams.length > 0 &&
                        selectedExams.length < examsData.length
                      }
                      onChange={this.handleSelectAll}
                    />
                  }
                  label="Select All"
                />
              </th>
              <th>{videos ? "Video" : isPdfPackage ? "PDF" : "Exam "} Name</th>
              {!isPdfPackage && <th>No.Of Attempts</th>}
              <th>Action</th>
              {!isPdfPackage && <th>EM/Bits</th>}
              <th>Day</th>
              {/* <th>2nd Attempts</th> */}
            </tr>
          </thead>
          <tbody>
            {examsData.map((e, i) => {
              return (
                <tr key={"reported" + e.quid}>
                  <td>
                    <Checkbox
                      checked={selectedExams.includes(e.quid)}
                      onChange={() => this.handleSelectExam(e.quid)}
                    />
                  </td>
                  <td
                    style={{
                      width: "50%",
                      border:
                        e.added_by !== localStorage.getItem("num")
                          ? "1px solid red"
                          : "",
                    }}
                  >
                    <div style={{ display: "flex" }}>
                      <div
                        style={{
                          display: "flex",
                          marginRight: 15,
                          cursor: "pointer",
                        }}
                        onClick={() => {
                          // navigator.clipboard.writeText(
                          //   `${commonData['app']}/exam-details/${window.btoa(
                          //     e.quid + "$$_" + window.atob(bucketId))}`
                          // );
                          // NotificationManager.success("Copied to ClipBoard");
                        }}
                      >
                        {e.quid + ") "}
                        <span
                          id={e.quid + "examName"}
                          style={{
                            marginLeft: 5,
                            height: 60,
                            overflow: "auto",
                          }}
                          dangerouslySetInnerHTML={{ __html: e.quiz_name }}
                        ></span>
                        {/* <i
                          className="bi bi-clipboard"
                          style={{ cursor: "pointer", marginLeft: 10 }}

                        ></i> */}
                      </div>
                      {/* {e.with_login === "0" && (
                        <i
                          class="bi bi-clipboard"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `${commonData["app"]}/exam-details/${e.quid}`
                            );
                            NotificationManager.success(
                              "Exam Link Copied to ClipBoard"
                            );
                          }}
                          style={{ cursor: "pointer", marginLeft: 15 }}
                        >
                          {" "}
                          Copy Exam Link
                        </i>
                      )} */}
                    </div>
                    {/* <div style={{ display: "flex" }}>
                      <p className="detailsExams">
                        Duration : {e.duration} Min.
                      </p>
                      <p className="detailsExams">
                        Attempts : {e.maximum_attempts}
                      </p>
                      <p className="detailsExams">
                        No. of Questions : {e.qids.split(",").length}
                      </p>
                    </div> */}
                  </td>
                  {!isPdfPackage && (
                    <td
                      style={{ cursor: e.firstcount > 0 ? "pointer" : "" }}
                      onClick={() => {
                        if (e.firstcount > 0)
                          this.getQuizAttemptsResultData(e.quid, e.quid);
                      }}
                    >
                      <p style={{ textAlign: "center" }}>{e.firstcount}</p>
                    </td>
                  )}
                  <td
                    style={{
                      flexDirection: "row",
                      display: "flex",
                      flexWrap: "wrap",
                      gap: 3,
                      justifyContent: "start",
                      alignItems: "center",
                    }}
                  >
                    <Link
                      to={`/admin/exams/add-edit-exam/${e.quid}${
                        videos
                          ? "?type=videos"
                          : isPdfPackage
                          ? "?type=pdfs"
                          : ""
                      }`}
                      className="linkto"
                    >
                      <i
                        className="bi bi-pencil-fill"
                        style={{
                          marginRight: 15,
                          cursor: "pointer",
                          border: "2px solid orange",
                          padding: 5,
                          color: "black",
                        }}
                      ></i>
                    </Link>
                    <i
                      className="bi bi-trash-fill"
                      style={{
                        cursor: "pointer",
                        border: "2px solid orange",
                        padding: 5,
                      }}
                      onClick={() =>
                        this.setState(
                          {
                            qNum: e.quid,
                          },
                          () => this.deleteQuestion()
                        )
                      }
                    ></i>
                    {!isPdfPackage && (
                      <>
                        <i
                          className="bi bi-download"
                          style={{
                            cursor: "pointer",
                            // marginTop: 15,
                            marginLeft: 15,
                            border: "2px solid orange",
                            padding: 5,
                          }}
                          onClick={() => this.downloadDocxFile(e.quid)}
                        >
                          {" "}
                          Q
                        </i>{" "}
                        <i
                          className="bi bi-download"
                          style={{
                            cursor: "pointer",
                            // marginTop: 15,
                            marginLeft: 15,
                            border: "2px solid orange",
                            padding: 5,
                          }}
                          onClick={() =>
                            this.downloadDocxFile(
                              e.quid,
                              "englishExamQuestions"
                            )
                          }
                        >
                          {" "}
                          BL Q
                        </i>{" "}
                        <i
                          className="bi bi-download"
                          style={{
                            cursor: "pointer",
                            // marginTop: 15,
                            marginLeft: 15,
                            border: "2px solid orange",
                            padding: 5,
                          }}
                          onClick={() =>
                            this.downloadDocxFile(e.quid, "allImageQuestions")
                          }
                        >
                          Lengthy Q
                        </i>
                        <a
                          href={`${commonData["api"]}/download-pdf/ranksheet/${e.quid}/${e.quid}_Ranksheet`}
                          className="linkto"
                          target="_blank"
                        >
                          <i
                            className="bi bi-download"
                            style={{
                              cursor: "pointer",
                              // marginTop: 15,
                              marginLeft: 15,
                              border: "2px solid orange",
                              padding: 5,
                            }}
                          >
                            {" "}
                            R
                          </i>
                        </a>
                        <div style={{ marginTop: 20 }}>
                          <i
                            className="bi bi-clipboard"
                            style={{
                              cursor: "pointer",
                              paddingTop: 30,
                              paddingLeft: 15,
                              border: "2px solid orange",
                              padding: 5,
                            }}
                            onClick={() =>
                              this.setState(
                                {
                                  qNum: e.quid,
                                },
                                this.cloneExam
                              )
                            }
                          ></i>{" "}
                          <i
                            className="bi bi-download"
                            style={{
                              cursor: "pointer",
                              paddingTop: 30,
                              paddingLeft: 15,
                              marginLeft: 20,
                              border: "2px solid orange",
                              padding: 5,
                            }}
                            onClick={() =>
                              this.downloadTeluguQuestions([e.quid])
                            }
                          >
                            TM
                          </i>
                          {e.is_single_paid_exam === "1" && (
                            <i
                              className="bi bi-download"
                              style={{
                                cursor: "pointer",
                                paddingTop: 30,
                                paddingLeft: 15,
                                marginLeft: 20,
                                border: "2px solid orange",
                                padding: 5,
                              }}
                              onClick={() => {
                                navigator.clipboard.writeText(
                                  `${commonData["app"]}/exam-link/${e.quid}`
                                );
                                NotificationManager.success(
                                  "Copied to ClipBoard"
                                );
                              }}
                            >
                              ELC
                            </i>
                          )}
                        </div>
                      </>
                    )}
                  </td>

                  {!isPdfPackage && (
                    <td>
                      <p style={{ textAlign: "center" }}>
                        {this.state.englishQidsCount[e.quid] === "loading" ? (
                          <i
                            className="bi bi-arrow-clockwise rotate"
                            style={{ color: "blue" }}
                          ></i>
                        ) : (
                          this.state.englishQidsCount[e.quid]
                        )}
                        /{e.noq}
                      </p>
                    </td>
                  )}
                  <td>
                    <p style={{ textAlign: "center" }}>{e.day}</p>
                  </td>

                  {/* {this.state.exportResultInfo.length > 0 &&
                    !this.state.isLoading && (
                      <ExportCSV
                        csvData={this.state.exportResultInfo}
                        fileName={" Export Result Data " + new Date()}
                        title={"Export Result"}
                        type={"ExportAllGidUsers"}
                        emptyFn={() => {
                          this.setState({ exportResultInfo: [] });
                        }}
                      />
                    )} */}
                </tr>
              );
            })}
          </tbody>
        </table>
        {this.renderPaginationButtons("paid", examsCount)}
      </div>
    );
  };

  cloneExam = async () => {
    const { qNum } = this.state;
    const { history } = this.props;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CLONEEXAM",
      search: "",
      qid: qNum,
    };
    // if (window.confirm("Do you really want to Clone this Exam?")) {
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data);
      const newTab = window.open("", "_blank");
      newTab.location.href = `/admin/exams/add-edit-exam/${data.data[0][0].newQuid}`;
      // history.push();
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
    // }
  };
  downloadTeluguQuestions = async (selectedQuid) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    try {
      NotificationManager.info("Processing, please wait...");
      let allQuestions = [];

      // Split the comma-separated quiz IDs
      const quizIds = selectedQuid;

      // Fetch data for each quiz ID
      for (const qid of quizIds) {
        const body = {
          type: "downloadTeluguQuestionsFromExam",
          search: qid,
          qid: 0,
        };

        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );

        // Get exam name from your examsData state
        const examName =
          this.state.examsData.find((exam) => exam.quid === parseInt(qid))
            ?.quiz_name || qid;

        // Add exam name to each question
        const questionsWithExamName = data.data[0].map((question) => ({
          ...question,
          exam_name: examName,
        }));

        // Accumulate questions
        allQuestions = [...allQuestions, ...questionsWithExamName];
      }

      // Sort questions by exam name if needed
      allQuestions.sort((a, b) => a.exam_name.localeCompare(b.exam_name));

      // Download combined data
      if (allQuestions.length > 0) {
        const filename =
          quizIds.length > 1
            ? `Multiple_Exams_Questions_${new Date()
                .toISOString()
                .slice(0, 10)}`
            : `${selectedQuid}_Questions`;

        exportToCSVD(allQuestions, filename);
        NotificationManager.success(
          `Successfully downloaded ${allQuestions.length} questions`
        );
      } else {
        NotificationManager.warning("No questions found");
      }
    } catch (err) {
      console.error("Download error:", err);
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      questionText: "",
      op1: "",
      op2: "",
      op3: "",
      op4: "",
      correctOption: null,
      selectedCat: "",
    }));
  };

  downloadDocxFile = async (examId, type = "examQuestions") => {
    // let examName = document?.getElementById(examId + "examName")?.innerText;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    try {
      NotificationManager.success("Please wait...");
      const response = await axios.get(
        `${commonData["api"]}/download-questions/${type}/${examId}`,
        { headers, responseType: "blob" } // Set responseType to 'blob'
      );

      // Get the content disposition header to extract the filename
      const fileName = "Exam_Questions_" + examId + ".docx";

      // Create a blob URL and trigger a download link
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });
      const blobUrl = URL.createObjectURL(blob);
      const downloadLink = document.createElement("a");
      downloadLink.href = blobUrl;
      downloadLink.download = fileName;
      downloadLink.click();

      // Clean up the blob URL
      URL.revokeObjectURL(blobUrl);
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };
  questionEditor = () => {
    const {
      qNum,
      examsData,
      editorLoading,
      optionsData,
      categoryList,
      selectedCat,
      correctOption,
      popupType,
    } = this.state;
    const question = examsData.filter((e) => e.questionid === qNum)[0];
    // this.setState({ selectedCat: question.cid });
    // console.log(popupType);
    return (
      <>
        {popupType === "Edit Exam" &&
        !editorLoading &&
        question !== undefined &&
        categoryList !== undefined ? (
          <>
            <div>
              Select Category{" "}
              <select
                onChange={(e) => this.setState({ selectedCat: e.target.value })}
              >
                {categoryList.map((category) => {
                  return (
                    <option
                      key={"category" + category.cid}
                      value={category.cid}
                      selected={
                        selectedCat === category.cid ||
                        question.cid === category.cid
                      }
                    >
                      {category.category_name}
                    </option>
                  );
                })}
              </select>
            </div>
            <div>
              Exam
              <Editor
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={question.question}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(e) => this.setState({ questionText: e })}
              />
            </div>

            {optionsData.map((e, i) => (
              <div style={{ marginTop: 15 }} key={"examslisteditor" + i}>
                Option {i + 1}{" "}
                <input
                  type={"radio"}
                  value={e.oid}
                  name={qNum}
                  id={e.oid}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value })
                  }
                  checked={correctOption == e.oid}
                />{" "}
                <label htmlFor={e.oid}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={e.oid}
                  initialValue={e.q_option}
                  init={{
                    height: 100,
                    width: "90%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      ["op" + (i + 1)]: value,
                      ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
            ))}
          </>
        ) : (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };

  AddquestionEditor = () => {
    const {
      categoryList,
      popupType,
      editorLoading,
      correctOption,
      questionText,
      op1,
      op2,
      op3,
      op4,
      selectedCat,
    } = this.state;

    return (
      <>
        {popupType === "Add New Exam" &&
        !editorLoading &&
        categoryList !== undefined ? (
          <>
            <div>
              Select Category{" "}
              <select
                onChange={(e) => this.setState({ selectedCat: e.target.value })}
              >
                <option value={null} selected={selectedCat === null}>
                  Please select category
                </option>
                {categoryList.map((category) => {
                  return (
                    <option
                      key={"category" + category.cid}
                      value={category.cid}
                      selected={selectedCat === category.cid}
                      required
                    >
                      {category.category_name}
                    </option>
                  );
                })}
              </select>
            </div>
            <div>
              Exam
              <Editor
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={""}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(e) => this.setState({ questionText: e })}
              />
            </div>
            <div style={{ display: "flex", flexWrap: "wrap" }}>
              <div style={{ marginTop: 15 }}>
                Option {1}{" "}
                <input
                  type={"radio"}
                  value={op1}
                  name={"qNum"}
                  id={"qNum" + "op1"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op1Num: 1 })
                  }
                  checked={correctOption == op1}
                />{" "}
                <label htmlFor={"qNum" + "op1"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop1"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      ["op" + 1]: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              <div style={{ marginTop: 15 }}>
                Option {2}{" "}
                <input
                  type={"radio"}
                  value={op2}
                  name={"qNum"}
                  id={"qNum" + "op2"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op2Num: 1 })
                  }
                  checked={correctOption == op2}
                />{" "}
                <label htmlFor={"qNum" + "op2"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop2"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      op2: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              <div style={{ marginTop: 15 }}>
                Option {3}{" "}
                <input
                  type={"radio"}
                  value={op3}
                  name={"qNum"}
                  id={"qNum" + "op3"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op3Num: 1 })
                  }
                  checked={correctOption == op3}
                />{" "}
                <label htmlFor={"qNum" + "op3"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop3"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      op3: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              <div style={{ marginTop: 15 }}>
                Option {4}{" "}
                <input
                  type={"radio"}
                  value={op4}
                  name={"qNum"}
                  id={"qNum" + "op4"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op1Num: 1 })
                  }
                  checked={correctOption == op4}
                />{" "}
                <label htmlFor={"qNum" + "op4"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop4"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      op4: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
            </div>
          </>
        ) : (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };

  saveQuestion = async () => {
    const {
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      examsData,
    } = this.state;
    let { questionText } = this.state;

    // console.log(questionText, op1, op2, op3, op4, selectedCat, correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      type: "EDITQuestion",
    };
    // console.log(body);
    try {
      this.setState({ editorLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/add-edit-question`,
        body,
        { headers }
      );
      //   console.log(data);
      const newQuestion = {
        cid: selectedCat,
        question: questionText,
        questionid: qNum,
      };
      const index = examsData.findIndex(
        (ek) => ek == examsData.filter((e) => e.questionid === qNum)[0]
      );

      const removedData = examsData.filter((e) => e.questionid !== qNum);
      const newQUestionsList = removedData.splice(index, 1, newQuestion);
      this.setState({
        editorLoading: false,
        popUpOpen: false,
        questionText: "",
        op1: "",
        op2: "",
        op3: "",
        op4: "",
        op1Num: 0,
        op2Num: 0,
        op3Num: 0,
        op4Num: 0,
        correctOption: null,
        selectedCat: null,
        examsData: removedData,
      });
      NotificationManager.success(`Exam Updated Successfully..`);
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ editorLoading: false });
    }
  };

  addNewQuestion = async () => {
    const {
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
    } = this.state;
    let { questionText } = this.state;

    // console.log(questionText, op1, op2, op3, op4, selectedCat, correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption: 554,
      qNum,
      quizId,
      type: "AddQuestion",
    };
    // console.log(body);
    if (questionText === "") {
      NotificationManager.error(`Please Enter Exam`);
    } else if (op1 === "") {
      NotificationManager.error(`Please Enter Option 1`);
    } else if (op2 === "") {
      NotificationManager.error(`Please Enter Option 2`);
    } else if (op3 === "") {
      NotificationManager.error(`Please Enter Option 3`);
    } else if (op4 === "") {
      NotificationManager.error(`Please Enter Option 4`);
    } else if (op1Num === 0 && op2Num === 0 && op3Num === 0 && op4Num === 0) {
      NotificationManager.error(`Please Select Correct Answer`);
    } else if (selectedCat === null) {
      NotificationManager.error(`Please Select Category`);
    } else {
      try {
        this.setState({ editorLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-question`,
          body,
          { headers }
        );
        // console.log(data.data[0]);
        const newQuestion = {
          cid: selectedCat,
          question: questionText,
          questionid: data.data[0][0].questionId,
        };
        this.setState((prev) => ({
          editorLoading: false,
          popUpOpen: false,
          examsData: [newQuestion, ...prev.examsData],
          questionText: "",
          op1: "",
          op2: "",
          op3: "",
          op4: "",
          op1Num: 0,
          op2Num: 0,
          op3Num: 0,
          op4Num: 0,
          correctOption: null,
          selectedCat: null,
        }));
        NotificationManager.success(`Exam Added Successfully..`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ editorLoading: false });
      }
    }
  };
  renderPopUp = () => {
    const { popUpOpen, popupType } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"lg"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={this.handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {popupType !== "Add New Exam"
            ? this.questionEditor()
            : this.AddquestionEditor()}
        </DialogContent>
        <DialogActions>
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={
              popupType !== "Add New Exam"
                ? this.saveQuestion
                : this.addNewQuestion
            }
          >
            Save changes
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  renderImportDialog = () => {
    const { showImportDialog, importAction } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={showImportDialog}
        onClose={() =>
          this.setState({ showImportDialog: false, importAction: "" })
        }
        maxWidth={"sm"}
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{importAction}</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={() =>
                this.setState({ showImportDialog: false, importAction: "" })
              }
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent
          className="dailogContent"
          style={{ height: 150, paddingTop: 50 }}
        >
          <FilesUploadComponent
            refresh={() => {
              if (importAction === "Import Exams") {
                this.getData();
              }
            }}
            loading={(da) =>
              this.setState({
                isLoading: da,
                showImportDialog: da,
                importAction: "",
              })
            }
            type={importAction}
          />
          <div
            style={{ display: "flex", flexDirection: "column", marginTop: 20 }}
          >
            <a
              href="https://docs.google.com/document/d/141fO9OKVD8MMPGglNdtzgdherv0R85wSRsnmlFMHcgs/edit?usp=sharing"
              target={"_blank"}
            >
              Demo File - Import Exams+Questions
            </a>
            <a
              href="https://docs.google.com/document/d/10LkxUpMvGVU0q_AkoeCbpY-jpbCRm7EH/edit?usp=sharing&ouid=112927756030263083484&rtpof=true&sd=true"
              target={"_blank"}
            >
              Demo File - Add Qids to Exams
            </a>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  render() {
    const { isLoading, login, popUpOpen, isPdfPackage } = this.state;
    // console.log(this.state.englishQidsCount);

    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />

              <div
                className="viewresultsdesktop admin"
                style={{
                  display: "flex",
                  flexDirection: "column",
                  height: "100%",
                }}
              >
                {!isPdfPackage && <ReportedQuestions from="examslist" />}
                {this.examsTable()}
                {!isPdfPackage && <Questions from="examslist" />}
                {popUpOpen && this.renderPopUp()}
                {this.renderImportDialog()}
                {this.renderImportPopUp()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default ExamsList;
