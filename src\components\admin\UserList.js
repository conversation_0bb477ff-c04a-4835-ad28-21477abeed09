import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import ExportCSV, { exportToCSVD } from "./ExportCsv";
import { ExcelRenderer } from "react-excel-renderer";
import Checkbox from "@mui/material/Checkbox";
import TextField from "@mui/material/TextField";
import ListItemText from "@mui/material/ListItemText";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import { exportToCSVFile } from "./csvFileDownload";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
let progressvalue = 0;
class UserList extends react.Component {
  state = {
    isLoading: true,
    login: "invalid",
    packagesAllList: [],
    selected: {},
    popupType: "",
    popUpOpen: false,
    name: "",
    surname: "",
    district: "",
    userType: "0",
    dob: "",
    email: "",
    phoneNum: "",
    selectedPackages: [],
    paidPageNum: 0,
    paidUsers: [],
    editId: "",
    showHide: false,
    pageNum: 0,
    search: "",
    packageUserName: "",
    searchClicked: false,
    hidePagination: false,
    excelDataSelected: [],
    fileName: "Users Export " + new Date(),
    selectedIds: [],
    importPopUp: false,
    importUsersClicked: false,
    insertedUsers: 0,
    existedUsers: 0,
    todaysPop: false,
    todaySelected: "1",
    todayData: [],
    istodayLoading: false,
    exportdatapopup: false,
    allUsersData: [],
    userId: null,
    expiryDate: "",
    packageEditClicked: false,
    selectedPackageUserId: "",
    particularUserPackages: [],
    amount: "",
    userPackLoading: false,
    examsSelectionMaster: [],
    GETALLCOUNT: [
      {
        new: 0,
        all: 0,
      },
    ],
    downloadType: "",
    importCount: 0,
    totalCountImport: 0,
    isImporting: false,
    sendMessage: true, // Add this state variable
  };

  componentDidMount() {
    const { location } = this.props;
    if (location?.search.includes("?userNum")) {
      const userMobile = location?.search.split("?userNum=91")[1];
      this.setState({ search: userMobile }, this.searchUsers);
      return;
    }
    console.log(
      "🚀 ~ file: UserList.js:101 ~ UserList ~ componentDidMount ~ locatiion:",
      this.props
    );
    this.getData();
  }

  emptySelectedImport = async () => {
    const { excelDataSelected, selectedIds } = this.state;
    // console.log(excelDataSelected, selectedIds);
    // this.setState(
    //   { excelDataSelected: [], selectedIds: [] },
    //   console.log(excelDataSelected, selectedIds)
    // );
  };
  getData = async () => {
    const { pageNum, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    const body = {
      type: "GETALLUSERS",
      search,
      qid: pageNum * 25,
    };
    const body2 = {
      type: "GETALLEXAMSSELECTION",
      search,
      qid: 0,
    };
    const body3 = {
      type: "GETALLCOUNT",
      search,
      qid: 0,
    };

    this.setState({ isLoading: true });

    try {
      // Fetch GETALLUSERS first
      const userList = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );

      // Update state with GETALLUSERS data
      this.setState(
        {
          isLoading: false,
          paidUsers: userList.data[0],
          usersCount: userList.data[1][0]?.count || 0,
          login: "valid",
        },
        () => {
          try {
            const { location } = this.props;
            if (location?.search.includes("?userNum")) {
              document?.getElementById("edit_user_" + search).click();
            }
          } catch (error) {}
        }
      );

      // Fetch remaining data in parallel
      this.fetchRemainingData(headers, body2, body3);
    } catch (err) {
      console.error(err);
      this.setState({
        isLoading: false,
        login: "invalid",
      });
    }
  };

  // Separate function to fetch remaining data
  fetchRemainingData = async (headers, body2, body3) => {
    try {
      const [examsSelectionMaster, GETALLCOUNT, packagesList] =
        await Promise.all([
          axios.post(`${commonData["api"]}/admin/qbankdata`, body2, {
            headers,
          }),
          axios.post(`${commonData["api"]}/admin/qbankdata`, body3, {
            headers,
          }),
          axios.get(`${commonData["api"]}/adminmasterdata/packagelist`, {
            headers,
          }),
        ]);

      // Update state with remaining data
      this.setState({
        examsSelectionMaster: examsSelectionMaster.data[0],
        GETALLCOUNT: GETALLCOUNT.data[0][0] || {},
        packagesAllList: packagesList.data[0],
      });
    } catch (err) {
      console.error(err);
    }
  };

  deleteUser = async () => {
    const { userno, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "userDelete",
      search: "",
      qid: userno,
    };
    if (window.confirm("Do you really want to remove entry?")) {
      try {
        const userList = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          {
            headers,
          }
        );
        // console.log(packagesList);
        NotificationManager.success(`User Deleted Succesfully...`);

        this.setState((prev) => ({
          paidUsers: prev.paidUsers.filter((ee) => ee.contact_no !== userno),
        }));
      } catch (err) {
        // console.log(err);
        this.setState({
          isLoading: false,
          login: "invalid",
        });
      }
    }
  };

  blockOrUnblockUser = async (status) => {
    const { userno } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "userBlock",
      search: status,
      qid: userno,
    };
    const isConfirm = window.confirm("Do you really update the user?");
    if (isConfirm) {
      try {
        await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
          headers,
        });
        NotificationManager.success(`User Status updated Succesfully...`);
        this.getData();
      } catch (err) {
        // console.log(err);
        this.setState({
          isLoading: false,
          login: "invalid",
        });
      }
    }
  };

  changeUserMedium = async (status) => {
    const { userno } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "userMedium",
      search: status,
      qid: userno,
    };
    const isConfirm = window.confirm("Do you really update the user?");
    if (isConfirm) {
      try {
        await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
          headers,
        });
        NotificationManager.success(`User Status updated Succesfully...`);
        this.getData();
      } catch (err) {
        // console.log(err);
        this.setState({
          isLoading: false,
          login: "invalid",
        });
      }
    }
  };

  sendMessagetoUser = async (status) => {
    const { phoneNum } = this.state;

    try {
      axios.post(
        `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
        {
          businessId: 1,
          verifyToken: Math.random() * 15000,
          phoneNumber: phoneNum,
          message: [],
          messageType: "promotion",
          templateLang: "te",
          templateName: "payment_user_status",
        }
      );
      NotificationManager.success(`User Status updated Succesfully...`);
    } catch (err) {}
  };

  getTodayData = async () => {
    const { todaySelected } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "gettodaysdata",
      search: "",
      qid: todaySelected,
    };

    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        {
          headers,
        }
      );
      // console.log(packagesList);

      this.setState((prev) => ({
        todayData: data.data[0],
        istodayLoading: false,
      }));
    } catch (err) {
      // console.log(err);
      this.setState({
        isLoading: false,
        login: "invalid",
      });
    }
  };
  updateUserInfo = async (e) => {
    const {
      name,
      surname,
      phoneNum,
      email,
      district,
      dob,
      selectedPackages,
      userType,
      popupType,
    } = this.state;

    e.preventDefault();
    const registrationData = {
      name,
      surname,
      email,
      district,
      dob,
      number: phoneNum,
      type: popupType === "Add New User" ? "CREATE" : "EDIT",
      gidselected: selectedPackages.join(","),
      su: userType,
    };
    console.log(registrationData);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.post(
        `${commonData["api"]}/admin/create-new-user`,
        registrationData,
        { headers }
      );
      // console.log(data.data[0][0].result);
      if (data.data[0][0].result === "yes") {
        if (this.state.sendMessage) {
          this.sendMessagetoUser();
        }
        this.getData();
        this.setState((pp) => ({
          isLoading: false,
          popUpOpen: false,
          name: "",
          email: "",
          dob: null,
          phoneNum: "",
          district: "",
          userType: "0",
          surname: "",
          selectedPackages: [],
          sendMessage: true,
        }));

        NotificationManager.success(`${name} Data updated Succesfully...`);
      } else {
        NotificationManager.error(`Student Already Exists..`);

        this.setState((pp) => ({
          isLoading: false,
        }));
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  renderPaginationButtons = (type, totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { pageNum } = this.state;
    console.log(pageNum, paidcount);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ pageNum: prev.pageNum - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={pageNum === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ pageNum: prev.pageNum + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={pageNum === paidcount}
        >
          Next
        </Button>
      </div>
    );
  };

  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      name: "",
      email: "",
      dob: null,
      phoneNum: "",
      district: "",
      userType: "0",
      surname: "",
    }));
  };

  handleexportdatapopup = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      importUsersClicked: false,
      popUpOpen: false,
      importPopUp: false,
      exportdatapopup: false,
      allUsersData: [],
    }));
  };

  handleImportPopup = () => {
    this.setState((p) => ({
      selectedPackages: [],
      importPopUp: !p.importPopUp,
      rows: [],
      excel: [],
      importUsersClicked: false,
      popUpOpen: false,
    }));
  };

  handlepoptoday = () => {
    this.setState((p) => ({
      selectedPackages: [],
      importPopUp: false,
      rows: [],
      excel: [],
      importUsersClicked: false,
      popUpOpen: false,
      todaysPop: false,
      todayData: [],
      todaySelected: 1,
    }));
  };

  searchUsers = (e) => {
    e?.preventDefault();
    const { search } = this.state;

    if (search === "") {
      NotificationManager.error(`Please Enter Search Value`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
        hidePagination: true,
      });
      this.getData();
    }
  };

  addToexportExcel = (e, value, type) => {
    const { checked } = e.target;
    // console.log(checked);
    const { excelDataSelected, selectedIds } = this.state;

    // console.log(selectedIds.indexOf(String(value.uid)));
    if (type === "all") {
      let paidUserss = this.state.paidUsers;
      // console.log(paidUserss);

      paidUserss.forEach((each) => (each.isChecked = 0));
      this.setState(
        (p) => ({
          paidUsers: paidUserss,
          excelDataSelected: [
            ...p.excelDataSelected.filter((e) => e.isChecked == 1),
            ...paidUserss.filter((e) => e.isChecked == 1),
          ],
        }),
        () => {
          paidUserss.forEach((each) =>
            checked ? (each.isChecked = 1) : (each.isChecked = 0)
          );
          this.setState((p) => ({
            paidUsers: paidUserss,
            excelDataSelected: [
              ...p.excelDataSelected.filter((e) => e.isChecked == 1),
              ...paidUserss.filter((e) => e.isChecked == 1),
            ],
          }));
        }
      );
    } else {
      let paidUserss = this.state.paidUsers;
      // console.log(checked);
      paidUserss.forEach(
        (each) =>
          each.uid == String(value.uid) && (each.isChecked = checked ? 1 : 0)
      );
      this.setState({
        paidUsers: paidUserss,
        // excelDataSelected: paidUserss.filter((e) => e.isChecked == 1),
      });
      if (selectedIds.indexOf(String(value.uid)) < 0 && checked) {
        const newgroup = excelDataSelected;
        newgroup.push(value);
        const ids = selectedIds;
        ids.push(String(value.uid));
        this.setState({ excelDataSelected: newgroup, selectedIds: ids });
      } else {
        const newgroup = excelDataSelected.filter((v) => v !== value);
        const newIds = selectedIds.filter(
          (ve) => String(ve) !== String(value.uid)
        );
        this.setState({ excelDataSelected: newgroup, selectedIds: newIds });
      }
      // console.log(selectedIds);
      //   value.forEach((eachVal) => {
      //     if (selectedIds.indexOf(String(eachVal.uid)) < 0) {
      //       const newgroup = excelDataSelected;
      //       newgroup.push(eachVal);
      //       const ids = selectedIds;
      //       ids.push(String(eachVal.uid));
      //       this.setState({ excelDataSelected: newgroup, selectedIds: ids });
      //     } else {
      //       const newgroup = excelDataSelected.filter((v) => v !== eachVal);
      //       const newIds = selectedIds.filter(
      //         (ve) => String(ve) !== String(eachVal.uid)
      //       );
      //       this.setState({ excelDataSelected: newgroup, selectedIds: newIds });
      //     }
      //   });
      // }
    }

    // if (checked) {
    //   this.setState((p) => ({
    //     excelDataSelected: [...p.excelDataSelected, value],
    //   }));
    // } else {
    //   this.setState((p) => ({
    //     excelDataSelected: p.excelDataSelected.filter((e) => e != value),
    //   }));
    // }
  };
  // addToexportExcelAll = (data) => {
  //   this.setState({
  //     excelDataSelected: data,
  //   });
  // };
  fileHandler = (event) => {
    let fileObj = event.target.files[0];
    //just pass the fileObj as parameter
    // console.log(fileObj.name.split(".xlsx"));
    ExcelRenderer(fileObj, (err, resp) => {
      if (err) {
        console.log(err);
      } else {
        // console.log(resp);
        this.setState({
          cols: resp.rows.slice(0, 1)[0],
          rows: resp.rows.slice(1, resp.rows.length),
        });
      }
    });
  };

  recentPaidUser = () => {
    const {
      usersCount,
      paidUsers,
      search,
      GETALLCOUNT,
      hidePagination,
      excelDataSelected,
    } = this.state;
    // const paidUsers = this.sliceUsers("paid");
    console.log(excelDataSelected);
    const style = `table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width:100%;
    }
    
    td, th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 10px;
    }
    
    tr:nth-child(even) {
      background-color: #dddddd;
    }`;

    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All Users</h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            {/* {!searchClicked ? ( */}
            <form
              onSubmit={this.searchUsers}
              style={{ cursor: "pointer", display: "flex", marginBottom: 10 }}
            >
              <input
                type={"text"}
                value={search}
                id="search"
                onChange={(e) => this.setState({ search: e.target.value })}
                placeholder="Enter Name / Email / Phone Number"
              />
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  // marginLeft: 5,
                  backgroundColor: "green",
                  padding: 5,
                  color: "#fff",
                  borderRadius: 10,
                }}
              >
                <button
                  type="submit"
                  style={{
                    backgroundColor: "transparent",
                    borderWidth: 0,
                    color: "#fff",
                  }}
                >
                  <i className="bi bi-search"></i>
                </button>
              </div>
            </form>
            {/* ) : (
              <i
                className="bi bi-search"
                style={{
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                }}
                onClick={() => {
                  this.setState(
                    {
                      search: "",
                      searchClicked: false,
                      isLoading: true,
                      hidePagination: false,
                    },
                    this.getData
                  );
                }}
              >
                Search Again
              </i>
              )*/}
          </div>
          <div>
            <p
              style={{
                background: "green",
                color: "white",
                padding: 5,
                borderRadius: 10,
                cursor: "pointer",
                textAlign: "center",
                width: 300,
              }}
              onClick={() => this.exportAllusers("CSVExport", "csv")}
            >
              New: {GETALLCOUNT?.new}
            </p>
            <p
              style={{
                background: "green",
                color: "white",
                padding: 5,
                marginTop: 10,
                textAlign: "center",
                borderRadius: 10,
                cursor: "pointer",
                width: 300,
                marginBottom: 10,
              }}
              onClick={() => this.exportAllusers("GETALLUSERSEXPORTCSV", "csv")}
            >
              All: {GETALLCOUNT?.all}
            </p>
          </div>
          <Button
            style={{
              background: "green",
              color: "white",
              padding: 10,
              marginTop: 10,
              textAlign: "center",
              borderRadius: 10,
              cursor: "pointer",
              // width: 300,
              marginBottom: 10,
            }}
            onClick={() => this.exportAllusers("getTopStudentsall", "csv")}
          >
            Toppers
          </Button>
          <div style={{ display: "flex", gap: 4 }}>
            <div>
              <Button
                className="btn exportbtn"
                onClick={() => {
                  this.setState((p) => ({
                    popUpOpen: !p.popUpOpen,
                    popupType: "Add New User",
                    exportdatapopup: false,
                  }));
                }}
              >
                Add New
              </Button>
            </div>

            <div
              onClick={() =>
                this.setState({
                  importPopUp: true,
                  popUpOpen: false,
                  exportdatapopup: false,
                })
              }
            >
              <Button className="btn exportbtn">Import</Button>
            </div>
            <div
              onClick={() =>
                this.setState({
                  importPopUp: false,
                  popUpOpen: false,
                  todaysPop: false,
                  istodayLoading: false,
                  exportdatapopup: true,
                  allUsersData: [],
                })
              }
            >
              <Button className="btn exportbtn">Export</Button>
            </div>
            <div
              onClick={() =>
                this.setState(
                  {
                    importPopUp: false,
                    popUpOpen: false,
                    todaysPop: true,
                    istodayLoading: true,
                    exportdatapopup: false,
                  },
                  this.getTodayData
                )
              }
            >
              <Button className="btn exportbtn">Today</Button>
            </div>
          </div>
          {/* <div
            onClick={() => this.setState((p) => ({ showHide: !p.showHide }))}
            style={{ cursor: "pointer" }}
          >
            {!showHide ? (
              <i className="bi bi-eye-fill">{"Show"}</i>
            ) : (
              <i className="bi bi-eye-slash"> {"Hide"}</i>
            )}
          </div> */}
        </div>

        <div className="dashboardtable">
          <table>
            <thead>
              <tr>
                <th onClick={(e) => this.addToexportExcel(e, paidUsers, "all")}>
                  <input
                    type="checkbox"
                    // defaultChecked={
                    //   excelDataSelected.length === paidUsers.length
                    // }
                    value="checkedall"
                  />
                </th>
                {/* <th>S.No</th> */}
                <th>Student name</th>
                {/* <th>Email</th> */}
                <th>Whatsapp</th>
                <th>District</th>

                <th style={{ width: "90px" }}>Groups Joined </th>
                {/* <th>Message</th> */}
                {/* <th>Joined On</th> */}
                {/* {showHide && <th>Send Msg</th>} */}
                <th>Action</th>
                <th>EM</th>

                {/* <th>Block/Unblock</th> */}
              </tr>
            </thead>
            <tbody>
              {paidUsers.map((each, ind) => {
                return (
                  <tr
                    key={"allusers" + ind + each.uid}
                    // style={{ background: each.blocked === "0" ? "" : "red" }}
                  >
                    {/* {console.log(
                      excelDataSelected.filter((eac) => eac.uid === each.uid)
                        .length > 0 &&
                        excelDataSelected.filter(
                          (eac) => eac.uid === each.uid
                        )[0].isChecked
                    )} */}
                    <td style={{ cursor: "pointer" }}>
                      {/* <i
                        className={`bi ${
                          excelDataSelected.filter(
                            (eac) => eac.uid === each.uid
                          ).length > 0 &&
                          excelDataSelected.filter(
                            (eac) => eac.uid === each.uid
                          )[0].isChecked === 0
                            ? "bi-plus-circle"
                            : "bi-trash-fill"
                        }`}
                      ></i> */}
                      <div>
                        <input
                          type="checkbox"
                          name="selectedxlsusers"
                          id={"selecteduser" + each.uid}
                          value={each}
                          // checked={each.isChecked == 1}
                          checked={
                            excelDataSelected.filter(
                              (eac) => eac.uid === each.uid
                            ).length > 0 &&
                            excelDataSelected.filter(
                              (eac) => eac.uid === each.uid
                            )[0].isChecked
                          }
                          onChange={(e) =>
                            this.addToexportExcel(e, each, "each")
                          }
                        />
                      </div>
                    </td>
                    {/* <td>{each.uid}</td> */}
                    <td>{each.first_name + " " + each.last_name} </td>
                    {/* <td>{each?.email?.split("@gmail.com")[0]} </td> */}
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => {
                        navigator.clipboard.writeText(
                          `https://api.whatsapp.com/send?phone=91${each.contact_no}&amp;text=Hai`
                        );
                        NotificationManager.success("Copied to ClipBoard");
                      }}
                    >
                      {each.contact_no}
                    </td>
                    <td>{each.district} </td>
                    {/* <td>{each.date_of_birth}</td> */}
                    {/* <td>
                      <select>
                        {each.gids !== null &&
                          String(each.gids)
                            .split(",")
                            .map((e, i) => {
                              return (
                                <option key={"giduser" + e + i}>
                                  {packagesAllList.filter(
                                    (va) => va.gid == e
                                  )[0] !== undefined
                                    ? packagesAllList.filter(
                                        (va) => va.gid == e
                                      )[0].group_name
                                    : ""}
                                </option>
                              );
                            })}
                      </select>
                    </td> */}
                    <td
                      onClick={() =>
                        this.setState(
                          {
                            userId: each.contact_no,
                            importUserPackagePopUp: true,
                            packageUserName:
                              each.first_name + " " + each.last_name,
                            userPackLoading: true,
                          },
                          this.getUserPackageData
                        )
                      }
                      style={{ cursor: "pointer" }}
                    >
                      <div
                        style={{
                          display: "flex",
                          flexWrap: "wrap",
                          width: 80,
                        }}
                      >
                        {each.gids !== null &&
                          each.gids
                            ?.split(",")
                            .map((eacc) => (
                              <p key={"groupJoined" + eacc}>{eacc}, </p>
                            ))}
                      </div>
                    </td>
                    {/* <td>{each.registered_date}</td> */}

                    {/* {showHide && (
                      <td>
                        <a
                          href={`//api.whatsapp.com/send?phone=91${each.contact_no}&amp;text=Hai`}
                          target="_blank"
                        >
                          {" "}
                          <i className="bi -forward-step"></i>
                        </a>
                      </td>
                    )} */}

                    <td>
                      {/* <i
                        className="bi bi-chat-dots-fill"
                        style={{
                          cursor: "pointer",
                          textAlign: "center",
                          marginRight: 15,
                        }}
                        onClick={() =>
                          this.setState(
                            { userno: each.contact_no },
                            this.sendMessagetoUser
                          )
                        }
                      ></i> */}
                      <i
                        className="bi bi-pencil-fill"
                        id={"edit_user_" + each.contact_no}
                        style={{ cursor: "pointer" }}
                        onClick={() => {
                          this.setState((p) => ({
                            popUpOpen: !p.popUpOpen,
                            popupType: "Edit User",
                            name: each.first_name,
                            email: each.email,
                            district: each.district,
                            dob: each.date_of_birth,
                            phoneNum: each.contact_no,
                            userType: each.su,
                            surname: each.last_name,
                            selectedPackages: each.gids
                              ? String(each.gids)
                                  ?.split(",")
                                  .map((e) => e != "" && parseInt(e))
                                  .filter((e) => e !== "false")
                              : [],
                          }));
                        }}
                      ></i>
                      <i
                        className="bi bi-trash-fill"
                        style={{ cursor: "pointer", marginLeft: 15 }}
                        onClick={() =>
                          this.setState(
                            { userno: each.contact_no },
                            this.deleteUser
                          )
                        }
                      ></i>
                      <i
                        className={
                          each.blocked !== "0"
                            ? "bi bi-ban"
                            : "bi bi-check-circle-fill"
                        }
                        style={{
                          cursor: "pointer",
                          textAlign: "center",
                          marginLeft: 15,
                        }}
                        onClick={() =>
                          this.setState({ userno: each.contact_no }, () =>
                            this.blockOrUnblockUser(
                              each.blocked === "0" ? "1" : "0"
                            )
                          )
                        }
                      ></i>
                    </td>
                    <td>
                      <i
                        className={
                          each.enable_english_medium === "1"
                            ? "bi bi-bookmark-check-fill"
                            : "bi bi-bookmark-check"
                        }
                        style={{
                          cursor: "pointer",
                          textAlign: "center",
                          marginLeft: 15,
                        }}
                        onClick={() =>
                          this.setState({ userno: each.contact_no }, () =>
                            this.changeUserMedium(
                              each.enable_english_medium === "1" ? "0" : "1"
                            )
                          )
                        }
                      ></i>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        {!hidePagination && this.renderPaginationButtons("paid", usersCount)}
      </div>
    );
  };

  formatNumber = (num, from, limit) => {
    num =
      Number(num) > from && Number(num) < 10 && num.length == 1
        ? "0" + num
        : num;
    if (Number(num) > limit) {
      num = num.substr(1, 2);
      num =
        Number(num) > from && Number(num) < 10 && num.length == 1
          ? "0" + num
          : num;
    }
    return num;
  };
  dateFormat = (e) => {
    var dateValue = e;
    if (/\D$/.test(dateValue)) {
      dateValue = dateValue.substr(0, dateValue.length - 3);
    }
    dateValue = dateValue.replaceAll(" ", "");
    var arr = dateValue?.split("/");

    if (arr[0]) arr[0] = this.formatNumber(arr[0], 3, 31);
    if (arr[1]) arr[1] = this.formatNumber(arr[1], 1, 12);

    var result = arr.map(function (val, index) {
      return val.length == 2 && index < 2 ? val + " / " : val;
    });
    this.setState({ dob: result.join("").substr(0, 14) });
  };

  onChangeDob = (e) => {
    e.preventDefault();
    this.dateFormat(e.target.value);
  };

  onChangeData = (e) => {
    this.setState({ [e.target.name]: e.target.value });
  };
  onChangePackageData = (e) => {
    this.setState({
      selectedPackages:
        typeof value === "string" ? e.target.value?.split(",") : e.target.value,
    });
  };
  importUsersUpdateDb = async (data) => {
    const {
      name,
      surname,
      phoneNum,
      email,
      district,
      dob,
      selectedPackages,
      userType,
    } = data;
    // console.log(data);
    const registrationData = {
      name,
      surname,
      email,
      district,
      dob,
      number: phoneNum,
      type: "UPLOAD",
      gidselected: selectedPackages.join(","),
      su: userType,
    };
    // console.log(registrationData);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/create-new-user`,
        registrationData,
        { headers }
      );
      // console.log(data);
      return true;
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      // this.setState({ isLoading: false });
    }
  };
  importUsers = async () => {
    const { cols, rows, existedUsers, importCount } = this.state;
    // console.log(rows);
    if (rows != undefined) {
      // let ind = 1;
      this.setState({
        importUsersClicked: true,
        isImporting: true,
        totalCountImport: rows.length,
      });
      // await rows.forEach(async (each, j) => {
      //   if (each.length > 0) {
      //     await this.importUsersUpdateDb({
      //       name: each[cols.indexOf("first_name")],
      //       email: each[cols.indexOf("email")],
      //       district: each[cols.indexOf("district")],
      //       dob: each[cols.indexOf("date_of_birth")],
      //       phoneNum: each[cols.indexOf("contact_no")],
      //       userType: each[cols.indexOf("su")],
      //       surname: each[cols.indexOf("last_name")],
      //       selectedPackages:
      //         each[cols.indexOf("gids")] !== null
      //           ? String(each[cols.indexOf("gids")])
      //               .split(",")
      //               .map((e) => e != "" && parseInt(e))
      //           : [],
      //     });
      //   }
      // });
      for (const each of rows) {
        if (each.length > 0) {
          await this.importUsersUpdateDb({
            name: each[cols.indexOf("first_name")],
            email: each[cols.indexOf("email")],
            district: each[cols.indexOf("district")],
            dob: each[cols.indexOf("date_of_birth")],
            phoneNum: each[cols.indexOf("contact_no")],
            userType: each[cols.indexOf("su")],
            surname: each[cols.indexOf("last_name")],
            selectedPackages:
              each[cols.indexOf("gids")] !== null
                ? String(each[cols.indexOf("gids")])
                    .split(",")
                    .map((e) => e !== "" && parseInt(e))
                : [],
          });
          this.setState((prevState) => ({
            importCount: prevState.importCount + 1,
          }));
        }
      }

      // console.log(existedUsers, insertedUsers);
      this.setState({
        existedUsers: 0,
        insertedUsers: 0,
        cols: [],
        importCount: 0,
        totalCountImport: 0,
        rows: [],
        importPopUp: false,
        importUsersClicked: false,
        isImporting: false,
      });

      NotificationManager.success(`Data Imported Succesfully...`);
      this.getData();
    } else {
      NotificationManager.error(`Please Select valid file to import`);
    }
  };

  renderImportPopUp = () => {
    const { importPopUp, importUsersClicked, importCount, totalCountImport } =
      this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    const sampleData = [
      {
        uid: "",
        first_name: "",
        last_name: "",
        email: "",
        contact_no: "",
        date_of_birth: "",
        district: "",
        gids: "",
        su: "",
      },
    ];
    return (
      <Dialog
        open={importPopUp}
        onClose={this.handleImportPopup}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Import Users</p>
            </div>
            <Button
              onClick={this.handleImportPopup}
              style={{ color: "#fff", fontSize: 20 }}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <div
            style={{
              marginTop: "20px",
              padding: 20,
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
            }}
          >
            <input
              type="file"
              onChange={this.fileHandler}
              style={{ padding: "10px" }}
            />
            <Button
              className="btn"
              onClick={this.importUsers}
              style={{ color: "#fff" }}
              disabled={importUsersClicked}
            >
              {importUsersClicked
                ? "Importing users " + importCount + "/" + totalCountImport
                : "Import Now"}
            </Button>
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
              marginTop: 10,
            }}
          >
            <ExportCSV
              csvData={sampleData}
              fileName={"sampleData user import"}
              title={"Download Sample File"}
              emptyFn={this.emptySelectedImport}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  handleUserPackage = () => {
    this.setState(
      (p) => ({
        importUserPackagePopUp: !p.importUserPackagePopUp,
        userId: null,
        packageEditClicked: false,
        packageUserName: "",
        selectedPackageUserId: "",
        userPackLoading: false,
        selectedPackageGid: "",
      }),
      this.getData
    );
  };

  deleteUserPackage = async () => {
    const { userId, selectedPackageGid } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "userPackageDelete",
      search: selectedPackageGid,
      qid: userId,
    };
    if (window.confirm("Do you really want to remove entry?")) {
      try {
        const userList = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          {
            headers,
          }
        );
        // console.log(packagesList);
        NotificationManager.success(`User Package Deleted Succesfully...`);

        this.setState(
          {
            packageEditClicked: false,
            selectedPackageUserId: "",
            userPackLoading: false,
          },
          this.getUserPackageData
        );
      } catch (err) {
        // console.log(err);
        this.setState({
          isLoading: false,
          login: "invalid",
        });
      }
    }
  };
  getUserPackageData = async () => {
    const { userId } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getUserPackageData",
      search: "",
      qid: userId,
    };
    try {
      const userList = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        {
          headers,
        }
      );

      this.setState({
        userPackLoading: false,
        particularUserPackages: userList.data[0],
      });
    } catch (err) {
      // console.log(err);
      this.setState({
        userPackLoading: false,
        login: "invalid",
      });
    }
  };

  savePackageUser = async () => {
    const { userId, expiryDate, amount, selectedPackageGid } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "SaveUserPackageData",
      search: expiryDate + "_$" + amount + "_$" + selectedPackageGid,
      qid: userId,
    };
    try {
      const userList = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        {
          headers,
        }
      );

      // console.log(packagesList);
      NotificationManager.success("User Package Updated Successfully");
      this.setState(
        {
          userPackLoading: false,
          expiryDate: "",
          amount: "",
          selectedPackageGid: "",
        },
        this.getUserPackageData
      );
    } catch (err) {
      // console.log(err);
      this.setState({
        userPackLoading: false,
        login: "invalid",
      });
    }
  };

  renderUserPackagesPopup = () => {
    const {
      importUserPackagePopUp,
      packageEditClicked,
      packageUserName,
      expiryDate,
      amount,
      selectedPackageGid,
      particularUserPackages,
      userPackLoading,
      userId,
    } = this.state;
    const style = `table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width:100%;
    }
    
    td, th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 10px;
    }
    
    tr:nth-child(even) {
      background-color: #dddddd;
    }`;
    return (
      <Dialog
        open={importUserPackagePopUp}
        onClose={this.handleUserPackage}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Package Edit of {packageUserName}</p>
            </div>
            <Button
              onClick={this.handleUserPackage}
              style={{ color: "#fff", fontSize: 20 }}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent" style={{ paddingTop: 15 }}>
          <style>{style}</style>
          {!userPackLoading ? (
            <table>
              <tr>
                <th>Gid</th>
                <th>Payment Date</th>
                <th>Amount</th>
                <th>Action</th>
                <th>Reciept</th>
              </tr>
              <tbody>
                {particularUserPackages.length > 0 ? (
                  particularUserPackages.map((e) => (
                    <tr>
                      <td>{e.gids}</td>

                      {packageEditClicked && selectedPackageGid === e.gids ? (
                        <td>
                          <input
                            type="text"
                            value={expiryDate}
                            onChange={(e) =>
                              this.setState({ expiryDate: e.target.value })
                            }
                            style={{ padding: "10px" }}
                          />
                        </td>
                      ) : (
                        <td>
                          <p>{e.expiry_date}</p>
                        </td>
                      )}

                      {packageEditClicked && selectedPackageGid === e.gids ? (
                        <td>
                          <input
                            type="text"
                            value={amount}
                            onChange={(e) =>
                              this.setState({ amount: e.target.value })
                            }
                            style={{ padding: "10px" }}
                          />
                        </td>
                      ) : (
                        <td>
                          <p>{e.amount}</p>
                        </td>
                      )}

                      <td>
                        <div
                          style={{ display: "flex", justifyContent: "center" }}
                        >
                          <i
                            className={`bi ${
                              packageEditClicked &&
                              selectedPackageGid === e.gids
                                ? "bi-check-circle-fill"
                                : "bi-pencil-fill"
                            } `}
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                              if (!packageEditClicked)
                                this.setState((p) => ({
                                  selectedPackageGid: e.gids,
                                  expiryDate: e.expiry_date,
                                  amount: e.amount,
                                  packageEditClicked: !p.packageEditClicked,
                                }));
                              else {
                                this.setState(
                                  (p) => ({
                                    packageEditClicked: !p.packageEditClicked,
                                    userPackLoading: true,
                                  }),
                                  this.savePackageUser
                                );
                              }
                            }}
                          ></i>
                          <i
                            className="bi bi-trash-fill"
                            style={{ cursor: "pointer", marginLeft: 15 }}
                            onClick={() =>
                              this.setState(
                                {
                                  selectedPackageGid: e.gids,
                                },
                                this.deleteUserPackage
                              )
                            }
                          ></i>
                        </div>
                      </td>
                      <td
                        style={{
                          color: "black",
                          padding: 10,
                          textAlign: "center",
                        }}
                      >
                        <a
                          href={`${
                            commonData["api"]
                          }/download-payment-reciept/${e.gids}/${userId}/${
                            "Payment Reciept _" + e.gids
                          }`}
                          target="_blank"
                          className="linkto fontUSerpay"
                        >
                          <i className="bi bi-download"></i>
                        </a>
                      </td>
                    </tr>
                  ))
                ) : (
                  <p>No data available</p>
                )}
              </tbody>
            </table>
          ) : (
            <div>
              <Loader />
            </div>
          )}
        </DialogContent>
      </Dialog>
    );
  };

  renderTodayPopUp = () => {
    const { todaysPop, todayData, todaySelected, istodayLoading } = this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    let reportstring = "";
    if (todaySelected === "1") {
      reportstring = "Todays Payments";
    } else if (todaySelected === "2") {
      reportstring = "Todays Registrations";
    } else if (todaySelected === "3") {
      reportstring = "Todays User Edits";
    }
    console.log(todaySelected, reportstring);
    return (
      <Dialog
        open={todaysPop}
        onClose={this.handlepoptoday}
        maxWidth={"lg"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Todays Updates </p>
            </div>

            <select
              onChange={(e) =>
                this.setState(
                  { todaySelected: e.target.value, istodayLoading: true },
                  this.getTodayData
                )
              }
              style={{ borderRadius: 20, padding: 10 }}
            >
              <option value={1} selected={todaySelected === "1"}>
                Todays Payments
              </option>
              <option value={2} selected={todaySelected === "2"}>
                Todays Registrations
              </option>
              <option value={3} selected={todaySelected === "3"}>
                Todays User Edits
              </option>
            </select>

            <div
              style={{
                display: "flex",
                flexDirection: "row",
                justifyContent: "center",
                marginTop: 10,
              }}
            >
              <ExportCSV
                csvData={todayData}
                fileName={reportstring + " " + new Date()}
                title={"Download Todays Data"}
              />
            </div>
            <Button
              onClick={this.handlepoptoday}
              style={{ color: "#fff", fontSize: 20 }}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {istodayLoading ? (
            <div className="loader-main-container">
              <Loader />
            </div>
          ) : todayData.length > 0 ? (
            <div className="dashboardtable">
              <table>
                <thead>
                  <tr>
                    <th>Student name</th>
                    <th>Email</th>
                    <th>Whatsapp</th>
                    <th>D.O.B</th>
                    <th>District</th>
                    <th>
                      {" "}
                      {todaySelected === "1" ? "Paid For" : "Groups Joined"}
                    </th>
                    {todaySelected === "1" && <th>Amount</th>}
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  {todayData.map((each, ind) => {
                    let dobNew = each.date_of_birth;

                    return (
                      <tr key={"allusers" + ind + each.uid}>
                        {/* {console.log(excelDataSelected.indexOf(each))} */}

                        <td
                          style={{ cursor: "pointer" }}
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `${each.first_name + " " + each.last_name},${
                                each.email
                              },${each.contact_no},${dobNew},${each.district},${
                                each.gid
                              }`
                            );
                            NotificationManager.success("Copied to ClipBoard");
                          }}
                        >
                          {each.uid +
                            " ) " +
                            each.first_name +
                            " " +
                            each.last_name}{" "}
                        </td>
                        <td>{each.email?.split("gmail.com")[0]}.. </td>
                        <td
                          style={{ cursor: "pointer" }}
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `https://api.whatsapp.com/send?phone=91${each.contact_no}&amp;text=Hai`
                            );
                            NotificationManager.success("Copied to ClipBoard");
                          }}
                        >
                          {each.contact_no}
                        </td>
                        <td>{dobNew} </td>
                        <td>{each.district} </td>
                        {/* {todaySelected == 1 && (
                          <td>
                            {
                              packagesAllList.filter(
                                (va) => va.gid == each.gid
                              )[0].group_name
                            }
                          </td>
                        )} */}
                        <td
                          onClick={() => {
                            if (todaySelected != 1)
                              this.setState(
                                {
                                  userId: each.contact_no,
                                  importUserPackagePopUp: true,
                                  packageUserName:
                                    each.first_name + " " + each.last_name,
                                  userPackLoading: true,
                                },
                                this.getUserPackageData
                              );
                          }}
                          style={{ cursor: "pointer" }}
                        >
                          <div
                            style={{
                              display: "flex",
                              flexWrap: "wrap",
                              width: 80,
                            }}
                          >
                            {each.gid !== null &&
                              each.gid
                                ?.split(",")
                                .map((eacc) => (
                                  <p key={"groupJoinedtoday" + eacc}>
                                    {eacc},{" "}
                                  </p>
                                ))}
                          </div>
                        </td>
                        {todaySelected === "1" && <td>{each.amount}</td>}
                        <td>
                          <i
                            className="bi bi-pencil-fill"
                            style={{ cursor: "pointer" }}
                            onClick={() => {
                              this.setState((p) => ({
                                popUpOpen: !p.popUpOpen,
                                popupType: "Edit User",
                                name: each.first_name,
                                email: each.email,
                                district: each.district,
                                dob: dobNew,
                                phoneNum: each.contact_no,
                                userType: each.su,
                                surname: each.last_name,
                                selectedPackages:
                                  each.gid !== null
                                    ? String(each.gid)
                                        ?.split(",")
                                        .map((e) => e != "" && parseInt(e))
                                    : [],
                              }));
                            }}
                          ></i>
                          <i
                            className="bi bi-trash-fill"
                            style={{ cursor: "pointer", marginLeft: 15 }}
                            onClick={() =>
                              this.setState(
                                { userno: each.contact_no },
                                this.deleteUser
                              )
                            }
                          ></i>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          ) : (
            <p>No data available ... </p>
          )}
        </DialogContent>
      </Dialog>
    );
  };

  renderUserEdit = () => {
    const {
      name,
      email,
      district,
      dob,
      phoneNum,
      userType,
      surname,
      selectedPackages,
      packagesAllList,
      popupType,
      sendMessage,
    } = this.state;
    return (
      <form className="form-register" onSubmit={this.updateUserInfo}>
        <TextField
          className="input-box register"
          label="Name"
          variant="filled"
          value={name}
          name="name"
          onChange={this.onChangeData}
        />
        <TextField
          className="input-box register"
          label="Surname"
          variant="filled"
          value={surname}
          name="surname"
          onChange={this.onChangeData}
        />
        <TextField
          className="input-box register"
          label="Email Address"
          variant="filled"
          value={email}
          name="email"
          onChange={this.onChangeData}
        />
        <TextField
          required
          className="input-box register"
          label="Phone Number"
          variant="filled"
          value={phoneNum}
          name="phoneNum"
          inputProps={{ maxLength: 10 }}
          onChange={this.onChangeData}
        />

        <Select
          id="Please-Select-District"
          className="input-box dis"
          // label="Please Select District"
          value={district}
          name="district"
          onChange={this.onChangeData}
          MenuProps={MenuProps}
        >
          <MenuItem value={10} disabled className="attempt-option-select">
            Please Select District
          </MenuItem>
          <MenuItem value="Anantapur" className="attempt-option-select">
            Anantapur
          </MenuItem>
          <MenuItem value="Chittoor" className="attempt-option-select">
            Chittoor
          </MenuItem>
          <MenuItem value="East Godavari" className="attempt-option-select">
            East Godavari
          </MenuItem>
          <MenuItem value="Guntur" className="attempt-option-select">
            Guntur
          </MenuItem>
          <MenuItem value="Krishna" className="attempt-option-select">
            Krishna
          </MenuItem>
          <MenuItem value="Kurnool" className="attempt-option-select">
            Kurnool
          </MenuItem>
          <MenuItem value="Prakasam" className="attempt-option-select">
            Prakasam
          </MenuItem>
          <MenuItem value="Srikakulam" className="attempt-option-select">
            Srikakulam
          </MenuItem>
          <MenuItem
            value="Sri Potti Sriramulu Nellore"
            className="attempt-option-select"
          >
            Sri Potti Sriramulu Nellore
          </MenuItem>
          <MenuItem value="Visakhapatnam" className="attempt-option-select">
            Visakhapatnam
          </MenuItem>
          <MenuItem value="Vizianagaram" className="attempt-option-select">
            Vizianagaram
          </MenuItem>
          <MenuItem value="West Godavari" className="attempt-option-select">
            West Godavari
          </MenuItem>
          <MenuItem value="Kadapa" className="attempt-option-select">
            Kadapa
          </MenuItem>
          <MenuItem value="Adilabad" className="attempt-option-select">
            Adilabad
          </MenuItem>
          <MenuItem
            value="Bhadradri Kothagudem"
            className="attempt-option-select"
          >
            Bhadradri Kothagudem
          </MenuItem>
          <MenuItem value="Hyderabad" className="attempt-option-select">
            Hyderabad
          </MenuItem>
          <MenuItem value="Jagtial" className="attempt-option-select">
            Jagtial
          </MenuItem>
          <MenuItem value="Jangaon" className="attempt-option-select">
            Jangaon
          </MenuItem>
          <MenuItem
            value="Jayashankar Bhoopalpally"
            className="attempt-option-select"
          >
            Jayashankar Bhoopalpally
          </MenuItem>
          <MenuItem value="Jogulamba Gadwal" className="attempt-option-select">
            Jogulamba Gadwal
          </MenuItem>
          <MenuItem value="Kamareddy" className="attempt-option-select">
            Kamareddy
          </MenuItem>
          <MenuItem value="Karimnagar" className="attempt-option-select">
            Karimnagar
          </MenuItem>
          <MenuItem value="Khammam" className="attempt-option-select">
            Khammam
          </MenuItem>
          <MenuItem
            value="Komaram Bheem Asifabad"
            className="attempt-option-select"
          >
            Komaram Bheem Asifabad
          </MenuItem>
          <MenuItem value="Mahabubabad" className="attempt-option-select">
            Mahabubabad
          </MenuItem>
          <MenuItem value="Mahabubnagar" className="attempt-option-select">
            Mahabubnagar
          </MenuItem>
          <MenuItem value="Mancherial" className="attempt-option-select">
            Mancherial
          </MenuItem>
          <MenuItem value="Medak" className="attempt-option-select">
            Medak
          </MenuItem>
          <MenuItem value="Medchal" className="attempt-option-select">
            Medchal
          </MenuItem>
          <MenuItem value="Nagarkurnool" className="attempt-option-select">
            Nagarkurnool
          </MenuItem>
          <MenuItem value="Nalgonda" className="attempt-option-select">
            Nalgonda
          </MenuItem>
          <MenuItem value="Nirmal" className="attempt-option-select">
            Nirmal
          </MenuItem>
          <MenuItem value="Nizamabad" className="attempt-option-select">
            Nizamabad
          </MenuItem>
          <MenuItem value="Peddapalli" className="attempt-option-select">
            Peddapalli
          </MenuItem>
          <MenuItem value="Rajanna Sircilla" className="attempt-option-select">
            Rajanna Sircilla
          </MenuItem>
          <MenuItem value="Rangareddy" className="attempt-option-select">
            Rangareddy
          </MenuItem>
          <MenuItem value="Sangareddy" className="attempt-option-select">
            Sangareddy
          </MenuItem>
          <MenuItem value="Siddipet" className="attempt-option-select">
            Siddipet
          </MenuItem>
          <MenuItem value="Suryapet" className="attempt-option-select">
            Suryapet
          </MenuItem>
          <MenuItem value="Vikarabad" className="attempt-option-select">
            Vikarabad
          </MenuItem>
          <MenuItem value="Wanaparthy" className="attempt-option-select">
            Wanaparthy
          </MenuItem>
          <MenuItem value="Warangal" className="attempt-option-select">
            Warangal
          </MenuItem>
          <MenuItem
            value="Yadadri Bhuvanagiri"
            className="attempt-option-select"
          >
            Yadadri Bhuvanagiri
          </MenuItem>
        </Select>
        <TextField
          variant="filled"
          value={dob}
          name="dob"
          // inputProps={{ maxLength: 10 }}
          onChange={this.onChangeDob}
          label={"Enter D.O.B in dd/MMM/yyyy Format"}
          className="input-box register"
        />

        <Select
          required
          id="Please-Select-District"
          className="input-box dis"
          // label="Please Select Package"
          value={selectedPackages}
          name="selectedPackages"
          multiple
          displayEmpty
          onChange={this.onChangePackageData}
          renderValue={(selectedPackages) => {
            if (selectedPackages.length === 0) {
              return <em>Please Select Package</em>;
            }

            return selectedPackages.join(", ");
          }}
          MenuProps={MenuProps}
        >
          <MenuItem disabled value="" className="attempt-option-select">
            Please Select Package
          </MenuItem>
          {packagesAllList
            ?.filter((e) => e.price > 0)
            .map((e, i) => (
              <MenuItem
                className="selectionbox"
                value={e.gid}
                key={"multiselect" + i}
              >
                <Checkbox checked={selectedPackages.indexOf(e.gid) > -1} />
                <ListItemText primary={e.group_name} />
              </MenuItem>
            ))}
        </Select>
        <Select
          required
          id="Please-Select-District"
          className="input-box dis"
          // label="Type of user"
          value={userType}
          name="userType"
          onChange={this.onChangeData}
          MenuProps={MenuProps}
        >
          <MenuItem value="0" className="attempt-option-select">
            Student
          </MenuItem>
          <MenuItem value="2" className="attempt-option-select22">
            Moderator
          </MenuItem>
          <MenuItem value="1" className="attempt-option-select">
            Admin
          </MenuItem>
        </Select>
        {/* <DatePicker
          disableFuture
          className="input-box register"
          openTo="year"
          format="dd/MMM/yyyy"
          label="Date of birth"
          views={["year", "month", "date"]}
          value={dob}
          inputVariant="filled"
          name="dob"
          onChange={this.onChangeData}
        /> */}
        {/* <input
          type={"text"}
          value={dob}
          name="dob"
          onChange={this.onChangeData}
          placeholder={"Enter D.O.B in dd/MMM/yyyy Format"}
          className="input-box register"
        /> */}
        <div>
          <label>
            <input
              type="checkbox"
              checked={this.state.sendMessage}
              style={{ marginRight: 5 }}
              onChange={(e) => this.setState({ sendMessage: e.target.checked })}
            />
            Send Message
          </label>
        </div>
        {popupType === "Add New User" ? (
          <Button
            type="submit"
            variant="contained"
            className="btn activateacoountbtn"
          >
            Create New Account
          </Button>
        ) : (
          <Button
            type="submit"
            variant="contained"
            className="btn activateacoountbtn"
          >
            Update Account
          </Button>
        )}
      </form>
    );
  };
  renderPopUp = () => {
    const { popUpOpen, popupType, importPopUp, sendMessage } = this.state;
    return (
      <Dialog
        open={popUpOpen && !importPopUp}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.handleOpen}
            >
              Cancel
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {popupType === "Add New User" && this.renderUserEdit()}
          {popupType === "Edit User" && this.renderUserEdit()}
        </DialogContent>
      </Dialog>
    );
  };

  exportAllusers = async (type = "GETALLUSERSEXPORT", fileType) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type,
      search: "",
      qid: 0,
    };
    try {
      this.setState({ isLoading: true });
      const userList = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        {
          headers,
        }
      );
      this.setState({
        allUsersData: fileType === "csv" ? [] : userList.data[0],
        isLoading: false,
        downloadType: fileType,
      });
      if (fileType === "csv") {
        exportToCSVFile(userList.data[0], "User List", () => {
          this.setState(
            { allUsersData: [], exportdatapopup: false, downloadType: "" },
            () => window.location.reload(false)
          );
        });
      }
    } catch (err) {
      NotificationManager.error("Something went wrong...");
    }
  };

  exportUsersExamsSelectionData = async (gid) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "ExamsSelectionData",
      search: gid,
      qid: 0,
    };
    try {
      // this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data.data[0]);
      if (data.data[0].length > 0) {
        exportToCSVD(data.data[0], "Exams Selection Data");
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  exportdatapopupContent = () => {
    const {
      popUpOpen,
      importPopUp,
      exportdatapopup,
      excelDataSelected,
      fileName,
      fields,
      allUsersData,
      isLoading,
      downloadType,
    } = this.state;
    console.log(excelDataSelected);
    return (
      <Dialog
        open={!popUpOpen && !importPopUp && exportdatapopup}
        onClose={this.handleexportdatapopup}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Export Users</p>
            </div>
            <Button
              onClick={this.handleexportdatapopup}
              style={{ color: "#fff", fontSize: 20 }}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {allUsersData.length > 0 && !isLoading && downloadType !== "csv" && (
            <ExportCSV
              csvData={allUsersData}
              fileName={"Export All Users " + new Date()}
              type={"ExportAllUsers"}
              title={"Download All users Again"}
              emptyFn={() => {
                this.setState(
                  { allUsersData: [], exportdatapopup: false },
                  () => window.location.reload(false)
                );
              }}
            />
          )}
          <div
            style={{
              marginTop: "20px",
              padding: 20,
              display: "flex",
              flexDirection: "row",
              justifyContent: "space-around",
            }}
          >
            <div>
              <Button
                className="btn exportbtn"
                onClick={() => this.exportAllusers("GETALLUSERSEXPORT", "xlsx")}
              >
                Export All Users
              </Button>
            </div>
            <div>
              <ExportCSV
                csvData={excelDataSelected}
                fileName={fileName}
                title={"Export Selected"}
                fields={fields}
                // emptyFn={() => {
                //   let paidUserss = paidUsers;
                //   paidUserss.forEach((each) => (each.isChecked = 0));
                //   this.setState({
                //     allUsersData: [],
                //     excelDataSelected: [],
                //     selectedIds: [],
                //     exportdatapopup: false,
                //     paidUsers: paidUserss,
                //   });
                // }}
              />
            </div>
            <br />
          </div>
          <div>
            <select
              onChange={(e) =>
                this.exportUsersExamsSelectionData(e.target.value)
              }
              style={{ borderRadius: 20, padding: 10 }}
            >
              <option value={1}>
                Please select Exam name to download Users List
              </option>
              {this.state.examsSelectionMaster &&
                this.state.examsSelectionMaster.map((a) => (
                  <option value={a.subject_uid}>{a.subject_name}</option>
                ))}
            </select>
          </div>
          {/* <div style={{ marginTop: 10 }}>
            <Button
              className="btn exportbtn"
              onClick={() => this.exportAllusers("CSVExport", "csv")}
            >
              Export All Users in CSV Format
            </Button>
          </div> */}
        </DialogContent>
      </Dialog>
    );
  };
  render() {
    const { isLoading, login, paidUsers } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.recentPaidUser()}
                {this.renderPopUp()}
                {this.renderImportPopUp()}
                {this.renderTodayPopUp()}
                {this.exportdatapopupContent()}
                {this.renderUserPackagesPopup()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default UserList;
