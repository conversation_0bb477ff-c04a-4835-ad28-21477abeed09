import React, { useEffect, useState } from "react";
import Header from "../Header";
import Loader from "../Loader";
import {
  Divider,
  Button,
  Typography,
  Box,
  Paper,
  Container,
  Grid,
  Card,
  CardContent,
  Chip,
  useTheme,
  Alert,
  AlertTitle,
} from "@mui/material";
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import QuizIcon from '@mui/icons-material/Quiz';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import PaymentIcon from '@mui/icons-material/Payment';
import EventIcon from '@mui/icons-material/Event';
import WarningIcon from '@mui/icons-material/Warning';
import PlayCircleOutlineIcon from '@mui/icons-material/PlayCircleOutline';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import DesktopMenu from "../DesktopMenu";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import { useHistory, useParams } from "react-router-dom";
import commonData from "../../importanValue";
import { v4 as uuidv4 } from "uuid";
import "./styles.css";
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import SubjectIcon from '@mui/icons-material/Subject';
import { alpha } from '@mui/material/styles';

const PREMIUM_COLORS = {
  primary: {
    main: '#2c3e50',
    light: '#34495e',
    dark: '#1a252f',
    contrastText: '#fff'
  },
  secondary: {
    main: '#3498db',
    light: '#5faee3',
    dark: '#2980b9',
    contrastText: '#fff'
  },
  success: {
    main: '#2ecc71',
    light: '#55d98d',
    dark: '#27ae60'
  },
  warning: {
    main: '#f1c40f',
    light: '#f4d03f',
    dark: '#f39c12'
  },
  error: {
    main: '#e74c3c',
    light: '#ec7063',
    dark: '#c0392b'
  },
  info: {
    main: '#3498db',
    light: '#5faee3',
    dark: '#2980b9'
  },
  background: {
    default: '#f8fafc',
    paper: '#ffffff'
  },
  text: {
    primary: '#2c3e50',
    secondary: '#7f8c8d'
  }
};

const cardStyles = {
  examDetailsCard: {
    boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
    borderRadius: '16px',
    transition: 'transform 0.2s ease-in-out',
    '&:hover': {
      transform: 'translateY(-2px)'
    }
  },
  contentSection: {
    p: { xs: 2, sm: 3, md: 4 },
    backgroundColor: PREMIUM_COLORS.background.paper
  },
  sectionTitle: {
    color: PREMIUM_COLORS.primary.main,
    fontWeight: 600,
    fontSize: { xs: '1.25rem', md: '1.5rem' }
  },
  infoBox: {
    backgroundColor: alpha(PREMIUM_COLORS.primary.main, 0.02),
    borderRadius: '12px',
    p: 3,
    border: `1px solid ${alpha(PREMIUM_COLORS.primary.main, 0.08)}`,
    '&:hover': {
      backgroundColor: alpha(PREMIUM_COLORS.primary.main, 0.03),
      borderColor: alpha(PREMIUM_COLORS.primary.main, 0.12)
    }
  },
  statBox: {
    p: 2.5,
    borderRadius: '12px',
    backgroundColor: PREMIUM_COLORS.background.paper,
    border: `1px solid ${alpha(PREMIUM_COLORS.primary.main, 0.08)}`,
    display: 'flex',
    flexDirection: 'column',
    gap: 1,
    transition: 'all 0.2s ease-in-out',
    '&:hover': {
      boxShadow: `0 4px 12px ${alpha(PREMIUM_COLORS.primary.main, 0.08)}`,
      borderColor: PREMIUM_COLORS.primary.main
    }
  },
  actionButton: {
    borderRadius: '12px',
    textTransform: 'none',
    py: 1.5,
    px: 4,
    fontWeight: 600,
    boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
    '&:hover': {
      boxShadow: '0 4px 12px rgba(0,0,0,0.12)'
    }
  }
};

const ExamDetailsPaid = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [examData, setExamData] = useState(null);
  const [attempts, setAttempts] = useState({});
  const [hasPaid, setHasPaid] = useState(false);
  const [invalidLink, setInvalidLink] = useState(false);
  const [access, setAccess] = useState(false);
  const [allowNegativeMarking, setAllowNegativeMarking] = useState(false);
  const [examStatus, setExamStatus] = useState('unavailable');
  const [examStartDate, setExamStartDate] = useState(null);
  const [timeUntilStart, setTimeUntilStart] = useState(null);

  const history = useHistory();
  const { quid } = useParams();

  const fetchExamDetails = async () => {
    try {
      const token = Cookie.get("jwt_token");
      const quizInf = window.atob(quid);
      const uid = localStorage.getItem("num");

      // Fetch all details in a single API call
      const response = await axios.post(
        `${commonData["api"]}/support`,
        {
          type: "getExamFullDetails",
          qid: quid,
          search: uid
        },
        {
          headers: {
            "Content-Type": "application/json",
            authorization: token,
          }
        }
      );
      setInvalidLink(false)
      if (!response.data || response.data.length === 0) {
        setInvalidLink(true)
        window.location = '/'

        throw new Error("No exam details found");
      }

      if (response.data[0].length === 0) {
        setInvalidLink(true)
        window.location = '/'
      }
      const examDetails = response.data[0][0];
      // Format exam data according to the response structure
      const formattedExamData = {
        name: examDetails.quiz_name,
        description: examDetails.description,
        duration: examDetails.duration,
        total_questions: examDetails.noq,
        max_marks: examDetails.noq * (1), // Assuming 1 mark per question if not specified
        price: examDetails.quiz_price || examDetails.price || 0,
        examAvailable: examDetails.examAvailable === 1,
        examExpired: examDetails.examExpired === 1,
        start_date_time: examDetails.startDate,
        end_date_time: examDetails.endDate,
        isUpcoming: examDetails.isUpcoming === 1,
      };

      // Format attempt details
      const formattedAttemptDetails = {
        max_attempts: examDetails.maximum_attempts,
        remaining_attempts: examDetails.remainingAttempts,
        pass_percentage: examDetails.pass_percentage,
      };
      // Format access and payment details
      const hasAccess = true; // Since user_id is present in the response
      const hasPaid = examDetails.payment_id || examDetails.price === 0; // Assuming payment_id = 1 means paid

      // Determine exam status from start_date_time and end_date_time
      const currentTime = new Date();
      const startTime = new Date(examDetails.startDate);
      const endTime = new Date(examDetails.endDate);

      // Calculate time difference for upcoming exams
      const timeDiff = startTime - currentTime;
      const daysUntilStart = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
      const hoursUntilStart = Math.floor((timeDiff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      
      setTimeUntilStart({
        days: daysUntilStart,
        hours: hoursUntilStart
      });

      // Determine exam status
      let status;
      if (currentTime < startTime) {
        status = 'upcoming';
      } else if (currentTime >= startTime && currentTime <= endTime) {
        status = 'available';
      } else {
        status = 'expired';
      }

      setExamStartDate(startTime);
      setExamData(formattedExamData);
      setAttempts(formattedAttemptDetails);
      setAccess(hasAccess);
      setAllowNegativeMarking(examDetails.incorrect_score !== null && examDetails.incorrect_score < 0);
      setHasPaid(hasPaid);
      setExamStatus(status);
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching exam details:", error);
      setIsLoading(false);
    }
  };

  const getExamStatusChip = () => {
    switch (examStatus) {
      case 'available':
        return (
          <Chip
            icon={<EventIcon />}
            label="Exam Available"
            color="success"
            sx={{ mb: 2 }}
          />
        );
      case 'upcoming':
        return (
          <Chip
            icon={<EventIcon />}
            label={`Upcoming: Starts in ${timeUntilStart?.days} days ${timeUntilStart?.hours} hours`}
            color="info"
            sx={{ mb: 2 }}
          />
        );
      case 'expired':
        return (
          <Chip
            icon={<WarningIcon />}
            label="Exam Expired"
            color="error"
            sx={{ mb: 2 }}
          />
        );
      default:
        return (
          <Chip
            icon={<EventIcon />}
            label="Exam Not Available"
            color="warning"
            sx={{ mb: 2 }}
          />
        );
    }
  };

  const getExamStatusAlert = () => {
    if (examStatus === 'upcoming') {
      return (
        <Alert
          severity="info"
          sx={{ mt: 3 }}
        >
          <AlertTitle>Upcoming Exam</AlertTitle>
          <Typography variant="body1" gutterBottom>
            This exam will be available from {examStartDate?.toLocaleString()}
          </Typography>
          <Typography variant="body2" sx={{ color: 'primary.main' }}>
            Time until start: {timeUntilStart?.days} days and {timeUntilStart?.hours} hours
          </Typography>
        </Alert>
      );
    } else if (examStatus === 'expired') {
      return (
        <Alert severity="error" sx={{ mt: 3 }}>
          <AlertTitle>Exam Expired</AlertTitle>
          This exam has expired and cannot be attempted anymore.
        </Alert>
      );
    }
    return null;
  };

  const handlePayment = async () => {
    try {
      const token = Cookie.get("jwt_token");
      const uid = localStorage.getItem("num");
      const quizInf = window.atob(quid);
      const quizId = quid

      // Get user details
      const userResponse = await axios.post(
        `${commonData["api"]}/get-user-details/${uid}`,
        {},
        {
          headers: {
            authorization: token,
          }
        }
      );

      const userData = userResponse.data.result[0];
      const expiryDate = new Date();
      const expiry = 365;
      expiryDate.setDate(expiryDate.getDate() + expiry);

      const phoneNum = localStorage.getItem("userDetailsApp") || isWebView();

      const paymentData = {
        purpose: "Purchase of " + examData.name + " Package",
        email: userData.email,
        firstname: userData.first_name.split(" ")[0],
        txnid: uuidv4(),
        phone: userData.contact_no,
        amount: examData.price,
        redirect_url: `${commonData["api"]}${phoneNum ? "/payment_instamojo" : "/phonepe/status"
          }?user_id=${userData.contact_no}&gid=${quizId}&firstname=${userData.first_name.split(" ")[0]
          }&net_amount_debit=${examData.price}&expiryDate=${expiryDate.toISOString().slice(0, 10)
          }&fbw=${userData.userAmount || 0}&type=exam`,
        type: "exam"
      };
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
        "Access-Control-Allow-Origin": "*",
      };

      const response = await axios.post(
        `${commonData["api"]}/${phoneNum ? "instamojo/pay" : "phonepe/payment"}`,
        paymentData,
        { headers }
      );

      // Send admin notifications
      const adminNotificationData = {
        businessId: 1,
        verifyToken: Math.random() * 15000,
        message: [
          paymentData.firstname + "-" + paymentData.phone,
          quizId + "-" + examData.name,
          commonData["app"] + "/admin/users?userNum=" + paymentData.phone,
        ],
        messageType: "promotion",
        templateLang: "en",
        templateName: "user_payment_process_started_admin_notification",
      };

      // Send notification to first admin
      await axios.post(
        `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
        {
          ...adminNotificationData,
          phoneNumber: "9441687174",
        }
      );

      // Send notification to second admin
      await axios.post(
        `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
        {
          ...adminNotificationData,
          phoneNumber: "9492387460",
        }
      );

      // Redirect to payment page
      window.location.href = response.data;

    } catch (error) {
      console.error("Payment initiation failed:", error);
    }
  };

  const startExam = () => {
    const link = `/attempt-exam/${quid
      }$$$_${window.btoa(String(false))}`


    history.push(link);
  };

  const isWebView = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    return /wv|WebView/.test(userAgent);
  };

  useEffect(() => {
    fetchExamDetails();
  }, [quid]);

  // Update the Additional Info chip to handle upcoming status
  const getStatusLabel = (status) => {
    switch (status) {
      case 'available':
        return 'Exam Available';
      case 'upcoming':
        return 'Upcoming Exam';
      case 'expired':
        return 'Exam Expired';
      default:
        return 'Not Available';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'available':
        return <CheckCircleIcon />;
      case 'upcoming':
        return <EventIcon />;
      case 'expired':
        return <ErrorIcon />;
      default:
        return <ErrorIcon />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'upcoming':
        return 'info';
      case 'expired':
        return 'error';
      default:
        return 'warning';
    }
  };

  if (isLoading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <Loader />
      </Box>
    );
  }

  if (invalidLink) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <Typography variant="h4">Invalid link</Typography>
      </Box>
    );
  }
  return (
    <div className="desktopsidebar">
      <div className="desktopsidebarmenuexamdetails">
        <DesktopMenu />
      </div>
      <Header />

      <Divider color="white" />
      <div className="examdetailsdesktop">
        <Container 
          maxWidth="lg" 
          sx={{ 
            mt: { xs: 2, sm: 3, md: 4 },
            mb: { xs: 2, sm: 3, md: 4 },
            px: { xs: 1, sm: 2, md: 3 }
          }}
        >
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Paper
                elevation={0}
                sx={{
                  ...cardStyles.examDetailsCard,
                  p: { xs: 2, sm: 3, md: 4 },
                  backgroundColor: PREMIUM_COLORS.background.paper
                }}
              >
                <Box 
                  display="flex" 
                  flexDirection={{ xs: 'column', sm: 'row' }}
                  justifyContent="space-between" 
                  alignItems={{ xs: 'flex-start', sm: 'center' }}
                  gap={2}
                  mb={4}
                >
                  <Typography
                    variant="h4"
                    sx={{
                      fontWeight: 700,
                      color: PREMIUM_COLORS.primary.main,
                      fontSize: { xs: '1.5rem', md: '2rem' }
                    }}
                  >
                    {examData?.name}
                  </Typography>
                  {getExamStatusChip()}
                </Box>
                <Divider sx={{ mb: 4 }} />

                <Grid container spacing={{ xs: 2, md: 4 }}>
                  <Grid item xs={12} md={8}>
                    <Card sx={cardStyles.examDetailsCard}>
                      <CardContent sx={cardStyles.contentSection}>
                        {/* Header */}
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
                          <QuizIcon sx={{ color: '#1a237e', mr: 2, fontSize: 28 }} />
                          <Typography variant="h5" sx={{ color: '#1a237e', fontWeight: 500 }}>
                            Exam Details
                          </Typography>
                        </Box>

                        <Grid container spacing={4}>
                          {/* Basic Info Section */}
                          <Grid item xs={12}>
                            <Box sx={{
                              backgroundColor: '#f8f9fa',
                              p: 3,
                              borderRadius: 2,
                              border: '1px solid #e0e0e0'
                            }}>
                              <Grid container spacing={3}>
                                {/* Exam Name */}
                                <Grid item xs={12}>
                                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                                    <SubjectIcon sx={{ color: '#5c6bc0', mr: 2 }} />
                                    <Box>
                                      <Typography variant="subtitle2" color="text.secondary">
                                        Exam Name
                                      </Typography>
                                      <Typography variant="h6" color="text.primary" sx={{ mt: 0.5 }}>
                                        {examData?.name}
                                      </Typography>
                                    </Box>
                                  </Box>
                                </Grid>

                                {/* Syllabus */}
                                <Grid item xs={12}>
                                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                                    <MenuBookIcon sx={{ color: '#5c6bc0', mr: 2, mt: 0.5 }} />
                                    <Box>
                                      <Typography variant="subtitle2" color="text.secondary">
                                        Syllabus
                                      </Typography>
                                      <div
                                        dangerouslySetInnerHTML={{ __html: examData?.description }}
                                        style={{
                                          color: '#424242',
                                          marginTop: '8px',
                                          '& p': { margin: '8px 0' }
                                        }}
                                      />
                                    </Box>
                                  </Box>
                                </Grid>

                                {/* Dates Section */}
                                <Grid item xs={12}>
                                  <Box sx={{
                                    display: 'flex',
                                    gap: 4,
                                    mt: 2,
                                    p: 2,
                                    flexDirection: { xs: 'column', sm: 'row' },
                                    backgroundColor: '#fff',
                                    borderRadius: 1,
                                    border: '1px solid #e0e0e0'
                                  }}>
                                    {/* Start Date */}
                                    <Box>
                                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                        <CalendarTodayIcon sx={{ color: '#4caf50', mr: 1, fontSize: 20 }} />
                                        <Typography variant="subtitle2" color="text.secondary">
                                          Start Date & Time
                                        </Typography>
                                      </Box>
                                      <Typography variant="body1" sx={{ color: '#1a237e', fontWeight: 500 }}>
                                        {new Date(examData?.start_date_time).toLocaleString()}
                                      </Typography>
                                    </Box>

                                    {/* End Date */}
                                    <Box>
                                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                        <CalendarTodayIcon sx={{ color: '#f44336', mr: 1, fontSize: 20 }} />
                                        <Typography variant="subtitle2" color="text.secondary">
                                          End Date & Time
                                        </Typography>
                                      </Box>
                                      <Typography variant="body1" sx={{ color: '#1a237e', fontWeight: 500 }}>
                                        {new Date(examData?.end_date_time).toLocaleString()}
                                      </Typography>
                                    </Box>
                                  </Box>
                                </Grid>
                              </Grid>
                            </Box>
                          </Grid>

                          {/* Stats Grid */}
                          {/* <Grid item xs={12}>
                            <Box sx={{
                              display: 'grid',
                              gridTemplateColumns: { xs: '1fr', sm: '1fr 1fr', md: '1fr 1fr 1fr 1fr' },
                              gap: 3
                            }}>
                              {/* Duration 
                              <Box sx={{
                                p: 2,
                                borderRadius: 2,
                                backgroundColor: '#fff',
                                border: '1px solid #e0e0e0',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  boxShadow: 1,
                                  borderColor: '#1a237e20'
                                }
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <AccessTimeIcon sx={{ color: '#5c6bc0', mr: 1, fontSize: 20 }} />
                                  <Typography variant="subtitle2" color="text.secondary">
                                    Duration
                                  </Typography>
                                </Box>
                                <Typography variant="h6" color="text.primary">
                                  {examData?.duration} minutes
                                </Typography>
                              </Box>

                              {/* Questions 
                              <Box sx={{
                                p: 2,
                                borderRadius: 2,
                                backgroundColor: '#fff',
                                border: '1px solid #e0e0e0',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  boxShadow: 1,
                                  borderColor: '#1a237e20'
                                }
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <QuizIcon sx={{ color: '#5c6bc0', mr: 1, fontSize: 20 }} />
                                  <Typography variant="subtitle2" color="text.secondary">
                                    Total Questions
                                  </Typography>
                                </Box>
                                <Typography variant="h6" color="text.primary">
                                  {examData?.total_questions}
                                </Typography>
                              </Box>

                              {/* Maximum Marks  
                              <Box sx={{
                                p: 2,
                                borderRadius: 2,
                                backgroundColor: '#fff',
                                border: '1px solid #e0e0e0',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  boxShadow: 1,
                                  borderColor: '#1a237e20'
                                }
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <EmojiEventsIcon sx={{ color: '#5c6bc0', mr: 1, fontSize: 20 }} />
                                  <Typography variant="subtitle2" color="text.secondary">
                                    Maximum Marks
                                  </Typography>
                                </Box>
                                <Typography variant="h6" color="text.primary">
                                  {examData?.max_marks}
                                </Typography>
                              </Box>

                              {/* Pass Percentage  
                              <Box sx={{
                                p: 2,
                                borderRadius: 2,
                                backgroundColor: '#fff',
                                border: '1px solid #e0e0e0',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                  boxShadow: 1,
                                  borderColor: '#1a237e20'
                                }
                              }}>
                                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                                  <QuizIcon sx={{ color: '#5c6bc0', mr: 1, fontSize: 20 }} />
                                  <Typography variant="subtitle2" color="text.secondary">
                                    Pass Percentage
                                  </Typography>
                                </Box>
                                <Typography variant="h6" color="text.primary">
                                  {attempts?.pass_percentage}%
                                </Typography>
                              </Box>
                            </Box>
                          </Grid> */}

                          {/* Additional Info */}
                          <Grid item xs={12}>
                            <Box sx={{ display: 'flex', gap: 2, mt: 1 }}>
                              {allowNegativeMarking && (
                                <Chip
                                  icon={<WarningIcon />}
                                  label="Negative Marking"
                                  color="warning"
                                  variant="outlined"
                                />
                              )}
                              <Chip
                                icon={getStatusIcon(examStatus)}
                                label={getStatusLabel(examStatus)}
                                color={getStatusColor(examStatus)}
                                variant="outlined"
                              />
                            </Box>
                          </Grid>
                        </Grid>
                      </CardContent>
                    </Card>

                    {hasPaid ? (
                      <Card sx={{ boxShadow: 2 }}>
                        {
                          examStatus === 'available' && (
                            <div className="main-table-container exam-details-instructions">
                              <div>
                                <p className="instr-title">సూచనలు</p>
                                <p>
                                  ఆన్ లైన్ పరీక్షలు రాయడంపై అవగాహన కలిగించేందుకు, ఆన్ లైన్
                                  పరీక్షలను అభ్యాసం చేసేందుకు ఈ పరీక్షలు ఉద్దేశించబడ్డాయి.
                                  వివిధ పోటీ పరీక్షలకు ఉపయుక్తమైన ఆన్ లైన్ టెస్ట్ లను అందించే
                                  ఈ వెబ్ సైట్ లో ఆన్ లైన్ టెస్ట్ ను రాసే ముందు సూచనలను తప్పక
                                  చదవండి.
                                </p>
                                <p>
                                  * ముందుగా వెబ్ సైట్ లో రిజిస్టర్ చేసుకుని, లాగిన్ కావడం
                                  ద్వారా ప్రవేశించాలి.
                                </p>
                                <p>* కోరిన టెస్ట్ ను రాయడానికి సిద్ధమవ్వాలి.</p>
                                <p>
                                  * ప్రతి టెస్ట్ కు టైమ్ లిమిట్ ఉంటుంది. కనుక కేటాయించిన
                                  సమయంలోనే సమాధానాలను గుర్తించేందుకు ప్రయత్నించండి. సమయం
                                  ముగిసిన తరువాత ప్రశ్నాపత్రం దానంతట అదే ముగించబడుతుంది. కనుక
                                  సమయపాలన ముఖ్యం.
                                </p>
                                <p>* ప్రతి ప్రశ్నకు నాలుగు ఐచ్చికాలు ఇవ్వబడతాయి. </p>
                                <p>* ప్రశ్నను శ్రద్దగా చదవండి.</p>
                                <p>
                                  * ఐచ్చికాలలో సరైనది ఎంచుకుండి దానికి ఎదురుగా గల రేడియో బటన్
                                  పై క్లిక్ చేయడం ద్వారా సమాధానం ఎంచుకోండి.
                                </p>
                                <p>
                                  * సమాధానం ఎంచుకోవడం పూర్తయితే{" "}
                                  <button className="savebtn detailsbtn">
                                    Save &amp; Next
                                  </button>
                                  పై క్లిక్ చేయండి.
                                </p>
                                <p>
                                  * ఇలా సమాధానం గుర్తించబడిన ప్రశ్న యొక్క సంఖ్య ఆకుపచ్చ రంగులో
                                  కనిపిస్తుంది.
                                </p>
                                <p>
                                  * ఒకవేళ సమాధానం తరువాత గుర్తించాలనుకుంటే{" "}
                                  <button className="reviewbtn detailsbtn">
                                    Review Later
                                  </button>{" "}
                                  పై క్లిక్ చేయాలి.
                                </p>
                                <p>
                                  * తరువాత రివ్యూ చేయాలనుకున్న ప్రశ్న సంఖ్య నారింజ రంగులో
                                  కనిపిస్తుంది.
                                </p>
                                <p>
                                  * సమాధానాన్ని గుర్తించని ప్రశ్న యొక్క సంఖ్య ఎరుపు రంగులోనూ,
                                  ఇంకా వీక్షించని ప్రశ్న యొక్క సంఖ్య నలుపు రంగులోనూ
                                  కనిపిస్తుంది.
                                </p>
                                <p>
                                  * వరుసగా అన్ని ప్రశ్నలకు సమాధానాలను గుర్తించిన తరువాత{" "}
                                  <button className="submitbtn detailsbtn">Submit</button> పై
                                  క్లిక్ చేయడం ద్వారా టెస్ట్ రాయడాన్ని పూర్తి చేయవచ్చు.
                                </p>
                                <p>
                                  * పరీక్ష రాసిన తరువాత ఫలితాలు విశ్లేషణాత్మకంగా కనిపిస్తాయి.
                                  దీనిలో మీరు గుర్తించిన సమాధానాలు, సరైన సమాధానాలు, మీరు
                                  సాధించిన మార్కులు, ఇప్పటివరకూ ఆ టెస్ట్ రాసిన వారిలో మీ
                                  ర్యాంక్, మీ స్కోరు పొజిషన్, టాప్ ర్యాంకర్ల వివరాలు అక్కడ
                                  పొందుపరచబడతాయి.
                                </p>
                                <p>
                                  * పరీక్షలో ప్రశ్నలు ఎంతో జాగ్రత్త వహిస్తూ రూపొందించినప్పటికీ
                                  తప్పులు దొర్లే అవకాశం లేకపోలేదు.
                                </p>
                                <p>
                                  * కనుక వీటిని ప్రామాణికంగా తీసుకోక, కేవలం అభ్యాసం కోసం
                                  మాత్రమే వీటిని రూపొందించామని గమనించగలరు.
                                </p>
                                <p>
                                  * ఈ టెస్ట్లలో అడిగే ప్రశ్నలలో దొర్లే తప్పులకు మేము
                                  న్యాయపరమైన బాధ్యత వహించలేము.
                                </p>
                                <p>
                                  <font color="red">
                                    * పెయిడ్ టెస్ట్ ల విషయంలో కానీ, అమౌంట్ పేమెంట్స్ విషయంలో కానీ ఏదైనా లోపం ఎదురైనట్లయితే మీ అమౌంట్ ను తిరిగి పంపించే
                                    బాధ్యత మాది.
                                  </font>
                                </p>
                                <p>
                                  <i>
                                    * లాభాపేక్ష, ధనార్జన కాకుండా అందరికీ అందుబాటులో సదుపాయాలను{" "}
                                    <b>అతి తక్కువ రుసుముతో లేదా ఉచితంగా</b> అందించాలన్న
                                    సత్సంకల్పంతో ప్రారంభమైన నవచైతన్య కాంపిటీషన్స్ - ఆన్ లైన్
                                    ఎగ్జామ్స్ పోర్టల్ కు మీ సలహాలు, సూచనలు అందించండి.
                                  </i>
                                </p>
                              </div>

                            </div>
                          )
                        }
                        <CardContent sx={{ p: 4 }}>
                          {attempts && attempts.remaining_attempts > 0 ? (
                            <Box>
                              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                                <PlayCircleOutlineIcon sx={{ color: '#2e7d32', mr: 2, fontSize: 28 }} />
                                <Typography variant="h5" sx={{ color: '#2e7d32', fontWeight: 500 }}>
                                  Ready to Start Your Exam
                                </Typography>
                              </Box>

                              <Box sx={{
                                display: 'flex',
                                gap: 4,
                                mb: 4,
                                backgroundColor: '#f5f5f5',
                                p: 3,
                                borderRadius: 2
                              }}>
                                <Box>
                                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                    Maximum Attempts
                                  </Typography>
                                  <Typography variant="h6" color="text.primary">
                                    {attempts.max_attempts}
                                  </Typography>
                                </Box>
                                <Box>
                                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                                    Remaining Attempts
                                  </Typography>
                                  <Typography variant="h6" color="text.primary" sx={{ color: attempts.remaining_attempts <= 2 ? '#ed6c02' : 'inherit' }}>
                                    {attempts.remaining_attempts}
                                  </Typography>
                                </Box>
                              </Box>

                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                <Button
                                  variant="contained"
                                  color={examStatus === 'upcoming' ? 'info' : 'primary'}
                                  size="large"
                                  onClick={startExam}
                                  disabled={!access || examStatus !== 'available'}
                                  startIcon={examStatus === 'upcoming' ? <EventIcon /> : <PlayArrowIcon />}
                                  sx={{
                                    textTransform: 'none',
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 2,
                                    boxShadow: 2,
                                    '&:hover': {
                                      boxShadow: 4
                                    }
                                  }}
                                >
                                  {examStatus === 'available' ? 'Start Exam' :
                                   examStatus === 'upcoming' ? `Opens in ${timeUntilStart?.days}d ${timeUntilStart?.hours}h` :
                                   examStatus === 'expired' ? 'Exam Expired' : 'Exam Not Available'}
                                </Button>

                              </Box>

                              {getExamStatusAlert()}
                            </Box>
                          ) : (
                            <Alert severity="error" sx={{ display: 'flex', alignItems: 'center' }}>
                              <AlertTitle>No Attempts Remaining</AlertTitle>
                              You have used all available attempts for this exam.
                            </Alert>
                          )}
                        </CardContent>

                      </Card>
                    ) : (
                      <Card sx={{ boxShadow: 2 }}>
                        <Box
                          sx={{
                            mt: 3,
                            p: 3,
                            bgcolor: 'background.paper',
                            borderRadius: 2,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            border: '1px solid rgba(0,0,0,0.08)'
                          }}
                        >
                          {/* Header */}
                          <Box
                            sx={{
                              display: 'flex',
                              alignItems: 'center',
                              pb: 2,
                              borderBottom: '1px solid rgba(0,0,0,0.1)',
                              mb: 2
                            }}
                          >
                            <PaymentIcon sx={{ mr: 2, color: '#1a237e', fontSize: 28 }} />
                            <Typography
                              variant="h6"
                              sx={{
                                color: '#1a237e',
                                fontWeight: 600
                              }}
                            >
                              Payment Details
                            </Typography>
                          </Box>

                          {/* Price Information */}
                          <Box sx={{ mb: 3 }}>
                            <Box
                              sx={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                mb: 1
                              }}
                            >
                              <Typography variant="body1" color="text.secondary">
                                Exam Price
                              </Typography>
                              <Typography
                                variant="h6"
                                sx={{
                                  color: '#1a237e',
                                  fontWeight: 500
                                }}
                              >
                                ₹{examData?.price}
                              </Typography>
                            </Box>
                          </Box>

                          {/* Action Button */}
                          <Box
                            sx={{
                              display: 'flex',
                              justifyContent: 'center',
                              pt: 2,
                              borderTop: '1px solid rgba(0,0,0,0.1)'
                            }}
                          >
                            <Button
                              variant="contained"
                              color="primary"
                              size="large"
                              onClick={handlePayment}
                              disabled={examStatus === 'expired'}
                              sx={{
                                textTransform: 'none',
                                px: 6,
                                py: 1.5,
                                borderRadius: 2,
                                bgcolor: '#1a237e',
                                '&:hover': {
                                  bgcolor: '#0d47a1',
                                },
                                '&:disabled': {
                                  bgcolor: 'rgba(0,0,0,0.12)',
                                }
                              }}
                              startIcon={<PaymentIcon />}
                            >
                              {examStatus === 'expired' ? 'Exam Expired' : 'Pay Now'}
                            </Button>
                          </Box>

                          {examStatus === 'expired' && (
                            <Typography
                              variant="body2"
                              color="error"
                              sx={{
                                mt: 2,
                                textAlign: 'center',
                                bgcolor: 'error.light',
                                color: 'error.dark',
                                p: 1,
                                borderRadius: 1
                              }}
                            >
                              This exam has expired and is no longer available for purchase.
                            </Typography>
                          )}
                        </Box>
                      </Card>
                    )}
                  </Grid>

                </Grid>


              </Paper>
            </Grid>
          </Grid>
        </Container>
      </div>
    </div>
  );
};

export default ExamDetailsPaid;
