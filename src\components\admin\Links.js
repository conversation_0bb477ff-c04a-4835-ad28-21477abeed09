import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import <PERSON>ie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
import TextField from "@mui/material/TextField";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import Checkbox from "@mui/material/Checkbox";
import "./styles.css";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
class Links extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    dataLink: [],
    page: 0,
    popUpOpen: false,
    couponId: null,
    editorLoading: false,
    search: "",
    dataCount: 0,
    linkName: "",
    popupType: "",
    link: "",
    searchClicked: false,
    category: "",
  };

  componentDidMount() {
    const { match } = this.props;
    const { params } = match;
    const { category } = params;
    this.setState({ category: category }, this.getData);
  }

  getData = async () => {
    const { page, search, category } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: category + "LINKS",
      search: search,
      qid: page * 25,
    };
    console.log(body);
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data);
      this.setState({
        dataLink: data.data[0],
        dataCount: data.data[1][0].count,
        isLoading: false,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  searchlink = (e) => {
    e.preventDefault();
    const { search } = this.state;

    if (search === "") {
      NotificationManager.error(`Please Enter Search Value`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
      });
      this.getData();
    }
  };
  categoriesTable = () => {
    const { dataLink, dataCount, searchClicked, search, category } = this.state;
    // console.log(dataLink);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All {category} Links</h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            {!searchClicked ? (
              <form
                onSubmit={this.searchlink}
                style={{ cursor: "pointer", display: "flex", marginBottom: 10 }}
              >
                <input
                  type={"text"}
                  value={search}
                  id="search"
                  onChange={(e) => this.setState({ search: e.target.value })}
                  placeholder="Enter Name"
                />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    // marginLeft: 5,
                    backgroundColor: "green",
                    padding: 5,
                    color: "#fff",
                    borderRadius: 10,
                  }}
                >
                  <button
                    type="submit"
                    style={{
                      backgroundColor: "transparent",
                      borderWidth: 0,
                      color: "#fff",
                    }}
                  >
                    <i className="bi bi-search"></i>
                  </button>
                </div>
              </form>
            ) : (
              <i
                className="bi bi-search"
                style={{
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                }}
                onClick={() => {
                  this.setState(
                    {
                      search: "",
                      searchClicked: false,
                      isLoading: true,
                    },
                    this.getData
                  );
                }}
              >
                Search Again
              </i>
            )}
          </div>
          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  popUpOpen: !p.popUpOpen,
                  linkName: "",
                  popupType: "",
                  link: "",

                  popupType: `ADD NEW ${category.toUpperCase()} LINK`,
                }));
              }}
            >
              Add New {category}
            </Button>
          </div>
        </div>
        <table>
          <thead>
            <tr>
              <th>Name</th>

              <th style={{ textAlign: "center" }}>Action</th>
            </tr>
          </thead>
          <tbody>
            {dataLink.length > 0 ? (
              dataLink.map((e, i) => {
                return (
                  <tr key={"Coupon" + e.int_uid}>
                    <td>
                      <div style={{ display: "flex" }}>
                        <p>{e.int_uid + ") " + e.group_name}</p>
                      </div>
                    </td>

                    <td style={{ textAlign: "center" }}>
                      <a href={e.drive_link} target="_blank">
                        <i
                          className="bi bi-download"
                          style={{
                            cursor: "pointer",
                            marginRight: 20,
                            color: "black",
                          }}
                        ></i>
                      </a>
                      <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 20, cursor: "pointer" }}
                        onClick={() =>
                          this.setState({
                            popUpOpen: true,
                            editorLoading: true,
                            couponId: e.int_uid,
                            linkName: e.group_name,
                            link: e.drive_link,
                            popupType: `EDIT ${category.toUpperCase()}`,
                          })
                        }
                      ></i>
                      <i
                        className="bi bi-trash-fill"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          this.setState(
                            {
                              couponId: e.int_uid,
                            },
                            () => this.deleteCoupon()
                          )
                        }
                      ></i>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: 20,
                }}
              >
                <td colSpan={4}>
                  <p style={{ color: "black", textAlign: "center" }}>
                    No {category} Links are Available...
                  </p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {this.renderPaginationButtons(dataCount)}
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      couponId: "",
      linkName: "",
      popupType: "",
    }));
  };
  onChangePackageData = (e) => {
    console.log(e);
    const { packagesAllList } = this.state;
    const allGids = packagesAllList.map((e) => e.gid).join(",");
    // if (e.target.value === "all") {
    //   this.setState({
    //     selectedPackages: allGids.split(","),
    //   });
    // } else {
    this.setState({
      selectedPackages:
        typeof value === "string" ? e.target.value.split(",") : e.target.value,
    });
    // }
  };

  renderPopUp = () => {
    const { popUpOpen, linkName, popupType, link, category } = this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <TextField
            required
            className="input-box register catgroy"
            label="Name"
            variant="filled"
            value={linkName}
            onChange={(e) => this.setState({ linkName: e.target.value })}
            focused
          />
          <TextField
            required
            className="input-box register catgroy"
            label="Link"
            variant="filled"
            value={link}
            onChange={(e) => this.setState({ link: e.target.value })}
            focused
          />
        </DialogContent>
        <DialogActions
          style={{ display: "flex", justifyContent: "center", marginLeft: -10 }}
        >
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={
              popupType === `EDIT ${category.toUpperCase()}`
                ? this.saveLink
                : this.addLink
            }
          >
            Save changes
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  deleteCoupon = async () => {
    const { couponId, dataLink, category } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: `LinksDelete`,
      search: "",
      qid: couponId,
    };
    console.log(body);
    if (
      window.confirm(`Do you really want to remove this ${category} Link ?`)
    ) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        console.log(data);
        this.setState(
          {
            dataLink: dataLink.filter((e) => e.int_uid != couponId),
          },
          NotificationManager.success(`${category} Link Deleted Succesfully...`)
        );
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  renderPaginationButtons = (totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    console.log(paidcount);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount - 1}
        >
          Next
        </Button>
      </div>
    );
  };
  saveLink = async () => {
    const { couponId, dataLink, linkName, link, category } = this.state;
    // console.log(selectedPackages);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "LinkEdit",
      search: linkName + "link%%$$" + link,
      qid: couponId,
    };
    console.log(body);
    if (linkName === "") {
      NotificationManager.error(`Please Enter ${category} Name`);
    } else if (link === "") {
      NotificationManager.error(`Please Enter ${category} Link `);
    } else {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );

        // console.log(data.data[0][0].result);

        this.setState(
          {
            popUpOpen: false,
          },
          () => {
            this.getData();
            NotificationManager.success(
              `${category} Link Updated Succesfully...`
            );
          }
        );
      } catch (err) {
        console.log(err);
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  addLink = async () => {
    const { dataLink, linkName, link, category } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: category + "Add",
      search: linkName + "link%%$$" + link,
      qid: 0,
    };
    if (linkName === "") {
      NotificationManager.error(`Please Enter Name`);
    } else if (link === "") {
      NotificationManager.error(`Please Enter Link`);
    } else {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        console.log(data);

        if (data.data[0][0].result === "yes") {
          this.setState(
            {
              popUpOpen: false,
            },
            () => {
              this.getData();
              NotificationManager.success(
                `${category} Link Updated Succesfully...`
              );
            }
          );
        } else {
          NotificationManager.error(`${category} Name Already Exists..`);
        }
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  render() {
    const { isLoading, login, popUpOpen } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.categoriesTable()}
                {popUpOpen && this.renderPopUp()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default Links;
