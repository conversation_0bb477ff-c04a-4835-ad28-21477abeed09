import React, { Component } from 'react';
import {
  <PERSON><PERSON>,
  TextField,
  CircularProgress,
  Card,
  CardContent,
  Typography,
  Box,
  Alert,
  IconButton,
  Tooltip,
  LinearProgress
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Delete as DeleteIcon,
  ContentCopy as CopyIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  PlayArrow as PlayArrowIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import * as tus from 'tus-js-client';
import axios from 'axios';
import {
  NotificationManager,
} from "react-notifications";

class CloudflareStreamManager extends Component {
  constructor(props) {
    super(props);
    this.state = {
      // Configuration
      accountId: "18aad3dec0d70ebfd4bb8d0f16ced4ec",
      apiToken: "XZhz23B4STt6Is_iFTFYeNPXixdHK31l20PZ_8QY",
      
      // Upload states
      selectedFile: null,
      isUploading: false,
      uploadProgress: 0,
      tusUpload: null,
      
      // Video states
      videoId: null,
      videoDetails: null,
      isLoadingDetails: false,
      
      // Preview states
      showPreview: false,
      isGeneratingPreview: false,
      signedPreviewUrl: null,
      
      // UI states
      showManualInput: false,
      manualVideoId: '',
      error: null,
      success: null,
    };
  }

  componentDidMount() {
    if (this.props.existingVideoId) {
      this.setState({ videoId: this.props.existingVideoId }, () => {
        this.loadVideoDetails();
      });
    }
  }

  componentDidUpdate(prevProps) {
    if (prevProps.existingVideoId !== this.props.existingVideoId && this.props.existingVideoId) {
      this.setState({ videoId: this.props.existingVideoId }, () => {
        this.loadVideoDetails();
      });
    }
  }

  // File selection handler
  handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type according to Cloudflare docs
    const validTypes = [
      'video/mp4', 'video/quicktime', 'video/x-msvideo', 
      'video/x-matroska', 'video/webm', 'video/x-flv',
      'video/mp2t', 'video/3gpp'
    ];
    
    if (!validTypes.includes(file.type)) {
      this.showError('Please upload a valid video file (MP4, MOV, AVI, MKV, WebM, FLV, 3GP)');
      return;
    }

    // Validate file size (max 30GB as per Cloudflare docs)
    const maxSize = 30 * 1024 * 1024 * 1024; // 30GB
    if (file.size > maxSize) {
      this.showError('File size must be less than 30GB');
      return;
    }

    // Clear any previous upload state and set new file
    this.setState({ 
      selectedFile: file, 
      error: null,
      success: null,
      uploadProgress: 0,
      tusUpload: null,
      // Reset videoId unless it's from existing props
      videoId: this.props.existingVideoId || null,
      videoDetails: this.props.existingVideoId ? this.state.videoDetails : null
    });
  };

  // Upload video using TUS protocol with fallback to basic upload
  uploadVideo = async () => {
    const { selectedFile } = this.state;
    
    if (!selectedFile) {
      this.showError('Please select a video file first');
      return;
    }

    this.setState({ isUploading: true, uploadProgress: 0, error: null, success: null });

    // Validate credentials first
    const credentialsValid = await this.validateCredentials();
    if (!credentialsValid) {
      this.showError('Invalid Cloudflare credentials. Please check your account ID and API token.');
      this.setState({ isUploading: false });
      return;
    }

    // Try TUS upload first, fallback to basic upload if it fails
    this.uploadWithTUS()
      .catch((error) => {
        console.warn('TUS upload failed, trying basic upload:', error);
        this.showError('TUS upload failed, trying alternative method...');
        return this.uploadWithBasicMethod();
      })
      .catch((error) => {
        console.error('All upload methods failed:', error);
        this.showError(`Upload failed: ${error.message}`);
        this.setState({ 
          isUploading: false,
          uploadProgress: 0,
          tusUpload: null
        });
      });
  };

  // TUS upload implementation
  uploadWithTUS = () => {
    return new Promise((resolve, reject) => {
      const { selectedFile, accountId, apiToken } = this.state;

      // Create metadata for upload
      const metadata = {
        name: selectedFile.name,
        filetype: selectedFile.type,
      };

      // Create TUS upload with proper Cloudflare configuration
      const upload = new tus.Upload(selectedFile, {
        endpoint: `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream`,
        headers: {
          'Authorization': `Bearer ${apiToken}`,
        },
        metadata: metadata,
        chunkSize: 5 * 1024 * 1024, // 5MB chunks for better reliability
        retryDelays: [0, 1000, 3000, 5000],
        
        onError: (error) => {
          console.error('TUS Upload error:', error);
          this.setState({ tusUpload: null });
          reject(error);
        },
        
        onProgress: (bytesUploaded, bytesTotal) => {
          const progress = Math.round((bytesUploaded / bytesTotal) * 100);
          this.setState({ uploadProgress: progress });
        },
        
        onSuccess: () => {
          console.log('TUS Upload completed successfully');
          this.setState({ tusUpload: null });
          resolve();
        },

        onAfterResponse: (req, res) => {
          // Get video ID from response header
          const videoId = res.getHeader('stream-media-id');
          if (videoId) {
            console.log('Video ID received:', videoId);
            this.setState({ videoId });
            
            // Update video settings to require signed URLs
            setTimeout(() => this.updateVideoSettings(videoId), 1000);
            
            // Notify parent component
            if (this.props.onVideoUploaded) {
              this.props.onVideoUploaded(videoId);
            }
          }
        }
      });

      this.setState({ tusUpload: upload });
      upload.start();
    });
  };

  // Basic upload method as fallback
  uploadWithBasicMethod = () => {
    return new Promise(async (resolve, reject) => {
      try {
        const { selectedFile, accountId, apiToken } = this.state;

        // Create FormData for basic upload
        const formData = new FormData();
        formData.append('file', selectedFile);

        // Create XMLHttpRequest for progress tracking
        const xhr = new XMLHttpRequest();

        // Set up progress tracking
        xhr.upload.addEventListener('progress', (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 100);
            this.setState({ uploadProgress: progress });
          }
        });

        // Set up response handlers
        xhr.addEventListener('load', () => {
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              if (response.success && response.result) {
                const videoId = response.result.uid;
                console.log('Basic upload completed, Video ID:', videoId);
                
                this.setState({ 
                  videoId,
                  isUploading: false,
                  uploadProgress: 100
                });

                // Update video settings to require signed URLs
                setTimeout(() => this.updateVideoSettings(videoId), 1000);
                
                // Notify parent component
                if (this.props.onVideoUploaded) {
                  this.props.onVideoUploaded(videoId);
                }

                this.showSuccess('Video uploaded successfully!');
                
                // Load video details
                setTimeout(() => this.loadVideoDetails(), 2000);
                
                resolve();
              } else {
                reject(new Error('Upload response indicates failure'));
              }
            } catch (parseError) {
              reject(new Error('Failed to parse upload response'));
            }
          } else {
            reject(new Error(`Upload failed with status: ${xhr.status}`));
          }
        });

        xhr.addEventListener('error', () => {
          reject(new Error('Network error during upload'));
        });

        xhr.addEventListener('timeout', () => {
          reject(new Error('Upload timeout'));
        });

        // Set timeout (30 minutes for large files)
        xhr.timeout = 30 * 60 * 1000;

        // Configure and send request
        xhr.open('POST', `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream`);
        xhr.setRequestHeader('Authorization', `Bearer ${apiToken}`);
        
        xhr.send(formData);

      } catch (error) {
        reject(error);
      }
    });
  };

  // Pause upload (only works with TUS)
  pauseUpload = () => {
    const { tusUpload } = this.state;
    if (tusUpload) {
      tusUpload.abort();
      this.setState({ isUploading: false });
      this.showSuccess('Upload paused');
    } else {
      this.showError('Cannot pause this upload method');
    }
  };

  // Resume upload (only works with TUS)
  resumeUpload = () => {
    const { tusUpload } = this.state;
    if (tusUpload) {
      this.setState({ isUploading: true });
      tusUpload.start();
      this.showSuccess('Upload resumed');
    } else {
      this.showError('Cannot resume this upload method');
    }
  };

  // Cancel upload
  cancelUpload = () => {
    const { tusUpload } = this.state;
    if (tusUpload) {
      tusUpload.abort();
    }
    this.setState({ 
      isUploading: false,
      uploadProgress: 0,
      tusUpload: null,
      selectedFile: null
    });
    this.showSuccess('Upload cancelled');
  };

  // Retry upload function
  retryUpload = () => {
    this.setState({ 
      error: null,
      success: null 
    });
    this.uploadVideo();
  };

  // Clear file selection
  clearFileSelection = () => {
    this.setState({
      selectedFile: null,
      uploadProgress: 0,
      tusUpload: null,
      error: null,
      success: null,
      // Also clear videoId if it's from a file upload (not from props)
      videoId: this.props.existingVideoId || null,
      videoDetails: this.props.existingVideoId ? this.state.videoDetails : null
    });
  };

  // Load video details using Cloudflare Stream API
  loadVideoDetails = async () => {
    const { videoId, accountId, apiToken } = this.state;
    
    if (!videoId) return;

    this.setState({ isLoadingDetails: true, error: null });

    try {
      const response = await axios.get(
        `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream/${videoId}`,
        {
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        const videoDetails = response.data.result;
        this.setState({ videoDetails });
        
        // Notify parent component
        if (this.props.onVideoDetailsLoaded) {
          this.props.onVideoDetailsLoaded(videoDetails);
        }
      } else {
        throw new Error('Failed to load video details');
      }
    } catch (error) {
      console.error('Error loading video details:', error);
      this.showError('Failed to load video details. Please check the video ID.');
    } finally {
      this.setState({ isLoadingDetails: false });
    }
  };

  // Delete video using Cloudflare Stream API
  deleteVideo = async () => {
    const { videoId, accountId, apiToken } = this.state;
    
    if (!videoId) {
      this.showError('No video ID available for deletion');
      return;
    }

    const confirmDelete = window.confirm(
      'Are you sure you want to delete this video? This action cannot be undone.'
    );

    if (!confirmDelete) return;

    this.setState({ isLoadingDetails: true, error: null });

    try {
      console.log('Attempting to delete video:', videoId);
      
      const response = await axios.delete(
        `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream/${videoId}`,
        {
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('Delete response:', response.data);

      // Check if the response indicates success
      if (response.data && response.data.success === true) {
        this.showSuccess('Video deleted successfully');
        this.setState({
          videoId: null,
          videoDetails: null,
          selectedFile: null,
          showPreview: false,
          signedPreviewUrl: null,
          isLoadingDetails: false
        });
        
        // Notify parent component
        if (this.props.onVideoDeleted) {
          this.props.onVideoDeleted();
        }
      } else {
        // Handle Cloudflare API errors
        const errorMessage = response.data?.errors?.[0]?.message || 'Unknown error occurred';
        console.error('Cloudflare API error:', response.data?.errors);
        throw new Error(`Cloudflare API error: ${errorMessage}`);
      }
    } catch (error) {
      console.error('Error deleting video:', error);
      this.setState({ isLoadingDetails: false });
      
      // Provide detailed error message
      let errorMessage = 'Failed to delete video';
      
      if (error.response) {
        // HTTP error response
        const status = error.response.status;
        const cfErrors = error.response.data?.errors;
        
        if (status === 404) {
          errorMessage = 'Video not found. It may have been already deleted.';
        } else if (status === 403) {
          errorMessage = 'Access denied. Please check your API token permissions.';
        } else if (status === 401) {
          errorMessage = 'Unauthorized. Please check your API token.';
        } else if (cfErrors && cfErrors.length > 0) {
          errorMessage = `Cloudflare error: ${cfErrors[0].message}`;
        } else {
          errorMessage = `HTTP ${status}: ${error.response.statusText}`;
        }
      } else if (error.request) {
        // Network error
        errorMessage = 'Network error. Please check your internet connection.';
      } else {
        // Other error
        errorMessage = error.message || 'Unknown error occurred';
      }
      
      this.showError(errorMessage);
    }
  };

  // Handle manual video ID input
  handleManualVideoId = () => {
    const { manualVideoId } = this.state;
    
    if (!manualVideoId.trim()) {
      this.showError('Please enter a valid video ID');
      return;
    }

    // Validate video ID format (32 character hex string)
    const videoIdRegex = /^[a-f0-9]{32}$/i;
    if (!videoIdRegex.test(manualVideoId.trim())) {
      this.showError('Invalid video ID format. Must be a 32-character hexadecimal string.');
      return;
    }

    this.setState({ 
      videoId: manualVideoId.trim(),
      showManualInput: false,
      manualVideoId: ''
    }, () => {
      this.loadVideoDetails();
      // Also update settings for manually entered videos to ensure security
      this.updateVideoSettings(manualVideoId.trim());
    });
  };

  // Get streaming URL for parent component
  getStreamingUrl = () => {
    const { videoDetails } = this.state;
    return videoDetails?.playback?.hls || null;
  };

  // Update video settings to require signed URLs
  updateVideoSettings = async (videoId) => {
    const { accountId, apiToken } = this.state;
    
    if (!videoId) return;

    try {
      // First check if video exists and is ready
      const checkResponse = await axios.get(
        `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream/${videoId}`,
        {
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (!checkResponse.data.success) {
        console.warn('Video not found or not ready yet, will retry later');
        // Retry after a delay
        setTimeout(() => this.updateVideoSettings(videoId), 5000);
        return;
      }

      // Update video settings
      const response = await axios.post(
        `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream/${videoId}`,
        {
          requireSignedURLs: true,
          allowedOrigins: ['exams.navachaitanya.net'],
          meta: {
            name: this.state.selectedFile?.name || 'Uploaded Video'
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (response.data.success) {
        console.log('Video settings updated successfully - Signed URLs required and origin restricted');
        // this.showSuccess('Video secured with signed URLs and domain restrictions');
        
        // Refresh video details to show updated settings
        setTimeout(() => this.loadVideoDetails(), 1000);
      } else {
        console.warn('Failed to update video settings:', response.data.errors);
        this.showError('Failed to update security settings');
      }
    } catch (error) {
      console.error('Error updating video settings:', error);
      
      // If it's a 404, the video might not be ready yet
      if (error.response && error.response.status === 404) {
        console.log('Video not ready yet, will retry in 5 seconds');
        setTimeout(() => this.updateVideoSettings(videoId), 5000);
      } else {
        this.showError('Failed to update security settings');
      }
    }
  };

  // Validate credentials before upload
  validateCredentials = async () => {
    const { accountId, apiToken } = this.state;
    
    try {
      const response = await axios.get(
        `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream`,
        {
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );
      
      return response.data.success;
    } catch (error) {
      console.error('Credential validation failed:', error);
      return false;
    }
  };

  // Preview methods
  generateSignedUrl = async () => {
    const { accountId, apiToken, videoId } = this.state;
    
    if (!videoId) {
      this.showError('No video ID available for preview');
      return;
    }

    this.setState({ isGeneratingPreview: true, error: null });

    try {
      // Create a signed token for viewing
      const tokenResponse = await axios.post(
        `https://api.cloudflare.com/client/v4/accounts/${accountId}/stream/${videoId}/token`,
        {
          // Set expiration for 1 hour from now
          exp: Math.floor(Date.now() / 1000) + 3600,
          // Allow download permission for signed URLs
          downloadable: false
        },
        {
          headers: {
            'Authorization': `Bearer ${apiToken}`,
            'Content-Type': 'application/json'
          }
        }
      );

      if (tokenResponse.data.success) {
        const signedToken = tokenResponse.data.result.token;
        const signedUrl = `https://iframe.videodelivery.net/${videoId}?token=${signedToken}`;
        
        this.setState({ 
          signedPreviewUrl: signedUrl,
          showPreview: true,
          isGeneratingPreview: false
        });
        
        this.showSuccess('Video preview loaded successfully');
      } else {
        throw new Error('Failed to generate signed token');
      }
    } catch (error) {
      console.error('Error generating signed URL:', error);
      this.setState({ isGeneratingPreview: false });
      this.showError('Failed to generate video preview: ' + (error.response?.data?.errors?.[0]?.message || error.message));
    }
  };

  closePreview = () => {
    this.setState({ 
      showPreview: false, 
      signedPreviewUrl: null 
    });
  };

  // Utility functions
  showError = (message) => {
    this.setState({ error: message, success: null });
    NotificationManager.error(message);
  };

  showSuccess = (message) => {
    this.setState({ success: message, error: null });
    NotificationManager.success(message);
  };

  copyToClipboard = (text, label = 'Text') => {
    navigator.clipboard.writeText(text);
    this.showSuccess(`${label} copied to clipboard!`);
  };

  formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  formatDuration = (seconds) => {
    if (!seconds) return 'N/A';
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return hours > 0 ? `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}` 
                     : `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  render() {
    const {
      selectedFile,
      isUploading,
      uploadProgress,
      videoId,
      videoDetails,
      isLoadingDetails,
      showManualInput,
      manualVideoId,
      error,
      success,
      tusUpload,
      showPreview,
      signedPreviewUrl,
      isGeneratingPreview
    } = this.state;

    // Debug logging
    console.log('CloudflareStreamManager render state:', {
      selectedFile: selectedFile?.name,
      isUploading,
      uploadProgress,
      videoId,
      hasVideoDetails: !!videoDetails
    });

    return (
      <Box sx={{ width: '100%', maxWidth: 800, mx: 'auto', p: 2 }}>
        {/* Error/Success Messages */}
        {error && (
          <Alert 
            severity="error" 
            sx={{ mb: 2 }} 
            onClose={() => this.setState({ error: null })}
            action={
              selectedFile && !isUploading ? (
                <Button
                  color="inherit"
                  size="small"
                  onClick={this.retryUpload}
                  startIcon={<RefreshIcon />}
                >
                  Retry
                </Button>
              ) : null
            }
          >
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 2 }} onClose={() => this.setState({ success: null })}>
            {success}
          </Alert>
        )}

        {/* Upload Section */}
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              📹 Upload Video to Cloudflare Stream
            </Typography>
            
            {/* File Selection */}
            <Box sx={{ mb: 2 }}>
              <input
                type="file"
                accept="video/*"
                onChange={this.handleFileSelect}
                style={{ display: 'none' }}
                id="video-upload-input"
              />
              <label htmlFor="video-upload-input">
                <Button
                  variant="outlined"
                  component="span"
                  startIcon={<CloudUploadIcon />}
                  disabled={isUploading}
                  fullWidth
                  sx={{ mb: 1 }}
                >
                  Select Video File
                </Button>
              </label>
              
              {selectedFile && (
                <Box sx={{ p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                    <Box sx={{ flexGrow: 1 }}>
                      <Typography variant="body2">
                        <strong>File:</strong> {selectedFile.name}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Size:</strong> {this.formatFileSize(selectedFile.size)}
                      </Typography>
                      <Typography variant="body2">
                        <strong>Type:</strong> {selectedFile.type}
                      </Typography>
                    </Box>
                    {!isUploading && (
                      <IconButton
                        size="small"
                        onClick={this.clearFileSelection}
                        color="error"
                        title="Clear selection"
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    )}
                  </Box>
                </Box>
              )}
            </Box>

            {/* Upload Controls */}
            {selectedFile && (
              <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                {!isUploading ? (
                  <Button
                    variant="contained"
                    onClick={this.uploadVideo}
                    startIcon={<CloudUploadIcon />}
                    size="large"
                  >
                    Upload Video
                  </Button>
                ) : (
                  <>
                  
                    <Button
                      variant="outlined"
                      color="error"
                      onClick={this.cancelUpload}
                      startIcon={<DeleteIcon />}
                    >
                      Cancel
                    </Button>
                  </>
                )}
              </Box>
            )}

            {/* Debug Info - Remove this later */}
            {process.env.NODE_ENV === 'development' && selectedFile && (
              <Box sx={{ p: 1, bgcolor: 'yellow', mb: 2, fontSize: '12px' }}>
                Debug: selectedFile={selectedFile?.name}, isUploading={isUploading.toString()}, videoId={videoId || 'null'}
              </Box>
            )}

            {/* Upload Progress */}
            {isUploading && (
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                    Uploading: {selectedFile?.name}
                  </Typography>
                  <Typography variant="body2" sx={{ minWidth: 50, textAlign: 'right' }}>
                    {uploadProgress}%
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={uploadProgress}
                  sx={{ height: 8, borderRadius: 4 }}
                />
                {uploadProgress === 100 && (
                  <Typography variant="body2" color="textSecondary" sx={{ mt: 1, textAlign: 'center' }}>
                    🔄 Processing video... This may take a few minutes.
                  </Typography>
                )}
                {uploadProgress > 0 && uploadProgress < 100 && (
                  <Typography variant="body2" color="textSecondary" sx={{ mt: 1, textAlign: 'center' }}>
                    📤 Uploading to Cloudflare Stream...
                  </Typography>
                )}
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Manual Video ID Input */}
        <Card sx={{ mb: 2 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🔗 Load Existing Video
            </Typography>
            
            {!showManualInput ? (
              <Button
                variant="outlined"
                onClick={() => this.setState({ showManualInput: true })}
                fullWidth
              >
                Enter Existing Video ID
              </Button>
            ) : (
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="Enter 32-character video ID (e.g., ea95132c15732412d22c1476fa83f27a)"
                  value={manualVideoId}
                  onChange={(e) => this.setState({ manualVideoId: e.target.value })}
                  sx={{ minWidth: 300 }}
                />
                <Button
                  variant="contained"
                  onClick={this.handleManualVideoId}
                  disabled={!manualVideoId.trim()}
                >
                  Load
                </Button>
                <Button
                  variant="outlined"
                  onClick={() => this.setState({ showManualInput: false, manualVideoId: '' })}
                >
                  Cancel
                </Button>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Video Details Section */}
        {videoId && (
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2, flexWrap: 'wrap' }}>
                <Typography variant="h6">
                  📊 Video Details
                </Typography>
                <Box>
                  <Tooltip title="Refresh Details">
                    <IconButton onClick={this.loadVideoDetails} disabled={isLoadingDetails}>
                      <RefreshIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete Video">
                    <IconButton 
                      onClick={this.deleteVideo} 
                      color="error"
                      disabled={isLoadingDetails}
                    >
                      {isLoadingDetails ? <CircularProgress size={20} /> : <DeleteIcon />}
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>

              {/* Video ID Display */}
              <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                <Typography variant="body2" gutterBottom>
                  <strong>Video ID:</strong>
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, flexWrap: 'wrap' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', wordBreak: 'break-all', flexGrow: 1 }}>
                    {videoId}
                  </Typography>
                  <IconButton
                    size="small"
                    onClick={() => this.copyToClipboard(videoId, 'Video ID')}
                  >
                    <CopyIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>

              {/* Loading State */}
              {isLoadingDetails && (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                  <CircularProgress />
                </Box>
              )}

              {/* Video Details */}
              {videoDetails && !isLoadingDetails && (
                <Box>
                  {/* Basic Info Grid */}
                  <Box sx={{ display: 'grid', gridTemplateColumns: { xs: '1fr', sm: '1fr 2fr' }, gap: 1, mb: 2 }}>
                    <Typography variant="body2"><strong>Status:</strong></Typography>
                    <Typography 
                      variant="body2"
                      color={
                        videoDetails.status?.state === 'ready' ? 'success.main' :
                        videoDetails.status?.state === 'inprogress' ? 'warning.main' : 'error.main'
                      }
                    >
                      {videoDetails.status?.state?.toUpperCase() || 'UNKNOWN'}
                      {videoDetails.status?.pctComplete && videoDetails.status?.state === 'inprogress' && 
                        ` (${videoDetails.status.pctComplete}% complete)`
                      }
                    </Typography>

                    <Typography variant="body2"><strong>Duration:</strong></Typography>
                    <Typography variant="body2">
                      {this.formatDuration(videoDetails.duration)}
                    </Typography>

                    <Typography variant="body2"><strong>Size:</strong></Typography>
                    <Typography variant="body2">
                      {videoDetails.size ? this.formatFileSize(videoDetails.size) : 'N/A'}
                    </Typography>

                    <Typography variant="body2"><strong>Created:</strong></Typography>
                    <Typography variant="body2">
                      {videoDetails.created ? new Date(videoDetails.created).toLocaleString() : 'N/A'}
                    </Typography>

                    <Typography variant="body2"><strong>Ready to Stream:</strong></Typography>
                    <Typography variant="body2" sx={{ 
                      color: videoDetails.readyToStream ? 'success.main' : 'warning.main',
                      fontWeight: 'bold'
                    }}>
                      {videoDetails.readyToStream ? '✅ Yes' : '⏳ Processing...'}
                    </Typography>

                    <Typography variant="body2"><strong>Require Signed URLs:</strong></Typography>
                    <Typography variant="body2" sx={{ 
                      color: videoDetails.requireSignedURLs ? 'success.main' : 'warning.main',
                      fontWeight: 'bold'
                    }}>
                      {videoDetails.requireSignedURLs ? '🔒 Enabled (Secure)' : '🔓 Disabled'}
                    </Typography>

                    <Typography variant="body2"><strong>Allowed Origins:</strong></Typography>
                    <Typography variant="body2" sx={{ 
                      color: videoDetails.allowedOrigins && videoDetails.allowedOrigins.length > 0 ? 'success.main' : 'warning.main',
                      fontWeight: 'bold'
                    }}>
                      {videoDetails.allowedOrigins && videoDetails.allowedOrigins.length > 0 
                        ? `🌐 ${videoDetails.allowedOrigins.join(', ')}` 
                        : '🌍 All domains (Not restricted)'}
                    </Typography>
                  </Box>

                  {/* Security Settings Actions */}
                  {(!videoDetails.requireSignedURLs || !videoDetails.allowedOrigins || videoDetails.allowedOrigins.length === 0) && (
                    <Box sx={{ mb: 2 }}>
                      <Button
                        variant="outlined"
                        color="warning"
                        size="small"
                        onClick={() => this.updateVideoSettings(videoId)}
                        startIcon={<SecurityIcon />}
                      >
                        Enable Security Settings (Recommended)
                      </Button>
                    </Box>
                  )}

                  {/* Video Preview Section */}
                  {videoDetails.readyToStream && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" gutterBottom>
                        <strong>🎬 Video Preview:</strong>
                      </Typography>
                      
                      {!showPreview && (
                        <Button
                          variant="contained"
                          color="primary"
                          onClick={this.generateSignedUrl}
                          disabled={isGeneratingPreview}
                          startIcon={isGeneratingPreview ? <CircularProgress size={20} /> : <PlayArrowIcon />}
                          sx={{ mb: 1 }}
                        >
                          {isGeneratingPreview ? 'Generating Preview...' : 'Generate & Show Video Preview'}
                        </Button>
                      )}

                      {showPreview && signedPreviewUrl && (
                        <Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                            <Typography variant="body2" color="success.main">
                              ✅ Preview loaded with signed URL
                            </Typography>
                            <Button
                              size="small"
                              onClick={this.closePreview}
                              startIcon={<CloseIcon />}
                            >
                              Close Preview
                            </Button>
                          </Box>
                          <iframe
                            src={signedPreviewUrl}
                            style={{
                              width: '100%',
                              height: '400px',
                              border: 'none',
                              borderRadius: '8px'
                            }}
                            allowFullScreen
                            title="Video Preview"
                          />
                        </Box>
                      )}
                    </Box>
                  )}

            

                  {/* Processing Message */}
                  {!videoDetails.readyToStream && videoDetails.status?.state === 'inprogress' && (
                    <Alert severity="info" sx={{ mt: 2 }}>
                      <Typography variant="body2">
                        🔄 Video is still processing. Streaming URLs and preview will be available once processing is complete.
                        {videoDetails.status?.pctComplete && ` (${videoDetails.status.pctComplete}% complete)`}
                      </Typography>
                    </Alert>
                  )}
                </Box>
              )}
            </CardContent>
          </Card>
        )}

        {/* Video Preview Section - Signed URL */}
        {showPreview && signedPreviewUrl && (
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                🎥 Video Preview
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="body2" gutterBottom>
                  <strong>Signed Preview URL:</strong>
                </Typography>
                <TextField
                  fullWidth
                  size="small"
                  value={signedPreviewUrl}
                  onFocus={(e) => e.target.select()}
                  InputProps={{
                    readOnly: true,
                  }}
                  sx={{ 
                    bgcolor: 'grey.100',
                    borderRadius: 1,
                    '& .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'transparent',
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'transparent',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: 'transparent',
                    },
                  }}
                />
              </Box>

              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                <Button
                  variant="contained"
                  onClick={() => window.open(signedPreviewUrl, '_blank')}
                  startIcon={<PlayIcon />}
                >
                  Watch Preview
                </Button>
                <Button
                  variant="outlined"
                  onClick={this.closePreview}
                  color="error"
                  startIcon={<DeleteIcon />}
                >
                  Close Preview
                </Button>
              </Box>

              <Typography variant="body2" color="textSecondary">
                Note: The preview URL is signed and will expire in 1 hour. Please refresh the page to generate a new URL if needed.
              </Typography>
            </CardContent>
          </Card>
        )}
      </Box>
    );
  }
}

export default CloudflareStreamManager;