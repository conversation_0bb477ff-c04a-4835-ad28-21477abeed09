import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import GroupIcon from "@mui/icons-material/Group";
import MenuBookIcon from "@mui/icons-material/MenuBook";
import LocalLibraryIcon from "@mui/icons-material/LocalLibrary";
import SendIcon from "@mui/icons-material/Send";
import ModeEditIcon from "@mui/icons-material/ModeEdit";
import invalid from "../invalid.png";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Checkbox from "@mui/material/Checkbox";
import ListItemText from "@mui/material/ListItemText";
import TextField from "@mui/material/TextField";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import { DatePicker } from "@material-ui/pickers";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
class Dashboard extends react.Component {
  state = {
    isLoading: true,
    paidUsers: [],
    freeUsers: [],
    paidPages: 0,
    freePages: 0,
    paidPageNum: 0,
    freePageNum: 0,
    login: "invalid",
    usersCount: 0,
    quizCount: 0,
    qBankCount: 0,
    packagesAllList: [],
    selected: {},
    popUpOpen: false,
    name: "",
    surname: "",
    district: "10",
    userType: "0",
    dob: "",
    email: "",
    phoneNum: "",
    selectedPackages: [],
    showHide: false,
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      const userList = await axios.get(`${commonData["api"]}/dashboard/0`, {
        headers,
      });
      const packagesList = await axios.get(
        `${commonData["api"]}/adminmasterdata/packagelist`,
        {
          headers,
        }
      );
      /* const realtimeData = await axios.get(
        `${commonData["api"]}/realtime-users`,
        {
          headers,
        }
      );

      console.log(realtimeData);*/
      console.log(packagesList);
      console.log(userList);
      this.setState({
        isLoading: false,
        packagesAllList: packagesList.data[0],
        paidUsers: userList.data[1],
        freeUsers: userList.data[3],
        paidPages: userList.data[1].length,
        freePages: userList.data[3].length,
        login: "valid",
        usersCount: userList.data[0][0].count,
        quizCount: userList.data[0][1].count,
        qBankCount: userList.data[0][2].count,
      });
    } catch (err) {
      this.setState({
        isLoading: false,
        login: "invalid",
      });
    }
  };

  onChangeData = (e) => {
    this.setState({ [e.target.name]: e.target.value });
  };
  onChangePackageData = (e) => {
    this.setState({
      selectedPackages:
        typeof value === "string" ? e.target.value.split(",") : e.target.value,
    });
  };

  updateUserInfo = async (e) => {
    e.preventDefault();
    const {
      name,
      surname,
      phoneNum,
      email,
      district,
      dob,
      selectedPackages,
      userType,
    } = this.state;
    const registrationData = {
      name,
      surname,
      email,
      district,
      dob,
      number: phoneNum,
      type: "EDIT",
      gidselected: selectedPackages.join(","),
      su: userType,
    };
    console.log(registrationData);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.post(
        `${commonData["api"]}/admin/create-new-user`,
        registrationData,
        { headers }
      );
      console.log(data);
      this.getData();
      this.setState({
        isLoading: false,
        popUpOpen: false,
        name: "",
        email: "",
        dob: "",
        phoneNum: "",
        district: "10",
        userType: "0",
        surname: "",
        selectedPackages: [],
      });
      NotificationManager.success(`${name} Data updated Succesfully...`);
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  renderUserEdit = () => {
    const {
      name,
      email,
      district,
      dob,
      phoneNum,
      userType,
      surname,
      selectedPackages,
      packagesAllList,
      popupType,
    } = this.state;
    return (
      <form className="form-register" onSubmit={this.updateUserInfo}>
        <TextField
          required
          className="input-box register"
          label="Name"
          variant="filled"
          value={name}
          name="name"
          onChange={this.onChangeData}
        />
        <TextField
          required
          className="input-box register"
          label="Surname"
          variant="filled"
          value={surname}
          name="surname"
          onChange={this.onChangeData}
        />
        <TextField
          required
          className="input-box register"
          label="Email Address"
          variant="filled"
          value={email}
          name="email"
          onChange={this.onChangeData}
        />
        <TextField
          required
          className="input-box register"
          label="Phone Number"
          variant="filled"
          value={phoneNum}
          name="phoneNum"
          onChange={this.onChangeData}
        />

        <Select
          required
          id="Please-Select-District"
          className="input-box dis"
          // label="Please Select District"
          value={district}
          name="district"
          onChange={this.onChangeData}
          MenuProps={MenuProps}
        >
          <MenuItem value={10} disabled className="attempt-option-select">
            Please Select District
          </MenuItem>
          <MenuItem value="Anantapur" className="attempt-option-select">
            Anantapur
          </MenuItem>
          <MenuItem value="Chittoor" className="attempt-option-select">
            Chittoor
          </MenuItem>
          <MenuItem value="East Godavari" className="attempt-option-select">
            East Godavari
          </MenuItem>
          <MenuItem value="Guntur" className="attempt-option-select">
            Guntur
          </MenuItem>
          <MenuItem value="Krishna" className="attempt-option-select">
            Krishna
          </MenuItem>
          <MenuItem value="Kurnool" className="attempt-option-select">
            Kurnool
          </MenuItem>
          <MenuItem value="Prakasam" className="attempt-option-select">
            Prakasam
          </MenuItem>
          <MenuItem value="Srikakulam" className="attempt-option-select">
            Srikakulam
          </MenuItem>
          <MenuItem
            value="Sri Potti Sriramulu Nellore"
            className="attempt-option-select"
          >
            Sri Potti Sriramulu Nellore
          </MenuItem>
          <MenuItem value="Visakhapatnam" className="attempt-option-select">
            Visakhapatnam
          </MenuItem>
          <MenuItem value="Vizianagaram" className="attempt-option-select">
            Vizianagaram
          </MenuItem>
          <MenuItem value="West Godavari" className="attempt-option-select">
            West Godavari
          </MenuItem>
          <MenuItem value="Kadapa" className="attempt-option-select">
            Kadapa
          </MenuItem>
          <MenuItem value="Adilabad" className="attempt-option-select">
            Adilabad
          </MenuItem>
          <MenuItem
            value="Bhadradri Kothagudem"
            className="attempt-option-select"
          >
            Bhadradri Kothagudem
          </MenuItem>
          <MenuItem value="Hyderabad" className="attempt-option-select">
            Hyderabad
          </MenuItem>
          <MenuItem value="Jagtial" className="attempt-option-select">
            Jagtial
          </MenuItem>
          <MenuItem value="Jangaon" className="attempt-option-select">
            Jangaon
          </MenuItem>
          <MenuItem
            value="Jayashankar Bhoopalpally"
            className="attempt-option-select"
          >
            Jayashankar Bhoopalpally
          </MenuItem>
          <MenuItem value="Jogulamba Gadwal" className="attempt-option-select">
            Jogulamba Gadwal
          </MenuItem>
          <MenuItem value="Kamareddy" className="attempt-option-select">
            Kamareddy
          </MenuItem>
          <MenuItem value="Karimnagar" className="attempt-option-select">
            Karimnagar
          </MenuItem>
          <MenuItem value="Khammam" className="attempt-option-select">
            Khammam
          </MenuItem>
          <MenuItem
            value="Komaram Bheem Asifabad"
            className="attempt-option-select"
          >
            Komaram Bheem Asifabad
          </MenuItem>
          <MenuItem value="Mahabubabad" className="attempt-option-select">
            Mahabubabad
          </MenuItem>
          <MenuItem value="Mahabubnagar" className="attempt-option-select">
            Mahabubnagar
          </MenuItem>
          <MenuItem value="Mancherial" className="attempt-option-select">
            Mancherial
          </MenuItem>
          <MenuItem value="Medak" className="attempt-option-select">
            Medak
          </MenuItem>
          <MenuItem value="Medchal" className="attempt-option-select">
            Medchal
          </MenuItem>
          <MenuItem value="Nagarkurnool" className="attempt-option-select">
            Nagarkurnool
          </MenuItem>
          <MenuItem value="Nalgonda" className="attempt-option-select">
            Nalgonda
          </MenuItem>
          <MenuItem value="Nirmal" className="attempt-option-select">
            Nirmal
          </MenuItem>
          <MenuItem value="Nizamabad" className="attempt-option-select">
            Nizamabad
          </MenuItem>
          <MenuItem value="Peddapalli" className="attempt-option-select">
            Peddapalli
          </MenuItem>
          <MenuItem value="Rajanna Sircilla" className="attempt-option-select">
            Rajanna Sircilla
          </MenuItem>
          <MenuItem value="Rangareddy" className="attempt-option-select">
            Rangareddy
          </MenuItem>
          <MenuItem value="Sangareddy" className="attempt-option-select">
            Sangareddy
          </MenuItem>
          <MenuItem value="Siddipet" className="attempt-option-select">
            Siddipet
          </MenuItem>
          <MenuItem value="Suryapet" className="attempt-option-select">
            Suryapet
          </MenuItem>
          <MenuItem value="Vikarabad" className="attempt-option-select">
            Vikarabad
          </MenuItem>
          <MenuItem value="Wanaparthy" className="attempt-option-select">
            Wanaparthy
          </MenuItem>
          <MenuItem value="Warangal" className="attempt-option-select">
            Warangal
          </MenuItem>
          <MenuItem
            value="Yadadri Bhuvanagiri"
            className="attempt-option-select"
          >
            Yadadri Bhuvanagiri
          </MenuItem>
        </Select>
        <Select
          required
          id="Please-Select-District"
          className="input-box dis"
          // label="Type of user"
          value={userType}
          name="userType"
          onChange={this.onChangeData}
          MenuProps={MenuProps}
        >
          <MenuItem value="0" className="attempt-option-select">
            Student
          </MenuItem>
          <MenuItem value="1" className="attempt-option-select">
            Admin
          </MenuItem>
        </Select>
        <Select
          required
          id="Please-Select-District"
          className="input-box dis"
          // label="Please Select Package"
          value={selectedPackages}
          name="selectedPackages"
          multiple
          displayEmpty
          onChange={this.onChangePackageData}
          renderValue={(selectedPackages) => {
            if (selectedPackages.length === 0) {
              return <em>Please Select Package</em>;
            }

            return selectedPackages.join(", ");
          }}
          MenuProps={MenuProps}
        >
          <MenuItem disabled value="" className="attempt-option-select">
            Please Select Package
          </MenuItem>
          {packagesAllList.map((e, i) => (
            <MenuItem
              className="selectionbox"
              value={e.gid}
              key={"multiselect" + i}
            >
              <Checkbox checked={selectedPackages.indexOf(e.gid) > -1} />
              <ListItemText primary={e.group_name} />
            </MenuItem>
          ))}
        </Select>
        {/* <DatePicker
          disableFuture
          className="input-box register"
          openTo="year"
          format="dd/MMM/yyyy"
          label="Date of birth"
          views={["year", "month", "date"]}
          value={dob}
          inputVariant="filled"
          name="dob"
          onChange={this.onChangeData}
        /> */}
        <input
          type={"text"}
          value={dob}
          name="dob"
          onChange={this.onChangeData}
          placeholder={"Enter D.O.B in dd/MMM/yyyy Format"}
          className="input-box register"
        />
        {popupType === "Add New User" ? (
          <Button
            type="submit"
            variant="contained"
            className="btn activateacoountbtn"
          >
            Create New Account
          </Button>
        ) : (
          <Button
            type="submit"
            variant="contained"
            className="btn activateacoountbtn"
          >
            Update Account
          </Button>
        )}
      </form>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      name: "",
      email: "",
      dob: "",
      phoneNum: "",
      district: "10",
      userType: "0",
      surname: "",
      selectedPackages: [],
    }));
  };
  renderPopUp = () => {
    const { popUpOpen } = this.state;
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{"Edit User"}</p>
            </div>
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.handleOpen}
            >
              Cancel
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {this.renderUserEdit()}
        </DialogContent>
      </Dialog>
    );
  };
  renderCountBoxes = () => {
    const { usersCount, quizCount, qBankCount } = this.state;
    return (
      <div className="useradmincountdiv">
        <div className="dashdiv blackborder">
          <div className="columndiv ">
            <p className="title">Number of User Registered</p>
            <p className="count">{usersCount}</p>
          </div>
          <div className="columncenter">
            <GroupIcon fontSize="large" />
          </div>
        </div>
        <div className="dashdiv greenborder">
          <div className="columndiv">
            <p className="title">Questions in Question Bank</p>
            <p className="count">{qBankCount}</p>
          </div>
          <div className="columncenter">
            <MenuBookIcon fontSize="large" />
          </div>
        </div>
        <div className="dashdiv orangeborder">
          <div className="columndiv">
            <p className="title">Number of Exams available</p>
            <p className="count">{quizCount}</p>
          </div>
          <div className="columncenter">
            <LocalLibraryIcon fontSize="large" />
          </div>
        </div>
      </div>
    );
  };

  renderPaginationButtons = (type, totalCount) => {
    if (type === "paid") {
      const paidcount = Math.round(totalCount / 10) - 1;
      const paidrows = [];
      for (let i = 0; i <= paidcount; i++) {
        paidrows.push(i);
      }
      return (
        <div className="pagination">
          {paidrows.map((each, i) => (
            <Button
              key={"paid" + i}
              className="btn navigate"
              onClick={() => {
                this.setState({ paidPageNum: each });
              }}
            >
              {each + 1}
            </Button>
          ))}
        </div>
      );
    } else {
      const freecount = Math.round(totalCount / 10) - 1;

      const freerows = [];
      for (let i = 0; i <= freecount; i++) {
        freerows.push(i);
      }
      return (
        <div className="pagination">
          {freerows.map((each, i) => (
            <Button
              key={"free" + i}
              className="btn navigate"
              onClick={() => {
                this.setState({ freePageNum: each });
              }}
            >
              {each + 1}
            </Button>
          ))}
        </div>
      );
    }
  };

  sliceUsers = (type) => {
    const { paidUsers, freeUsers, freePageNum, paidPageNum } = this.state;
    if (type === "paid") {
      return paidUsers.slice(paidPageNum * 10, paidPageNum * 10 + 10);
    } else {
      return freeUsers.slice(freePageNum * 10, freePageNum * 10 + 10);
    }
  };

  recentPaidUser = () => {
    const { paidPages, packagesAllList, selected, showHide } = this.state;
    const paidUsers = this.sliceUsers("paid");
    // console.log(paidUsers);
    const style = `table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width: 100%;
      overflow-x:auto;
    }
    
    td, th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 10px;
     
    }
    
    tr:nth-child(even) {
      background-color: #dddddd;
    }`;

    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <h3>Recent Paid Users</h3>
          <div>
            <Button className="btn exportbtn">Export Selected</Button>
          </div>
          <div
            onClick={() => this.setState((p) => ({ showHide: !p.showHide }))}
            style={{ cursor: "pointer" }}
          >
            {!showHide ? (
              <i className="bi bi-eye-fill">{"Show"}</i>
            ) : (
              <i className="bi bi-eye-slash"> {"Hide"}</i>
            )}
          </div>
        </div>

        <div className="dashboardtable">
          <table>
            <thead>
              <tr>
                <th>
                  <input type="checkbox" />
                </th>
                <th>S.No</th>
                <th>Student name</th>
                {showHide && <th>Email</th>}
                <th>Whatsapp</th>
                {showHide && (
                  <>
                    <th>District</th>
                    <th>D.O.B</th>
                  </>
                )}
                <th>Amount</th>
                <th>Paid for </th>
                {showHide && (
                  <>
                    <th>Paid On</th>
                    <th>Groups Joined </th>
                  </>
                )}
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {paidUsers.map((each, ind) => {
                let dobNew = new Date(each.date_of_birth);

                if (dobNew != "Invalid Date") {
                  dobNew =
                    dobNew.getDate() +
                    "/" +
                    dobNew.getMonth() +
                    "/" +
                    dobNew.getFullYear();
                } else {
                  dobNew = each.date_of_birth;
                }
                return (
                  <tr key={"paidusers" + ind}>
                    <td>
                      <input
                        type="checkbox"
                        name="paidusers"
                        onChange={(e) => {
                          if (selected[each.uid] != each.uid) {
                            const valu = {};
                            valu[e.target.value] = e.target.value;
                            this.setState((prev) => ({
                              selected: { ...prev.selected, ...valu },
                            }));
                          } else {
                            delete selected[each.uid];
                          }
                        }}
                        value={
                          selected[each.uid] ? selected[each.uid] : each.uid
                        }
                        // checked={console.log(selected[each.uid] == each.uid)}
                      />
                    </td>
                    <td>{each.row}</td>
                    <td>{each.first_name + " " + each.last_name} </td>
                    {showHide && <td>{each.email} </td>}
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => {
                        navigator.clipboard.writeText(
                          `https://api.whatsapp.com/send?phone=91${each.contact_no}&amp;text=Hai`
                        );
                        NotificationManager.success("Copied to ClipBoard");
                      }}
                    >
                      {each.contact_no}
                    </td>
                    {showHide && (
                      <>
                        <td>{each.district} </td>
                        <td>{dobNew} </td>{" "}
                      </>
                    )}
                    <td>{each.amount}</td>
                    <td>
                      {packagesAllList.filter(
                        (va) => va.gid == each.paidgroup
                      )[0] !== undefined
                        ? packagesAllList.filter(
                            (va) => va.gid == each.paidgroup
                          )[0].group_name
                        : ""}
                    </td>
                    {showHide && (
                      <>
                        <td>{new Date(each.paid_date).toLocaleDateString()}</td>
                        <td>
                          <select>
                            {each.gid.split(",").map((e, i) => {
                              return (
                                <option key={"gid" + e}>
                                  {packagesAllList.filter(
                                    (va) => va.gid == e
                                  )[0] !== undefined
                                    ? packagesAllList.filter(
                                        (va) => va.gid == e
                                      )[0].group_name
                                    : ""}
                                </option>
                              );
                            })}
                          </select>
                        </td>
                      </>
                    )}
                    <td style={{ textAlign: "center" }}>
                      <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 10, cursor: "pointer" }}
                        onClick={() => {
                          this.setState((p) => ({
                            popUpOpen: true,
                            name: each.first_name,
                            email: each.email,
                            district: each.district,
                            dob: dobNew,
                            phoneNum: each.contact_no,
                            userType: each.su,
                            surname: each.last_name,
                            selectedPackages: each.gid
                              .split(",")
                              .map((e) => parseInt(e)),
                          }));
                        }}
                      ></i>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        {this.renderPaginationButtons("paid", paidPages)}
      </div>
    );
  };

  recentFreeUser = () => {
    const { freePages, packagesAllList, showHide } = this.state;
    const freeUsers = this.sliceUsers("free");
    const style = `table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width: 100%;
      overflow-x:auto;
    }
    
    td, th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 10px;
     
    }
    
    tr:nth-child(even) {
      background-color: #dddddd;
    }`;
    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <h3>Recently Registered Users (Free)</h3>
          <div>
            <Button className="btn exportbtn">Export Selected</Button>
          </div>
          <div
            onClick={() => this.setState((p) => ({ showHide: !p.showHide }))}
            style={{ cursor: "pointer" }}
          >
            {!showHide ? (
              <i className="bi bi-eye-fill">{"Show"}</i>
            ) : (
              <i className="bi bi-eye-slash"> {"Hide"}</i>
            )}
          </div>
        </div>

        <div className="dashboardtable">
          <table>
            <thead>
              <tr>
                <th></th>
                <th>S.No</th>
                <th>Student name</th>
                {showHide && <th>Email</th>}
                <th>Whatsapp</th>
                {showHide && (
                  <>
                    <th>District</th>
                    <th>D.O.B</th>
                  </>
                )}
                <th>Group Joined </th>

                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {freeUsers.map((each, ind) => {
                let dobNew = new Date(each.date_of_birth);
                if (dobNew != "Invalid Date") {
                  dobNew =
                    dobNew.getDate() +
                    "/" +
                    dobNew.getMonth() +
                    "/" +
                    dobNew.getFullYear();
                } else {
                  dobNew = each.date_of_birth;
                }
                return (
                  <tr key={"free" + ind}>
                    <td>Germany</td>
                    <td>{each.row}</td>
                    <td>{each.first_name + " " + each.last_name} </td>
                    {showHide && <td>{each.email} </td>}
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => {
                        navigator.clipboard.writeText(
                          `https://api.whatsapp.com/send?phone=91${each.contact_no}&amp;text=Hai`
                        );
                        NotificationManager.success("Copied to ClipBoard");
                      }}
                    >
                      {each.contact_no}
                    </td>
                    {showHide && (
                      <>
                        <td>{each.district} </td>
                        <td>{dobNew} </td>{" "}
                      </>
                    )}
                    <td>
                      <select>
                        {each.gid.split(",").map((e, i) => {
                          return (
                            <option>
                              {packagesAllList.filter(
                                (va) => va.gid == e
                              )[0] !== undefined
                                ? packagesAllList.filter((va) => va.gid == e)[0]
                                    .group_name
                                : ""}
                            </option>
                          );
                        })}
                      </select>
                    </td>

                    <td style={{ textAlign: "center" }}>
                      <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 10, cursor: "pointer" }}
                        onClick={() => {
                          this.setState((p) => ({
                            popUpOpen: !p.popUpOpen,
                            name: each.first_name,
                            email: each.email,
                            district: each.district,
                            dob: dobNew,
                            phoneNum: each.contact_no,
                            userType: each.su,
                            surname: each.last_name,
                            selectedPackages: each.gid
                              .split(",")
                              .map((e) => parseInt(e)),
                          }));
                        }}
                      ></i>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
        {this.renderPaginationButtons("free", freePages)}
      </div>
    );
  };

  render() {
    const { isLoading, login, selected } = this.state;
    // console.log(this.state);
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.renderCountBoxes()}
                {this.renderPopUp()}
                {this.recentPaidUser()}
                {this.recentFreeUser()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default Dashboard;
