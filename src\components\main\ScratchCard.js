import React from "react";
import ScratchCard from "react-scratchcard";
import loaderback from "../scratch.png";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Button from "@mui/material/Button";
import { NotificationManager } from "react-notifications";
import axios from "axios";
import Cookie from "js-cookie";
import commonData from "../../importanValue";

const ScratchCardView = ({ linkText, loadData, countData }) => {
  const [count, setCOunt] = React.useState(0);
  const [amount, setamount] = React.useState(0);
  const [popUpOpen, setpopUpOpen] = React.useState(false);
  const [getScratch, setgetScratch] = React.useState(false);
  const [scratchClear, setscratchClear] = React.useState(false);
  console.log("countData", countData);
  const settings = {
    width: 200,
    height: 200,
    image: loaderback,
    finishPercent: 80,
    onComplete: () => {
      //   console.log("The card is now clear!");
      randomAmountGen();
      setscratchClear(!scratchClear);
    },
  };
  const randomAmountGen = async () => {
    const value = Math.ceil(Math.random() * 3);
    setamount(value);
    saveScratchAmount(value);
    NotificationManager.success(`Congrats you won Rs. ${value}`);
  };

  const checkShareCount = () => {
    if (count < 1) {
      NotificationManager.error(
        `Please share in 5 whatsapp groups to get Scratch Card`
      );
    } else if (countData === 5) {
      NotificationManager.error(
        `Sorry 5 chances only. Try to share package details to get earnings`
      );
    } else {
      NotificationManager.success(`Congrats you won a Scratch Card`);
      setgetScratch(!getScratch);
    }
  };
  const setCountData = () => setCOunt(count + 1);
  const handleOpen = () => {
    setCOunt(0);
    setscratchClear(false);
    setgetScratch(false);
    setpopUpOpen(!popUpOpen);
  };

  console.log(amount);
  const saveScratchAmount = async (amount2) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const num = localStorage.getItem("num");
    const body = {
      type: "saveScratchAmount",
      search: num,
      qid: amount2,
    };
    try {
      //   console.log(commonData["api"]);
      await axios.post(`${commonData["api"]}/support`, body, {
        headers,
      });
      setTimeout(() => loadData(), 6000);
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };
  const renderPopUp = () => {
    return (
      <Dialog open={popUpOpen} onClose={handleOpen} maxWidth={"xs"} fullWidth>
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Share & Earn</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent scratchContent">
          {" "}
          {!getScratch ? (
            <div>
              {window.innerWidth > 766 ? (
                <p>Please open in your Mobile</p>
              ) : (
                <>
                  {" "}
                  <p style={{ textAlign: "center" }}> Get Discount here</p>
                  <p>
                    1. హోమ్ పేజీ లో కనిపించే ఏ ప్యాకేజీ ను అయినా మీ ఫ్రెండ్స్ కు
                    షేర్ (షేర్ బటన్ అక్కడ కనిపిస్తుంది) చేయండి. ఒకవేళ వారు
                    పేమెంట్ పూర్తి చేస్తే, వారు చేసే పేమెంట్ లో 20% మీకు
                    డిస్కౌంట్ గా లభిస్తుంది
                  </p>
                  <p>
                    {" "}
                    2. ఇక్కడ క్లిక్ చేసి మీ ఫ్రెండ్స్ కు యాప్ ను షేర్ చేయండి.
                    షేర్ చేసి స్క్రాచ్ కార్డ్ ద్వారా రూ. 1000 వరకు డిస్కౌంట్ 
                    పొందండి
                  </p>
                  <a
                    href={linkText}
                    target="_blank"
                    rel="noopener"
                    aria-label="Share on WhatsApp"
                    data-action="share/whatsapp/share"
                  >
                    <div className="winscratch2" onClick={setCountData}>
                      Share on WhatsApp
                    </div>
                  </a>
                  <div className="winscratch" onClick={checkShareCount}>
                    {" "}
                    Win Scratch Card
                  </div>
                </>
              )}
            </div>
          ) : (
            <ScratchCard {...settings}>
              {scratchClear && (
                <div>
                  <p> Congratulations...</p>
                  <p> You won ₹{amount}</p>
                </div>
              )}
            </ScratchCard>
          )}
        </DialogContent>
      </Dialog>
    );
  };
  return (
    <div>
      <div onClick={handleOpen}>
        <p>
          1. హోమ్ పేజీ లో కనిపించే ఏ ప్యాకేజీ ను అయినా మీ ఫ్రెండ్స్ కు షేర్
          (షేర్ బటన్ అక్కడ కనిపిస్తుంది) చేయండి. ఒకవేళ వారు పేమెంట్ పూర్తి
          చేస్తే, వారు చేసే పేమెంట్ లో 20% మీకు డిస్కౌంట్ గా లభిస్తుంది
        </p>
        <p>
          {" "}
          2. ఇక్కడ క్లిక్ చేసి మీ ఫ్రెండ్స్ కు యాప్ ను షేర్ చేయండి. షేర్ చేసి
          స్క్రాచ్ కార్డ్ ద్వారా రూ. 1000 వరకు డిస్కౌంట్  పొందండి
        </p>
        <div className="referCodediv">
          Refer your friends {"&"} Earn scratch card
        </div>
      </div>
      {renderPopUp()}
    </div>
  );
};

export default ScratchCardView;
