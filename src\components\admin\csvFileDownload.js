
import <PERSON> from "papaparse";

// Function to export data to CSV and trigger download
const exportToCSVFile = (DATA) => {
  // Convert the data to CSV format using PapaParse
  const csv = Papa.unparse(DATA);

  // Create a Blob containing the CSV data
  const csvBlob = new Blob([csv], { type: "text/csv" });

  // Create a URL for the Blob
  const csvUrl = URL.createObjectURL(csvBlob);

  // Create an invisible link element to trigger the download
  const link = document.createElement("a");
  link.href = csvUrl;
  link.download = "userList.csv";

  link.click();

  // Clean up by revoking the URL to release resources
  URL.revokeObjectURL(csvUrl);
};

export { exportToCSVFile };
