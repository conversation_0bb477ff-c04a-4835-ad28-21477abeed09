import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import <PERSON>ie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
// import { Editor } from "@tinymce/tinymce-react";
import JSZip from "jszip";
import Docxtemplater from "docxtemplater";
// import xml_data from "./Q.xml";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import FilesUploadComponent from "./FileUpload";
import Editor from "./Editor";
import ExportCSV, { exportToCSVD } from "./ExportCsv";
import { ExcelRenderer } from "react-excel-renderer";

class Questions extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    qCount: null,
    qReportData: [],
    page: 0,
    popUpOpen: false,
    qNum: null,
    category: null,
    questionText: "",
    op1: "",
    op2: "",
    op3: "",
    op4: "",
    op5: "",
    op1Num: 0,
    op2Num: 0,
    op3Num: 0,
    op4Num: 0,
    op5Num: 0,
    correctOption: 1,
    selectedCat: "",
    categoryList: [],
    editorLoading: false,
    optionsData: [],
    popupType: null,
    search: "",
    questionsCount: 0,
    oid: 0,
    addOptionClicked: false,
    errQdata: [],
    ErrqNum: 0,
    catId: 0,
    englishMedium: false,
    engOp1: "",
    engOp2: "",
    engOp3: "",
    engOp4: "",
    engOp5: "",
    engquestionText: "",
    importUsersClicked: false,
    isImporting: false,
    totalCountImport: 0,
  };

  componentDidMount() {
    try {
      const { location } = this.props;
      const { search } = location;
      if (search.includes("?qid=")) {
        this.setState(
          {
            qNum: search.split("?qid=")[1],
            popUpOpen: true,
            editorLoading: true,
            popupType: "Edit Question",
            isLoading: false,
          },
          () => {
            this.getData();
            this.getOptions();
          }
        );
        return;
      }
    } catch (error) {}

    this.loadQuestions();
  }

  handleImportPopup = () => {
    this.setState((p) => ({
      selectedPackages: [],
      importPopUp: !p.importPopUp,
      rows: [],
      excel: [],
      importUsersClicked: false,
      popUpOpen: false,
    }));
  };

  importUsers = async () => {
    const { cols, rows, existedUsers, importCount } = this.state;
    console.log("🚀 ~ file: Questions.js:91 ~ importUsers= ~ rows:", rows);
    // console.log(rows);
    if (rows != undefined) {
      // let ind = 1;
      this.setState({
        importUsersClicked: true,
        isImporting: true,
        totalCountImport: rows.length,
        importCount: 0,
      });

      for (const each of rows) {
        if (each.length > 0) {
          if (each[1] !== "LENGTHY") {
            const questionObject = {
              qid: each[0], // 0
              question: each[1], // 1
              optionId1: each[2], // 2
              option1Text: each[3], // 3
              optionId2: each[4], // 4
              option2Text: each[5], // 5
              optionId3: each[6], // 6
              option3Text: each[7], // 7
              optionId4: each[8], // 8
              option4Text: each[9], // 9
            };

            this.setState(
              (prevState) => ({
                importCount: prevState.importCount + 1,
              }),
              async () => await this.importUsersUpdateDb(questionObject)
            );
          }
        }
      }

      // console.log(existedUsers, insertedUsers);
      this.setState({
        existedUsers: 0,
        insertedUsers: 0,
        cols: [],
        importCount: 0,
        totalCountImport: 0,
        rows: [],
        importPopUp: false,
        importUsersClicked: false,
        isImporting: false,
      });

      NotificationManager.success(`Data Imported Succesfully...`);
      this.getData();
    } else {
      NotificationManager.error(`Please Select valid file to import`);
    }
  };

  importUsersUpdateDb = async (data) => {
    console.log(
      "🚀 ~ file: Questions.js:160 ~ importUsersUpdateDb= ~ data:",
      data
    );
    const {
      qid,
      question,
      optionId1,
      option1Text,
      optionId2,
      option2Text,
      optionId3,
      option3Text,
      optionId4,
      option4Text,
    } = data;
    // console.log(data);
    const registrationData = {
      qid,
      question,
      optionId1,
      option1Text,
      optionId2,
      option2Text,
      optionId3,
      option3Text,
      optionId4,
      option4Text,
    };
    try {
      const token = Cookie.get("jwt_token");
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
        "Access-Control-Allow-Origin": "*",
      };
      const body = {
        type: "updateQuestionsEnglishExcel",
        search: "",
        qid: registrationData,
      };

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      return true;
    } catch (error) {}
  };

  fileHandler = (event) => {
    let fileObj = event.target.files[0];
    //just pass the fileObj as parameter
    // console.log(fileObj.name.split(".xlsx"));
    ExcelRenderer(fileObj, (err, resp) => {
      if (err) {
        console.log(err);
      } else {
        // console.log(resp);
        this.setState({
          cols: resp.rows.slice(0, 1)[0],
          rows: resp.rows.slice(1, resp.rows.length),
        });
      }
    });
  };
  renderImportPopUp = () => {
    const { importPopUp, importUsersClicked, importCount, totalCountImport } =
      this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    const sampleData = [
      {
        qid: "",
        question: "",
        optionId1: "",
        option1Text: "",
        optionId2: "",
        option2Text: "",
        optionId3: "",
        option3Text: "",
        optionId4: "",
        option4Text: "",
      },
    ];
    return (
      <Dialog
        open={importPopUp}
        onClose={this.handleImportPopup}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Import English Questions Excel</p>
            </div>
            <Button
              onClick={this.handleImportPopup}
              style={{ color: "#fff", fontSize: 20 }}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <div
            style={{
              marginTop: "20px",
              padding: 20,
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
            }}
          >
            <input
              type="file"
              onChange={this.fileHandler}
              style={{ padding: "10px" }}
            />
            <Button
              className="btn"
              onClick={this.importUsers}
              style={{ color: "#fff" }}
              disabled={importUsersClicked}
            >
              {importUsersClicked
                ? "Importing Questions " + importCount + "/" + totalCountImport
                : "Import Now"}
            </Button>
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "row",
              justifyContent: "center",
              marginTop: 10,
            }}
          >
            <ExportCSV
              csvData={sampleData}
              fileName={"sampleData user import"}
              title={"Download Sample File"}
              emptyFn={() => {}}
            />
          </div>
        </DialogContent>
      </Dialog>
    );
  };
  loadQuestions = async () => {
    this.getQbankCount();
    this.getData();
    this.getErrData();
  };
  getQbankCount = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.get(
        `${commonData["api"]}/adminmasterdata/qbankcount`,
        { headers }
      );
      //   console.log(data.data[0][0].pageCount);
      this.setState({ isLoading: false, qCount: data.data[0][0].pageCount });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  getData = async () => {
    const { page, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: isNaN(search) ? "qbankSearch" : "qbankall",
      search: search,
      qid: page * 25,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({
        qReportData: data.data[0],
        categoryList: data.data[1],
        isLoading: false,
        questionsCount: data.data[2][0].count,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  getErrData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "qbankErrall",
      search: "",
      qid: 0,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({
        isLoading: false,
        errQdata: data.data[0],
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  deleteQuestion = async () => {
    const { qNum, qReportData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "delete",
      search: "",
      qid: qNum,
    };
    if (window.confirm("Do you really want to remove entry?")) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        //   console.log(data);
        this.setState({
          qReportData: qReportData.filter((e) => e.questionid !== qNum),
        });
        NotificationManager.success(`Question Deleted Succesfully...`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };
  deleteOption = async () => {
    const { oid } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "deleteOption",
      search: "",
      qid: oid,
    };
    if (window.confirm("Do you really want to remove entry?")) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        /*await this.getData();
        this.getOptions();*/
        this.getOptions();

        NotificationManager.success(`Option Deleted Succesfully...`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };
  getOptions = async () => {
    const { qNum, qReportData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getOptions",
      search: "",
      qid: qNum,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log("data", data.data);
      const options = data.data[0];
      this.setState({
        editorLoading: false,
        // popupType: "Edit Question",
        op1: data.data[0].length >= 1 ? data.data[0][0].q_option : "",
        op2: data.data[0].length >= 2 ? data.data[0][1].q_option : "",
        op3: data.data[0].length >= 3 ? data.data[0][2].q_option : "",
        op4: data.data[0].length >= 4 ? data.data[0][3].q_option : "",
        op5: data.data[0].length >= 5 ? data.data[0][4].q_option : "",
        op1Num: data.data[0].length >= 1 ? data.data[0][0].oid : 0,
        op2Num: data.data[0].length >= 2 ? data.data[0][1].oid : 0,
        op3Num: data.data[0].length >= 3 ? data.data[0][2].oid : 0,
        op4Num: data.data[0].length >= 4 ? data.data[0][3].oid : 0,
        op5Num: data.data[0].length >= 5 ? data.data[0][4].oid : 0,
        engOp1:
          data.data[0].length >= 1 ? data.data[0][0].option_in_english : "",
        engOp2:
          data.data[0].length >= 2 ? data.data[0][1].option_in_english : "",
        engOp3:
          data.data[0].length >= 3 ? data.data[0][2].option_in_english : "",
        engOp4:
          data.data[0].length >= 4 ? data.data[0][3].option_in_english : "",
        engOp5:
          data.data[0].length >= 5 ? data.data[0][4].option_in_english : "",

        correctOption: data.data[1][0].selectedOp,
        selectedCat: data.data[2][0].selectedCategory,
        questionText: data.data[2][0].question,
        engquestionText: data.data[2][0].question_in_english,
        optionsData: options,
        addOptionClicked: false,
        questionInfo: data.data[4][0],
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  searchUsers = () => {
    const { search } = this.state;
    console.log(search);
    if (search === 0) {
      NotificationManager.error(`Please Select Category`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
      });
      this.getData();
    }
  };
  renderPaginationButtons = (type, totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    // console.log(paidrows);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount}
        >
          Next
        </Button>
      </div>
    );
  };
  ErrQuestionsTable = () => {
    const { errQdata } = this.state;
    // console.log(qReportData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv" style={{ overflowX: "scroll" }}>
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>Error Questions ( {errQdata.length} )</h3>
        </div>
        <table>
          <thead>
            <tr>
              <th>Question</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {errQdata.map((e, i) => {
              return (
                <tr key={"reported" + e.questionid}>
                  <td>
                    <div style={{ display: "flex" }}>
                      {e.questionid + ") "}
                      <span
                        style={{ marginLeft: 5 }}
                        dangerouslySetInnerHTML={{ __html: e.question }}
                      ></span>
                    </div>
                  </td>
                  <td>
                    <i
                      className="bi bi-pencil-fill"
                      style={{ marginRight: 15, cursor: "pointer" }}
                      onClick={() =>
                        this.setState(
                          {
                            qNum: e.questionid,
                            popUpOpen: true,
                            editorLoading: true,
                            popupType: "Edit Error Question",
                          },
                          () => this.getOptions()
                        )
                      }
                    ></i>
                    <i
                      className="bi bi-trash-fill"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        this.setState(
                          {
                            qNum: e.questionid,
                          },
                          () => this.deleteQuestion()
                        )
                      }
                    ></i>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };
  reportedQuestionsTable = () => {
    const { qReportData, search, categoryList, questionsCount } = this.state;
    // console.log(qReportData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv" style={{ overflowX: "scroll" }}>
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All Questions</h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              marginRight: 15,
            }}
          >
            {/* {!searchClicked ? ( */}
            <select
              style={{
                width: "230px",
                border: "2px solid orange",
                padding: 5,
                cursor: "pointer",
                marginBottom: 10,
              }}
              onChange={(e) =>
                this.setState({ search: e.target.value }, this.searchUsers)
              }
            >
              <option value={0}>{"Filter Questions By Category"}</option>
              {categoryList.map((eachCateg) => (
                <option value={eachCateg.cid}>{eachCateg.category_name}</option>
              ))}
            </select>
            <div>
              <form
                onSubmit={this.searchUsers}
                style={{ cursor: "pointer", display: "flex", marginBottom: 10 }}
              >
                <input
                  type={"text"}
                  value={search}
                  id="search"
                  onChange={(e) => this.setState({ search: e.target.value })}
                  placeholder="Search"
                />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    // marginLeft: 5,
                    backgroundColor: "green",
                    padding: 5,
                    color: "#fff",
                    borderRadius: 10,
                  }}
                >
                  <button
                    type="submit"
                    style={{
                      backgroundColor: "transparent",
                      borderWidth: 0,
                      color: "#fff",
                    }}
                  >
                    <i className="bi bi-search"></i>
                  </button>
                </div>
              </form>
            </div>
            {/* ) : (
            //   <>
            //     <select
            //       style={{
            //         width: "230px",
            //         border: "2px solid orange",
            //         padding: 5,
            //         cursor: "pointer",
            //         marginBottom: 10,
            //       }}
            //       onChange={(e) =>
            //         this.setState(
            //           { search: e.target.value, page: 0 },
            //           this.searchUsers
            //         )
            //       }
            //     >
            //       <option value={0}>{"Filter Questions By Category"}</option>
            //       {categoryList.map((eachCateg) => (
            //         <option value={eachCateg.cid}>
            //           {eachCateg.category_name}
            //         </option>
            //       ))}
            //     </select>

            //     <i
            //       className="bi bi-search"
            //       style={{
            //         border: "2px solid orange",
            //         padding: 5,
            //         cursor: "pointer",
            //         marginBottom: 10,
            //         textAlign: "center",
            //       }}
            //       onClick={() => {
            //         this.setState(
            //           {
            //             search: "",
            //             searchClicked: false,
            //             isLoading: true,
            //             hidePagination: false,
            //             page: 0,
            //           },
            //           this.getData
            //         );
            //       }}
            //     >
            //       Clear Filter
            //     </i>
            //   </>
            // )*/}
          </div>
          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  popUpOpen: !p.popUpOpen,
                  popupType: "Add New Question",
                  questionText: "",
                  op1: "",
                  op2: "",
                  op3: "",
                  op4: "",
                  op5: "",
                  op1Num: 0,
                  op2Num: 0,
                  op3Num: 0,
                  op4Num: 0,
                  op5Num: 0,
                  correctOption: null,
                  selectedCat: null,
                  editorLoading: false,
                }));
              }}
            >
              Add New Question
            </Button>
          </div>
          <div style={{ marginLeft: 10, marginRight: 10 }}>
            <Button className="btn exportbtn" onClick={this.handleImportPopup}>
              Import English Questions
            </Button>
          </div>
          <div>
            {/* <Button className="btn exportbtn">Import/Export Questions</Button> */}
            <FilesUploadComponent
              refresh={() => this.loadQuestions()}
              loading={(da) => this.setState({ isLoading: da })}
              type="questions"
            />
          </div>
          <div>
            <Button
              className="btn exportbtn"
              onClick={(da) =>
                this.setState({ popUpOpen: true, popupType: "export" })
              }
            >
              Export Category Questions
            </Button>
          </div>
        </div>
        <table>
          <thead>
            <tr>
              <th>Question</th>

              <th>Given Answer</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {qReportData.map((e, i) => {
              return (
                <tr key={"reported" + e.questionid}>
                  <td>
                    <div
                      style={{
                        display: "flex",
                        border:
                          e.added_by !== localStorage.getItem("num")
                            ? "1px solid red"
                            : "",
                      }}
                    >
                      {e.questionid + ") "}
                      <span
                        style={{ marginLeft: 5 }}
                        dangerouslySetInnerHTML={{ __html: e.question }}
                      ></span>
                    </div>
                    {e.question_in_english && (
                      <div style={{ display: "flex" }}>
                        <span
                          style={{ marginLeft: 5 }}
                          dangerouslySetInnerHTML={{
                            __html: e.question_in_english,
                          }}
                        ></span>
                      </div>
                    )}
                  </td>
                  <td>
                    <span
                      style={{ marginLeft: 5 }}
                      dangerouslySetInnerHTML={{ __html: e.givenAnswer }}
                    ></span>
                    {e.givenEnglishAnswer && (
                      <span
                        style={{ marginLeft: 5 }}
                        dangerouslySetInnerHTML={{
                          __html: e.givenEnglishAnswer,
                        }}
                      ></span>
                    )}
                  </td>
                  {/* <td
                    style={{
                      display: "flex",
                      flexWrap: "wrap",
                    }}
                  >
                    {e.reported.split(",").map((v) => (
                      <p
                        key={e.questionid + v}
                        style={{
                          backgroundColor: "green",
                          borderRadius: 10,
                          color: "white",
                          padding: 4,
                          marginRight: 5,
                          marginBottom: 5,
                        }}
                      >
                        {v}
                      </p>
                    ))}
                  </td> */}
                  {/* <td>
                    {
                      e.givenAnswer
                        .split("$;")
                        .filter((ev) => ev.split("$$")[1] === "1")[0]
                        .split("$$")[0]
                    }
                  </td> */}
                  <td>
                    <i
                      className="bi bi-pencil-fill"
                      style={{ marginRight: 15, cursor: "pointer" }}
                      onClick={() =>
                        this.setState(
                          {
                            qNum: e.questionid,
                            popUpOpen: true,
                            editorLoading: true,
                            popupType: "Edit Question",
                          },
                          () => this.getOptions()
                        )
                      }
                    ></i>
                    <i
                      className="bi bi-trash-fill"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        this.setState(
                          {
                            qNum: e.questionid,
                          },
                          () => this.deleteQuestion()
                        )
                      }
                    ></i>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
        {this.renderPaginationButtons("paid", questionsCount)}
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      questionText: "",
      op1: "",
      op2: "",
      op3: "",
      op4: "",
      correctOption: 0,
      selectedCat: "",
      op5: "",
      addOptionClicked: false,
    }));
  };
  questionEditor = () => {
    const {
      qNum,
      editorLoading,
      optionsData,
      categoryList,
      selectedCat,
      correctOption,
      popupType,
      englishMedium,
      errQdata,
      questionInfo,
    } = this.state;
    let { qReportData } = this.state;
    const question =
      popupType === "Edit Error Question"
        ? errQdata.filter((e) => e.questionid === qNum)[0]
        : questionInfo;

    // this.setState({ selectedCat: question.cid });
    qReportData = popupType === "Edit Error Question" ? errQdata : qReportData;
    return (
      <>
        {(popupType === "Edit Question" ||
          popupType === "Edit Error Question") &&
        !editorLoading &&
        question !== undefined &&
        categoryList !== undefined ? (
          <>
            <div>
              Select Category{" "}
              <select
                onChange={(e) => this.setState({ selectedCat: e.target.value })}
              >
                {categoryList.map((category) => {
                  return (
                    <option
                      key={"category" + category.cid}
                      value={category.cid}
                      selected={
                        selectedCat === category.cid ||
                        question.cid === category.cid
                      }
                    >
                      {category.category_name}
                    </option>
                  );
                })}
              </select>
            </div>
            {/* {question?.question_in_english && ( */}
            <label style={{ color: "black" }}>
              <input
                type="checkbox"
                checked={englishMedium}
                onChange={() =>
                  this.setState((p) => ({ englishMedium: !p.englishMedium }))
                }
                style={{ marginRight: 10 }}
              />{" "}
              English Medium
            </label>
            {/* )} */}
            <div>
              Question
              <Editor
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={
                  englishMedium
                    ? question.question_in_english || question.question
                    : question.question
                }
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(e) =>
                  this.setState({
                    questionText: englishMedium ? question.question : e,
                    engquestionText: englishMedium
                      ? e
                      : question.question_in_english || question.question,
                  })
                }
              />
            </div>

            {optionsData.map((e, i) => (
              <div
                style={{
                  marginTop: 15,
                }}
              >
                Option {i + 1}{" "}
                <input
                  type={"radio"}
                  value={e.oid}
                  name={qNum}
                  id={e.oid}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value })
                  }
                  checked={correctOption == e.oid}
                />{" "}
                <label htmlFor={e.oid}>Correct option</label>
                {i >= 4 && (
                  <i
                    className="bi bi-trash-fill"
                    style={{ cursor: "pointer", marginLeft: 10 }}
                    onClick={() =>
                      this.setState(
                        {
                          oid: e.oid,
                          optionsData:
                            e.oid === 0
                              ? optionsData.slice(0, optionsData.length - 1)
                              : optionsData,
                          addOptionClicked: false,
                        },
                        () => {
                          if (e.oid !== 0) {
                            this.deleteOption();
                          }
                        }
                      )
                    }
                  ></i>
                )}
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={e.oid}
                  initialValue={
                    englishMedium
                      ? e.option_in_english || e.q_option
                      : e.q_option
                  }
                  init={{
                    height: 100,
                    width: "90%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      ["op" + (i + 1)]: englishMedium ? e.q_option : value,
                      ["engOp" + (i + 1)]: englishMedium
                        ? value
                        : e.option_in_english || e.q_option,
                      ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
            ))}
            {/* {[...options, { oid: 0, q_option: "", score: 0 }]} */}

            {optionsData.length < 5 && (
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  className="btn addbtn"
                  style={{ color: "white" }}
                  onClick={() =>
                    this.setState((p) => ({
                      optionsData: [
                        ...p.optionsData,
                        { oid: 0, q_option: "", score: 0 },
                      ],
                      addOptionClicked: true,
                    }))
                  }
                >
                  Add Option
                </Button>
              </div>
            )}
          </>
        ) : (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };

  AddquestionEditor = () => {
    const {
      categoryList,
      popupType,
      editorLoading,
      correctOption,
      addOptionClicked,
      op1,
      op2,
      op3,
      op4,
      selectedCat,
      op5,
    } = this.state;

    return (
      <>
        {popupType === "Add New Question" &&
        !editorLoading &&
        categoryList !== undefined ? (
          <>
            <div>
              Select Category{" "}
              <select
                onChange={(e) => this.setState({ selectedCat: e.target.value })}
              >
                <option value={null} selected={selectedCat === null}>
                  Please select category
                </option>
                {categoryList.map((category) => {
                  return (
                    <option
                      key={"category" + category.cid}
                      value={category.cid}
                      selected={selectedCat === category.cid}
                      required
                    >
                      {category.category_name}
                    </option>
                  );
                })}
              </select>
            </div>
            <div>
              Question
              <Editor
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={""}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(e) => this.setState({ questionText: e })}
              />
            </div>
            <div style={{ display: "flex", flexWrap: "wrap" }}>
              <div style={{ marginTop: 15 }}>
                Option {1}{" "}
                <input
                  type={"radio"}
                  value={op1}
                  name={"qNum"}
                  id={"qNum" + "op1"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op1Num: 1 })
                  }
                  checked={correctOption == op1}
                />{" "}
                <label htmlFor={"qNum" + "op1"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop1"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      ["op" + 1]: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              <div style={{ marginTop: 15 }}>
                Option {2}{" "}
                <input
                  type={"radio"}
                  value={op2}
                  name={"qNum"}
                  id={"qNum" + "op2"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op2Num: 1 })
                  }
                  checked={correctOption == op2}
                />{" "}
                <label htmlFor={"qNum" + "op2"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop2"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      op2: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              <div style={{ marginTop: 15 }}>
                Option {3}{" "}
                <input
                  type={"radio"}
                  value={op3}
                  name={"qNum"}
                  id={"qNum" + "op3"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op3Num: 1 })
                  }
                  checked={correctOption == op3}
                />{" "}
                <label htmlFor={"qNum" + "op3"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop3"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      op3: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              <div style={{ marginTop: 15 }}>
                Option {4}{" "}
                <input
                  type={"radio"}
                  value={op4}
                  name={"qNum"}
                  id={"qNum" + "op4"}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value, op4Num: 1 })
                  }
                  checked={correctOption == op4}
                />{" "}
                <label htmlFor={"qNum" + "op4"}>Correct option</label>
                <Editor
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={"addnewquestionop4"}
                  initialValue={""}
                  init={{
                    height: 100,
                    width: "100%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      op4: value,
                      // ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
              {addOptionClicked && (
                <div style={{ marginTop: 15 }}>
                  Option {5}{" "}
                  <input
                    type={"radio"}
                    value={op5}
                    name={"qNum"}
                    id={"qNum" + "op5"}
                    onChange={(e) =>
                      this.setState({
                        correctOption: e.target.value,
                        op5Num: 1,
                      })
                    }
                    checked={correctOption == op5}
                  />{" "}
                  <label htmlFor={"qNum" + "op5"}>Correct option</label>
                  <i
                    className="bi bi-trash-fill"
                    style={{ cursor: "pointer", marginLeft: 10 }}
                    onClick={() =>
                      this.setState({
                        op5: "",
                        op5Num: 0,
                        addOptionClicked: false,
                      })
                    }
                  ></i>
                  <Editor
                    // onInit={(evt, editor) => (editorRef.current = editor)}
                    key={"addnewquestionop4"}
                    initialValue={""}
                    init={{
                      height: 100,
                      width: "100%",
                      menubar: false,

                      plugins: [
                        "advlist autolink lists link image charmap print preview anchor",
                        "searchreplace visualblocks code fullscreen",
                        "insertdatetime media table paste code help wordcount",
                      ],
                      toolbar:
                        "undo redo | formatselect | " +
                        "bold italic backcolor | alignleft aligncenter " +
                        "alignright | bullist numlist | subscript superscript |  " +
                        " code " +
                        "removeformat",
                      content_style:
                        "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                    }}
                    onEditorChange={(value) =>
                      this.setState({
                        op5: value,
                        // ["op" + (i + 1) + "Num"]: e.oid,
                      })
                    }
                  />
                </div>
              )}
            </div>
            {!addOptionClicked && (
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  className="btn addbtn"
                  style={{ color: "white" }}
                  onClick={() =>
                    this.setState((p) => ({
                      addOptionClicked: true,
                    }))
                  }
                >
                  Add Option
                </Button>
              </div>
            )}
          </>
        ) : (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };

  saveQuestion = async () => {
    const {
      op1,
      op2,
      op3,
      op4,
      op5,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      englishMedium,
      engOp1,
      engOp2,
      op5Num,
      engOp3,
      engOp4,
      engOp5,
      qReportData,
      engquestionText,
    } = this.state;
    let { questionText } = this.state;

    console.log(correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op5,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      op5Num,
      selectedCat,
      correctOption: parseInt(correctOption),
      qNum,
      quizId,
      type: "EDITQuestion" + (englishMedium ? ",0,1" : ""),
    };

    if (englishMedium) {
      body.questionText = engquestionText || questionText;
      body.op1 = engOp1 || op1;
      body.op2 = engOp2 || op2;
      body.op3 = engOp3 || op3;
      body.op4 = engOp4 || op4;
      body.op5 = engOp5 || op5;
    }
    if (questionText === "") {
      NotificationManager.error(`Please Enter Question`);
    } else if (op1 === "") {
      NotificationManager.error(`Please Enter Option 1`);
    } else if (op2 === "") {
      NotificationManager.error(`Please Enter Option 2`);
    } else if (op3 === "") {
      NotificationManager.error(`Please Enter Option 3`);
    } else if (op4 === "") {
      NotificationManager.error(`Please Enter Option 4`);
    } else if (op1Num === 0 && op2Num === 0 && op3Num === 0 && op4Num === 0) {
      NotificationManager.error(`Please Select Correct Answer`);
    } else if (selectedCat === null) {
      NotificationManager.error(`Please Select Category`);
    } else {
      try {
        this.setState({ editorLoading: true });
        console.log("body", body);
        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-question`,
          body,
          { headers }
        );
        //   console.log(data);
        // const newQuestion = {
        //   cid: selectedCat,
        //   question: questionText,
        //   questionid: qNum,
        // };
        // const index = qReportData.findIndex(
        //   (ek) => ek == qReportData.filter((e) => e.questionid === qNum)[0]
        // );

        // const removedData = qReportData.filter((e) => e.questionid !== qNum);
        // const newQUestionsList = removedData.splice(index, 1, newQuestion);
        this.setState({
          editorLoading: false,
          popUpOpen: false,
          questionText: "",
          op1: "",
          op2: "",
          op3: "",
          op4: "",
          op5: "",
          op5Num: 0,
          op1Num: 0,
          op2Num: 0,
          op3Num: 0,
          op4Num: 0,
          correctOption: null,
          selectedCat: null,
          // qReportData: removedData,
          addOptionClicked: false,
        });
        this.getData();
        NotificationManager.success(`Question Updated Successfully..`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ editorLoading: false });
      }
    }
  };

  addNewQuestion = async () => {
    const {
      op1,
      op2,
      op3,
      op4,
      op5,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      op5Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
    } = this.state;
    let { questionText } = this.state;

    // console.log(questionText, op1, op2, op3, op4, selectedCat, correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op5,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      op5Num,
      selectedCat,
      correctOption: 1,
      qNum,
      quizId,
      type: "AddQuestion",
    };
    // console.log(body);
    if (questionText === "") {
      NotificationManager.error(`Please Enter Question`);
    } else if (op1 === "") {
      NotificationManager.error(`Please Enter Option 1`);
    } else if (op2 === "") {
      NotificationManager.error(`Please Enter Option 2`);
    } else if (op3 === "") {
      NotificationManager.error(`Please Enter Option 3`);
    } else if (op4 === "") {
      NotificationManager.error(`Please Enter Option 4`);
    } else if (op1Num === 0 && op2Num === 0 && op3Num === 0 && op4Num === 0) {
      NotificationManager.error(`Please Select Correct Answer`);
    } else if (selectedCat === null) {
      NotificationManager.error(`Please Select Category`);
    } else {
      try {
        this.setState({ editorLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-question`,
          body,
          { headers }
        );
        // console.log(data.data[0]);
        const newQuestion = {
          cid: selectedCat,
          question: questionText,
          questionid: data.data[0][0].questionId,
        };
        this.setState((prev) => ({
          editorLoading: false,
          popUpOpen: false,
          qReportData: [newQuestion, ...prev.qReportData],
          questionText: "",
          op1: "",
          op2: "",
          op3: "",
          op4: "",
          op4: "",
          op4Num: "",
          op1Num: 0,
          op2Num: 0,
          op3Num: 0,
          op4Num: 0,
          correctOption: null,
          selectedCat: null,
        }));
        NotificationManager.success(`Question Added Successfully..`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ editorLoading: false });
      }
    }
  };
  renderPopUp = () => {
    const { popUpOpen, popupType } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"lg"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={this.handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {popupType !== "Add New Question"
            ? this.questionEditor()
            : this.AddquestionEditor()}
        </DialogContent>
        <DialogActions>
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={
              popupType !== "Add New Question"
                ? this.saveQuestion
                : this.addNewQuestion
            }
          >
            Save changes
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  downloadDocxFile = async () => {
    const { catId } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    try {
      NotificationManager.success("Please wait...");
      const response = await axios.get(
        `${commonData["api"]}/download-questions/categoryQuestions/${catId}`,
        { headers, responseType: "blob" } // Set responseType to 'blob'
      );

      // Get the content disposition header to extract the filename
      const fileName = "Category_Questions_" + catId + ".docx";

      // Create a blob URL and trigger a download link
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });
      const blobUrl = URL.createObjectURL(blob);
      const downloadLink = document.createElement("a");
      downloadLink.href = blobUrl;
      downloadLink.download = fileName;
      downloadLink.click();

      // Clean up the blob URL
      URL.revokeObjectURL(blobUrl);
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };
  renderDownloadQuestions = () => {
    const { popUpOpen, popupType, categoryList } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={this.handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent" style={{ padding: 50 }}>
          <div>Download Category Questions</div>
          <div style={{ display: "flex" }}>
            <select
              style={{
                width: "230px",
                border: "2px solid orange",
                padding: 5,
                cursor: "pointer",
                marginBottom: 10,
                marginRight: 30,
              }}
              onChange={(e) => this.setState({ catId: e.target.value })}
            >
              <option value={0}>{"Filter Questions By Category"}</option>
              {categoryList.map((eachCateg) => (
                <option value={eachCateg.cid}>{eachCateg.category_name}</option>
              ))}
            </select>
            <button onClick={this.downloadDocxFile}>Download Questions</button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  render() {
    const { isLoading, login, popUpOpen, popupType } = this.state;
    // const parser = new DOMParser();
    const { from } = this.props;
    // var xml = parser.parseFromString(xml_data, "text/xml");
    // console.log(xml);
    // var rawFile = new XMLHttpRequest();
    // var allText;
    // rawFile.open("GET", xml_data, false);
    // rawFile.onreadystatechange = function () {
    //   if (rawFile.readyState === 4) {
    //     if (rawFile.status === 200 || rawFile.status == 0) {
    //       allText = rawFile.responseText;
    //       alert(allText);
    //     }
    //   }
    // };
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            {from !== "examslist" && (
              <div className="desktopsidebar">
                <div className="desktopsidebarmenuexamdetailsAdmin">
                  <AdminMenu />
                </div>
                <Header />

                <Divider color="white" />

                <div className="viewresultsdesktop admin">
                  {this.reportedQuestionsTable()}
                  {this.ErrQuestionsTable()}
                  {popUpOpen && popupType !== "export" && this.renderPopUp()}
                  {popUpOpen &&
                    popupType === "export" &&
                    this.renderDownloadQuestions()}
                  {this.renderImportPopUp()}
                </div>
                {/* <input
                type="file"
                name="word_file"
                onChange={(e) => this.showFile(e)}
              /> */}
              </div>
            )}
            {from === "examslist" && (
              <>
                {this.ErrQuestionsTable()}
                {popUpOpen && popupType !== "export" && this.renderPopUp()}
                {popUpOpen &&
                  popupType === "export" &&
                  this.renderDownloadQuestions()}
              </>
            )}
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default Questions;
