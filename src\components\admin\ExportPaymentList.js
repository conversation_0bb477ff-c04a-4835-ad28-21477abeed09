import react from "react";
import Loader from "../Loader";
import axios from "axios";
import commonData from "../../importanValue";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import ExportCSV from "./ExportCsv";
class ExportPaymentList extends react.Component {
  state = {
    isLoading: false,
    paymentdata: [],
    date: "",
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const { match } = this.props;
    const { params } = match;
    const { date } = params;

    try {
      const headers = {
        "Content-Type": "application/json",

        "Access-Control-Allow-Origin": "*",
      };
      this.setState({ isLoading: true });
      console.log(date);
      const data = await axios.get(
        `${commonData["api"]}/export-payment-details/${date}`,
        { headers }
      );
      console.log(data);
      this.setState({
        paymentdata: data.data[0],

        date: date,
        isLoading: false,
      });
    } catch (err) {
      console.log(err);
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  render() {
    const { isLoading, paymentdata, date } = this.state;
    return (
      <>
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && paymentdata.length > 0 ? (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              margin: 50,
            }}
          >
            <ExportCSV
              csvData={paymentdata}
              fileName={"Payment Report of " + date}
              title={"Download Payment Report of " + date}
              type={"exportpayment"}
            />
          </div>
        ) : (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
              alignItems: "center",
              color: "#fff",
              margin: 50,
            }}
          >
            <p>No payments are done on this date ( {date} )</p>
          </div>
        )}
      </>
    );
  }
}
export default ExportPaymentList;
