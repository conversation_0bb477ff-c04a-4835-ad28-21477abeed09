import React from "react";
import { Link } from "react-router-dom";
import {
  Box,
  Typography,
  Paper,
  Avatar,
  Card,
  Button
} from "@mui/material";
import AssignmentIcon from '@mui/icons-material/Assignment';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import "./styles.css";

// Theme colors - consistent with the application's design language
const THEME_COLORS = {
  primary: "#ff9800", // Orange as primary color
  secondary: "#262626",
  accent: "#4caf50", // Green as accent color
  success: "#4caf50", // Green as success color
  warning: "#ed6c02",
  info: "#0288d1",
  error: "#d32f2f",
  background: {
    light: "#ffffff",
    dark: "#262626",
    paper: "#f5f5f5",
    card: "#ffffff",
  },
  text: {
    primary: "#212121",
    secondary: "#666666",
    light: "#ffffff",
  },
  border: {
    light: "#e0e0e0",
    highlight: "#ffecb3", // Light orange for highlight
  }
};

const UserProfile = ({ data }) => {
  const { first_name, last_name, district } = data;
  const userName = first_name.slice(0, 1) + first_name.slice(1).toLowerCase();
  const userLastName = last_name
    ? last_name.slice(0, 1) + last_name.slice(1).toLowerCase()
    : "";

  localStorage.setItem("name", userName);

  // Function to create initials for avatar
  const getInitials = () => {
    return (first_name[0] + (last_name ? last_name[0] : "")).toUpperCase();
  };

  // Generate a color based on the user's name
  const stringToColor = (string) => {
    let hash = 0;
    for (let i = 0; i < string.length; i++) {
      hash = string.charCodeAt(i) + ((hash << 5) - hash);
    }

    // Use orange and green colors as a base
    const colors = [
      THEME_COLORS.primary, // orange
      "#e65100", // darker orange
      THEME_COLORS.success, // green
      "#2e7d32", // darker green
    ];

    return colors[Math.abs(hash) % colors.length];
  };

  const avatarColor = stringToColor(userName + userLastName);

  return (
    <Box sx={{ my: 1 }}>
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          overflow: "hidden",
          boxShadow: "0 2px 8px rgba(0,0,0,0.06)",
          border: `1px solid ${THEME_COLORS.border.light}`,
        }}
      >
        {/* Compact header with gradient background */}
        <Box
          sx={{
            background: `linear-gradient(135deg, ${THEME_COLORS.primary} 0%, ${THEME_COLORS.success} 100%)`,
            py: 1.5,
            px: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Avatar
              sx={{
                width: 40,
                height: 40,
                bgcolor: avatarColor,
                fontSize: '1rem',
                fontWeight: 600,
                border: '2px solid rgba(255,255,255,0.3)'
              }}
            >
              {getInitials()}
            </Avatar>
            <Box sx={{ ml: 1.5 }}>
              <Typography variant="subtitle1" sx={{ color: 'white', fontWeight: 500, lineHeight: 1.2 }}>
                {userName} {userLastName}
              </Typography>
              {district && (
                <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.85)', display: 'flex', alignItems: 'center' }}>
                  <Box
                    component="span"
                    sx={{
                      width: 6,
                      height: 6,
                      borderRadius: '50%',
                      bgcolor: THEME_COLORS.success,
                      display: 'inline-block',
                      mr: 0.8
                    }}
                  />
                  {district}
                </Typography>
              )}
            </Box>
          </Box>
          <Button
            component={Link}
            to="/user-profile"
            size="small"
            variant="outlined"
            sx={{
              color: 'white',
              borderColor: 'rgba(255,255,255,0.3)',
              fontSize: '0.75rem',
              py: 0.5,
              '&:hover': {
                borderColor: 'rgba(255,255,255,0.6)',
                backgroundColor: 'rgba(255,255,255,0.1)'
              }
            }}
          >
            Profile
          </Button>
        </Box>

        {/* Quick actions section - horizontal layout */}
        <Box sx={{ p: 1.5 }}>
          <Box sx={{ display: 'flex', gap: 1, mb: 0 }}>
            <Link
              to="/user/reported-questions"
              style={{ textDecoration: 'none', flex: 1 }}
            >
              <Card
                elevation={0}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  borderRadius: 1.5,
                  border: `1px solid ${THEME_COLORS.border.light}`,
                  transition: 'all 0.2s ease',
                  height: '100%',
                  '&:hover': {
                    borderColor: THEME_COLORS.border.highlight,
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    p: 0.6,
                    borderRadius: 1,
                    bgcolor: 'rgba(255, 152, 0, 0.08)',
                    color: THEME_COLORS.primary,
                    mb: 0.5
                  }}
                >
                  <AssignmentIcon fontSize="small" />
                </Box>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, color: THEME_COLORS.text.primary, textAlign: 'center' }}
                >
                  Reported Questions
                </Typography>
              </Card>
            </Link>

            <Link
              to="/user/user-wallet"
              style={{ textDecoration: 'none', flex: 1 }}
            >
              <Card
                elevation={0}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  borderRadius: 1.5,
                  border: `1px solid ${THEME_COLORS.border.light}`,
                  transition: 'all 0.2s ease',
                  height: '100%',
                  '&:hover': {
                    borderColor: THEME_COLORS.border.highlight,
                    transform: 'translateY(-2px)'
                  }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    p: 0.6,
                    borderRadius: 1,
                    bgcolor: 'rgba(76, 175, 80, 0.08)',
                    color: THEME_COLORS.success,
                    mb: 0.5
                  }}
                >
                  <LocalOfferIcon fontSize="small" />
                </Box>
                <Typography
                  variant="body2"
                  sx={{ fontWeight: 600, color: THEME_COLORS.text.primary, textAlign: 'center' }}
                >
                  Get Discount
                </Typography>
              </Card>
            </Link>
          </Box>
        </Box>
      </Paper>
    </Box>
  );
};

export default UserProfile;
