import React from "react";
import Button from "@mui/material/Button";
import * as FileSaver from "file-saver";

import * as XLSX from "xlsx";
import { NotificationManager } from "react-notifications";
let x = 0;
let y = 0;
let z = 0;
const ExportCSV = ({ csvData, fileName, title, emptyFn, type }) => {
  const fileType =
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";

  const fileExtension = ".xlsx";

  const exportToCSV = (csvData, fileName) => {
    y = 1;
    const ws = XLSX.utils.json_to_sheet(csvData);

    const wb = { Sheets: { data: ws }, SheetNames: ["data"] };

    const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });

    const data = new Blob([excelBuffer], { type: fileType });

    FileSaver.saveAs(data, fileName + fileExtension);
    emptyFn();
  };

  if (type === "exportpayment" && x == 0) {
    exportToCSV(csvData, fileName);
    x = 1;
  } else if (type === "ExportAllUsers" && y === 0) {
    y = 1;
    exportToCSV(csvData, fileName);

    // return window.location.reload(false);
  } else if (type === "ExportAllGidUsers") {
    console.log(type);

    exportToCSV(csvData, fileName);

    // return window.location.reload(false);
  } else {
    return (
      <Button
        className="btn exportbtn"
        onClick={(e) => {
          if (csvData.length === 0) {
            NotificationManager.error("Please Select Atleast One User");
          } else {
            exportToCSV(csvData, fileName);
            // emptyFn();
          }
        }}
      >
        {title}
      </Button>
    );
  }
};

const fileType =
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8";

const fileExtension = ".xlsx";

export const exportToCSVD = (csvData, fileName) => {
  y = 1;
  const ws = XLSX.utils.json_to_sheet(csvData);

  const wb = { Sheets: { data: ws }, SheetNames: ["data"] };

  const excelBuffer = XLSX.write(wb, { bookType: "xlsx", type: "array" });

  const data = new Blob([excelBuffer], { type: fileType });

  FileSaver.saveAs(data, fileName + fileExtension);
};
export default ExportCSV;