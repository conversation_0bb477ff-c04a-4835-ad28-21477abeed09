import React, { Component } from "react";
import Header from "../Header";
import Loader from "../Loader";
import DesktopMenu from "../DesktopMenu";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import commonData from "../../importanValue";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import {
  Typography,
  Box,
  Paper,
  Divider,
  Container,
  Chip,
  Pagination,
  Al<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>le,
  Tabs,
  Tab,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Badge,
  Card,
  CardContent,
  CardHeader,
} from "@mui/material";
import FeedbackIcon from "@mui/icons-material/Feedback";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ErrorIcon from "@mui/icons-material/Error";
import HourglassEmptyIcon from "@mui/icons-material/HourglassEmpty";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import QuizIcon from "@mui/icons-material/Quiz";
import LibraryBooksIcon from "@mui/icons-material/LibraryBooks";

const THEME_COLORS = {
  primary: "#1976d2",
  secondary: "#262626",
  accent: "#ff9800",
  success: "#2e7d32",
  warning: "#ed6c02",
  info: "#0288d1",
  error: "#d32f2f",
  background: {
    light: "#ffffff",
    dark: "#262626",
    paper: "#f5f5f5",
    card: "#ffffff",
  },
  text: {
    primary: "#212121",
    secondary: "#666666",
    light: "#ffffff",
  },
  border: {
    light: "#e0e0e0",
    highlight: "#bbdefb",
  },
};

class ReportedQuestionsByMe extends Component {
  state = {
    isLoading: true,
    verifiedQuestions: [],
    pendingQuestions: [],
    currentPage: 1,
    questionsPerPage: 5,
    totalPages: 1,
    error: null,
    activeTab: 0,
  };

  componentDidMount() {
    this.fetchReportedQuestions();
  }

  fetchReportedQuestions = async () => {
    try {
      this.setState({ isLoading: true });
      const token = Cookie.get("jwt_token");
      const phone = localStorage.getItem("num");

      if (!phone) {
        throw new Error("Phone number not found. Please log in again.");
      }

      // Fetch verified questions
      const verifiedResponse = await axios.post(
        `${commonData["api"]}/support`,
        {
          type: "getUserReportedQuestions",
          search: phone,
          qid: 0,
        },
        {
          headers: {
            "Content-Type": "application/json",
            authorization: token,
            "Access-Control-Allow-Origin": "*",
          },
        }
      );

      let verifiedQuestions = [];
      let pendingQuestions = [];

      if (verifiedResponse.data && Array.isArray(verifiedResponse.data[0])) {
        verifiedQuestions = verifiedResponse.data[0].filter(
          (question) => question.verified === 1
        );
        pendingQuestions = verifiedResponse.data[0].filter(
          (question) => question.verified !== 1
        );
      }

      // Group the questions by exam name
      const groupedVerified = this.groupQuestionsByExam(verifiedQuestions);
      const groupedPending = this.groupQuestionsByExam(pendingQuestions);

      this.setState({
        verifiedQuestions: groupedVerified,
        pendingQuestions: groupedPending,
        totalPages: Math.ceil(
          Object.keys(groupedVerified).length / this.state.questionsPerPage
        ),
        isLoading: false,
      });
    } catch (error) {
      console.error("Error fetching reported questions:", error);
      this.setState({
        error: error.message || "Failed to fetch reported questions",
        isLoading: false,
      });
      NotificationManager.error(
        error.message || "Failed to fetch reported questions"
      );
    }
  };

  handlePageChange = (event, page) => {
    this.setState({
      currentPage: page,
    });
  };

  handleTabChange = (event, newValue) => {
    this.setState({
      activeTab: newValue,
      currentPage: 1,
      totalPages: Math.ceil(
        Object.keys(
          newValue === 0
            ? this.state.verifiedQuestions
            : this.state.pendingQuestions
        ).length / this.state.questionsPerPage
      ),
    });
  };

  groupQuestionsByExam = (questions) => {
    return questions.reduce((grouped, question) => {
      // Clean the exam name to remove HTML artifacts
      const examName = this.cleanHtml(question.quiz_name || "Unnamed Exam");

      if (!grouped[examName]) {
        grouped[examName] = [];
      }

      grouped[examName].push(question);
      return grouped;
    }, {});
  };

  getPaginatedExams = () => {
    const {
      currentPage,
      questionsPerPage,
      activeTab,
      verifiedQuestions,
      pendingQuestions,
    } = this.state;

    // Get the appropriate question group based on active tab
    const questionGroups =
      activeTab === 0 ? verifiedQuestions : pendingQuestions;

    // Get exam names for the current page
    const examNames = Object.keys(questionGroups);
    const indexOfLastExam = currentPage * questionsPerPage;
    const indexOfFirstExam = indexOfLastExam - questionsPerPage;
    const currentExams = examNames.slice(indexOfFirstExam, indexOfLastExam);

    // Create an object with only the current page's exams
    return currentExams.reduce((result, examName) => {
      result[examName] = questionGroups[examName];
      return result;
    }, {});
  };

  cleanHtml = (html) => {
    // Remove various HTML artifacts and clean the string
    if (!html) return "";
    return html
      .replace(/<\/p><p>$/, "")
      .replace(/<\/p><p>/, " ")
      .replace(/<[^>]*>/g, "");
  };

  render() {
    const {
      isLoading,
      error,
      totalPages,
      currentPage,
      verifiedQuestions,
      pendingQuestions,
      activeTab,
    } = this.state;

    const paginatedExams = this.getPaginatedExams();
    const hasVerifiedQuestions = Object.keys(verifiedQuestions).length > 0;
    const hasPendingQuestions = Object.keys(pendingQuestions).length > 0;
    const totalVerified = Object.values(verifiedQuestions).flat().length;
    const totalPending = Object.values(pendingQuestions).flat().length;

    return (
      <div className="desktopsidebar">
        <div className="homedesktopsidebarmenuexamdetails">
          <DesktopMenu />
        </div>
        <Header />
        <Divider style={{ backgroundColor: THEME_COLORS.border.light }} />

        <Container maxWidth="lg" sx={{ py: { xs: 2, md: 4 } }}>
          {/* Page Header */}
          <Box
            sx={{
              mb: 4,
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              justifyContent: "space-between",
              alignItems: { xs: "flex-start", sm: "center" },
              gap: 2,
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <LibraryBooksIcon
                sx={{
                  fontSize: { xs: 36, md: 48 },
                  color: THEME_COLORS.primary,
                  mr: 2,
                  p: 1,
                  borderRadius: "50%",
                  backgroundColor: "white",
                }}
              />
              <Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: "orange",
                    fontSize: { xs: "1.75rem", md: "2.25rem" },
                  }}
                >
                  My Reported Questions
                </Typography>
                <Typography variant="subtitle1" sx={{ color: "white" }}>
                  Track the status of questions you've reported for improvement
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Main Content Card */}
          <Card
            elevation={0}
            sx={{
              border: `1px solid ${THEME_COLORS.border.light}`,
              borderRadius: 3,
              overflow: "hidden",
              boxShadow: "0 6px 18px rgba(0,0,0,0.06)",
            }}
          >
            {/* Tabs Section */}
            <Box
              sx={{
                p: { xs: 2, md: 3 },
                borderBottom: `1px solid ${THEME_COLORS.border.light}`,
                bgcolor: THEME_COLORS.background.paper,
              }}
            >
              <Tabs
                value={activeTab}
                onChange={this.handleTabChange}
                variant="fullWidth"
                sx={{
                  minHeight: 64,
                  "& .MuiTab-root": {
                    minHeight: 64,
                    fontWeight: 600,
                    fontSize: "1rem",
                    textTransform: "none",
                    borderRadius: "8px",
                    transition: "all 0.3s ease",
                    "&:hover": {
                      backgroundColor: "rgba(25, 118, 210, 0.04)",
                    },
                  },
                  "& .Mui-selected": {
                    backgroundColor: "rgba(25, 118, 210, 0.08)",
                    color: `${THEME_COLORS.primary} !important`,
                  },
                  "& .MuiTabs-indicator": {
                    height: 3,
                    borderRadius: "3px",
                  },
                }}
              >
                <Tab
                  label={
                    <Badge
                      badgeContent={totalVerified}
                      color="success"
                      max={999}
                      sx={{
                        "& .MuiBadge-badge": {
                          fontSize: "0.75rem",
                          fontWeight: 700,
                          height: 20,
                          minWidth: 20,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          px: { xs: 0.5, md: 1 },
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <CheckCircleIcon fontSize="small" sx={{ mr: 1 }} />
                        Verified Questions
                      </Box>
                    </Badge>
                  }
                />
                <Tab
                  label={
                    <Badge
                      badgeContent={totalPending}
                      color="warning"
                      max={999}
                      sx={{
                        "& .MuiBadge-badge": {
                          fontSize: "0.75rem",
                          fontWeight: 700,
                          height: 20,
                          minWidth: 20,
                        },
                      }}
                    >
                      <Box
                        sx={{
                          px: { xs: 0.5, md: 1 },
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        <HourglassEmptyIcon fontSize="small" sx={{ mr: 1 }} />
                        Pending Questions
                      </Box>
                    </Badge>
                  }
                />
              </Tabs>
            </Box>

            {/* Content Section */}
            <Box
              sx={{
                p: { xs: 2, md: 3 },
                bgcolor: THEME_COLORS.background.paper,
              }}
            >
              {activeTab === 0 && (
                <Alert
                  severity="info"
                  variant="outlined"
                  icon={<CheckCircleIcon fontSize="inherit" />}
                  sx={{
                    mb: 3,
                    border: `1px solid ${THEME_COLORS.info}20`,
                    backgroundColor: `${THEME_COLORS.info}10`,
                    "& .MuiAlert-icon": {
                      color: THEME_COLORS.info,
                    },
                  }}
                >
                  <AlertTitle sx={{ fontWeight: 600 }}>
                    Verified Questions
                  </AlertTitle>
                  These questions have been reviewed by our academic team and
                  updated with correct answers. Thank you for contributing to
                  our learning platform's quality!
                </Alert>
              )}

              {activeTab === 1 && (
                <Alert
                  severity="warning"
                  variant="outlined"
                  icon={<HourglassEmptyIcon fontSize="inherit" />}
                  sx={{
                    mb: 3,
                    border: `1px solid ${THEME_COLORS.warning}20`,
                    backgroundColor: `${THEME_COLORS.warning}10`,
                    "& .MuiAlert-icon": {
                      color: THEME_COLORS.warning,
                    },
                  }}
                >
                  <AlertTitle sx={{ fontWeight: 600 }}>
                    Pending Questions
                  </AlertTitle>
                  These questions are currently under review by our academic
                  team.
                </Alert>
              )}

              {isLoading ? (
                <Box
                  sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    flexDirection: "column",
                    py: 8,
                  }}
                >
                  <Loader />
                  <Typography
                    variant="body1"
                    sx={{ mt: 3, color: THEME_COLORS.text.secondary }}
                  >
                    Loading your reported questions...
                  </Typography>
                </Box>
              ) : error ? (
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    p: 3,
                    bgcolor: `${THEME_COLORS.error}10`,
                    borderRadius: 2,
                    border: `1px solid ${THEME_COLORS.error}30`,
                  }}
                >
                  <ErrorIcon sx={{ color: THEME_COLORS.error, mr: 2 }} />
                  <Typography
                    variant="body1"
                    sx={{ color: THEME_COLORS.error }}
                  >
                    {error}
                  </Typography>
                </Box>
              ) : (activeTab === 0 && !hasVerifiedQuestions) ||
                (activeTab === 1 && !hasPendingQuestions) ? (
                <Box
                  sx={{
                    textAlign: "center",
                    py: 8,
                    px: 4,
                    bgcolor: THEME_COLORS.background.paper,
                    borderRadius: 2,
                    border: `1px dashed ${THEME_COLORS.border.light}`,
                  }}
                >
                  {activeTab === 0 ? (
                    <FeedbackIcon
                      sx={{
                        fontSize: 60,
                        color: THEME_COLORS.text.secondary,
                        opacity: 0.4,
                        mb: 2,
                      }}
                    />
                  ) : (
                    <HourglassEmptyIcon
                      sx={{
                        fontSize: 60,
                        color: THEME_COLORS.text.secondary,
                        opacity: 0.4,
                        mb: 2,
                      }}
                    />
                  )}
                  <Typography
                    variant="h5"
                    sx={{
                      color: THEME_COLORS.text.secondary,
                      fontWeight: 600,
                      mb: 1,
                    }}
                  >
                    {activeTab === 0
                      ? "No Verified Questions Yet"
                      : "No Questions Pending Review"}
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: THEME_COLORS.text.secondary,
                      maxWidth: 500,
                      mx: "auto",
                    }}
                  >
                    {activeTab === 0
                      ? "When our academic team verifies questions you've reported, they'll appear here. Thank you for helping improve our question bank!"
                      : "You don't have any questions pending verification at this time. Report questions during your exam attempts to see them here."}
                  </Typography>
                </Box>
              ) : (
                <>
                  {Object.entries(paginatedExams).map(
                    ([examName, questions]) => (
                      <Accordion
                        key={examName}
                        defaultExpanded={false}
                        sx={{
                          mb: 3,
                          boxShadow: "none",
                          border: `1px solid ${THEME_COLORS.border.light}`,
                          borderRadius: "12px !important",
                          "&:before": { display: "none" },
                          overflow: "hidden",
                          "&.Mui-expanded": {
                            borderColor: THEME_COLORS.primary,
                          },
                        }}
                      >
                        <AccordionSummary
                          expandIcon={
                            <ExpandMoreIcon
                              sx={{ color: THEME_COLORS.primary }}
                            />
                          }
                          sx={{
                            backgroundColor: "rgba(25, 118, 210, 0.04)",
                            borderTopLeftRadius: "12px",
                            borderTopRightRadius: "12px",
                            "&:hover": {
                              backgroundColor: "rgba(25, 118, 210, 0.08)",
                            },
                            "&.Mui-expanded": {
                              backgroundColor: "rgba(25, 118, 210, 0.1)",
                              borderBottom: `1px solid ${THEME_COLORS.border.light}`,
                            },
                          }}
                        >
                          <Box
                            sx={{
                              display: "flex",
                              alignItems: "center",
                              justifyContent: "space-between",
                              width: "100%",
                            }}
                          >
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                              {/* <QuizIcon sx={{ mr: 2, color: THEME_COLORS.primary }} /> */}
                              <Typography
                                variant="h6"
                                sx={{
                                  color: THEME_COLORS.primary,
                                  fontWeight: 600,
                                  fontSize: {
                                    xs: "1rem",
                                    sm: "1.125rem",
                                    md: "1.25rem",
                                  },
                                }}
                              >
                                {examName}
                              </Typography>
                            </Box>
                            <Chip
                              label={`${questions.length} question${
                                questions.length > 1 ? "s" : ""
                              }`}
                              size="small"
                              color="primary"
                              variant="outlined"
                              sx={{
                                ml: 2,
                                borderRadius: "16px",
                                fontWeight: 600,
                                "& .MuiChip-label": {
                                  px: 1.5,
                                },
                              }}
                            />
                          </Box>
                        </AccordionSummary>
                        <AccordionDetails sx={{ p: 0 }}>
                          {questions.map((question, index) => (
                            <Box
                              key={index}
                              sx={{
                                p: { xs: 2, sm: 3 },
                                borderTop:
                                  index !== 0
                                    ? `1px dashed ${THEME_COLORS.border.light}`
                                    : "none",
                                "&:hover": {
                                  backgroundColor:
                                    activeTab === 0
                                      ? "rgba(46, 125, 50, 0.04)"
                                      : "rgba(237, 108, 2, 0.04)",
                                },
                                transition: "background-color 0.2s ease",
                              }}
                            >
                              {/* Question */}
                              <Box
                                sx={{
                                  p: 3,
                                  borderRadius: 2,
                                  mb: 2.5,
                                  bgcolor: THEME_COLORS.background.paper,
                                  border: `1px solid ${THEME_COLORS.border.light}`,
                                  position: "relative",
                                  overflow: "hidden",
                                  boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
                                  "&::before": {
                                    content: '""',
                                    position: "absolute",
                                    left: 0,
                                    top: 0,
                                    bottom: 0,
                                    width: 4,
                                    backgroundColor:
                                      activeTab === 0
                                        ? THEME_COLORS.success
                                        : THEME_COLORS.warning,
                                  },
                                }}
                              >
                                <Typography
                                  variant="subtitle1"
                                  sx={{
                                    color: THEME_COLORS.text.secondary,
                                    fontWeight: 600,
                                    mb: 1,
                                    display: "flex",
                                    alignItems: "center",
                                  }}
                                >
                                  <span
                                    style={{
                                      backgroundColor:
                                        activeTab === 0
                                          ? THEME_COLORS.success
                                          : THEME_COLORS.warning,
                                      color: "#fff",
                                      padding: "2px 8px",
                                      borderRadius: "12px",
                                      fontSize: "0.875rem",
                                      marginRight: "8px",
                                    }}
                                  >
                                    Q
                                  </span>
                                  Question
                                </Typography>
                                <Box
                                  sx={{
                                    px: 1,
                                    fontFamily:
                                      '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
                                    "& p": {
                                      margin: 0,
                                      lineHeight: 1.7,
                                      color: THEME_COLORS.text.primary,
                                    },
                                  }}
                                >
                                  <div
                                    dangerouslySetInnerHTML={{
                                      __html: question.question,
                                    }}
                                  />
                                </Box>
                              </Box>

                              {/* Answer or Report Info */}
                              {activeTab === 0 ? (
                                <Box
                                  sx={{
                                    p: 3,
                                    borderRadius: 2,
                                    bgcolor: "rgba(46, 125, 50, 0.04)",
                                    border: `1px solid rgba(46, 125, 50, 0.15)`,
                                    position: "relative",
                                    overflow: "hidden",
                                    boxShadow: "0 2px 8px rgba(0,0,0,0.04)",
                                    "&::before": {
                                      content: '""',
                                      position: "absolute",
                                      left: 0,
                                      top: 0,
                                      bottom: 0,
                                      width: 4,
                                      backgroundColor: THEME_COLORS.success,
                                    },
                                  }}
                                >
                                  <Typography
                                    variant="subtitle1"
                                    sx={{
                                      color: THEME_COLORS.success,
                                      fontWeight: 600,
                                      mb: 1,
                                      display: "flex",
                                      alignItems: "center",
                                    }}
                                  >
                                    <CheckCircleIcon
                                      sx={{ mr: 1, fontSize: "1.25rem" }}
                                    />
                                    Verified Correct Answer:
                                  </Typography>
                                  <Box
                                    sx={{
                                      px: 1,
                                      fontFamily:
                                        '"Segoe UI", Tahoma, Geneva, Verdana, sans-serif',
                                      "& p": {
                                        margin: 0,
                                        lineHeight: 1.7,
                                        color: THEME_COLORS.text.primary,
                                      },
                                    }}
                                  >
                                    <div
                                      dangerouslySetInnerHTML={{
                                        __html: question.correct_answer,
                                      }}
                                    />
                                  </Box>
                                </Box>
                              ) : null}
                            </Box>
                          ))}
                        </AccordionDetails>
                      </Accordion>
                    )
                  )}

                  {totalPages > 1 && (
                    <Box
                      sx={{
                        display: "flex",
                        justifyContent: "center",
                        mt: 4,
                        pb: 2,
                      }}
                    >
                      <Pagination
                        count={totalPages}
                        page={currentPage}
                        onChange={this.handlePageChange}
                        color="primary"
                        variant="outlined"
                        shape="rounded"
                        size="large"
                        sx={{
                          "& .MuiPaginationItem-root": {
                            fontWeight: 600,
                            fontSize: "0.875rem",
                            borderWidth: 2,
                          },
                        }}
                      />
                    </Box>
                  )}
                </>
              )}
            </Box>
          </Card>
        </Container>
        <NotificationContainer />
      </div>
    );
  }
}

export default ReportedQuestionsByMe;
