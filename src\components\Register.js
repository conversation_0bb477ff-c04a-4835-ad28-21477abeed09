import React from "react";
import Header from "./Header";
import Loader from "./Loader";
// import axios from "axios";
import TextField from "@mui/material/TextField";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import DesktopMenu from "./DesktopMenu";
import "./styles.css";
import Divider from "@mui/material/Divider";
import Button from "@mui/material/Button";
import axios from "axios";
import commonData from "../importanValue";
import Cookies from "js-cookie";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
class Register extends React.Component {
  state = {
    name: "",
    surname: "",
    district: "10",
    dob: null,
    isLoading: false,
    email: "",
    err: "",
    phoneNum: "",
    refer: localStorage.getItem("refer"),
    dobdate: "",
    dobmonth: "",
    dobyear: "",
  };

  registerAccount = (e) => {
    e.preventDefault();
    const { surname, email, district, dob, dobdate, dobmonth, dobyear } =
      this.state;
    // const d = new Date(dob);
    const newdob = dobdate + " / " + dobmonth + " / " + dobyear;
    console.log(newdob);
    if (!email.includes("@gmail.com")) {
      this.setState({ err: "Enter Valid Email" });
    } else if (district === "10") {
      this.setState({ err: "Please select your District" });
    } else if (surname.length < 3) {
      this.setState({ err: "Please enter complete surname" });
    } else if (
      dobdate.length < 2 ||
      dobmonth.length < 2 ||
      dobyear.length < 4
    ) {
      this.setState({ err: "Please Enter Valid Date of Birth" });
    } else {
      this.setState({ isLoading: true, dob: newdob }, this.activeAccount);
    }
  };

  activeAccount = async () => {
    const { history, location } = this.props;
    const nextUrl = location.search.split("?next=")[1];
    const { name, surname, email, district, dob, refer } = this.state;
    const phoneNum = localStorage.getItem("num");
    const registrationData = {
      name,
      surname,
      email,
      district: district + "$$" + refer,
      dob: dob,
      number: phoneNum,
      type: "CREATE",
    };
    // console.log(registrationData);
    try {
      const response = await axios.post(
        `${commonData["api"]}/create-new-user`,
        registrationData
      );
      this.setState({ isLoading: true });
      // console.log(response.data);
      Cookies.set("jwt_token", response.data.jwt, { expires: 365 });
      localStorage.setItem("uid", response.data.result[0].uid);
      if (nextUrl !== undefined) {
        history.replace(nextUrl + "?msg=Your Account Activated Succesfully");
        // console.log("going to", nextUrl);
      } else {
        history.replace("/?msg=Your Account Activated Succesfully");
      }
    } catch (err) {
      // console.log(err);
      this.setState({ isLoading: true });
      this.setState({ err: "Please Enter Correct details" });
    }
  };
  onChangeEmail = (e) => {
    // console.log(e.target.value);
    this.setState({ email: e.target.value, err: "" });
  };
  onChangeName = (e) => {
    // console.log(e.target.value);
    this.setState({ name: e.target.value, err: "" });
  };
  onChangeSurName = (e) => {
    // console.log(e.target.value);
    this.setState({ surname: e.target.value, err: "" });
  };
  onChangeDistrict = (e) => {
    // console.log(e.target.value);
    this.setState({ district: e.target.value, err: "" });
  };

  formatNumber = (num, from, limit) => {
    num =
      Number(num) > from && Number(num) < 10 && num.length == 1
        ? "0" + num
        : num;
    if (Number(num) > limit) {
      num = num.substr(1, 2);
      num =
        Number(num) > from && Number(num) < 10 && num.length == 1
          ? "0" + num
          : num;
    }
    return num;
  };
  // dateFormat = (e) => {
  //   var dateValue = e;
  //   if (/\D$/.test(dateValue)) {
  //     dateValue = dateValue.substr(0, dateValue.length - 3);
  //   }
  //   dateValue = dateValue.replaceAll(" ", "");
  //   var arr = dateValue.split("/");

  //   if (arr[0]) arr[0] = this.formatNumber(arr[0], 3, 31);
  //   if (arr[1]) arr[1] = this.formatNumber(arr[1], 1, 12);

  //   var result = arr.map(function (val, index) {
  //     return val.length == 2 && index < 2 ? val + " / " : val;
  //   });
  //   this.setState({ dob: result.join("").substr(0, 14) });
  // };

  onChangeDob = (e) => {
    // console.log(e.target.name);
    // this.dateFormat(e.target.value);
    this.setState({ [e.target.name]: e.target.value, err: "" });
  };

  onChangeRefer = (e) => {
    e.preventDefault();
    this.setState({ refer: e.target.value, err: "" });
  };

  render() {
    const { name, email, district, dob, err, isLoading, surname, refer } =
      this.state;
    console.log(refer);
    return (
      <>
        <div className="desktopsidebar">
          <div className="desktopsidebarmenuexamdetails">
            <DesktopMenu />
          </div>
          <Header />

          <Divider color="white" />
          <div className="viewresultsdesktop22 desktopregister">
            <div className="packages-container">
              <div className="instructions-register">
                <p className="main-register">గుర్తుంచుకోండి</p>
                <ol>
                  <li> మీ పనిచేసే ఈమెయిల్ చిరునామా మాత్రమే ఇవ్వండి</li>
                  <li> Name, Surname ఖచ్చితంగా అప్ డేట్ చేయాలి</li>
                  <li>
                    జిల్లా, వాట్సాప్ నెంబరు, డేటాఫ్ బర్త్ వివరాలు తప్పులు
                    లేకుండా ఎంటర్ చేయాలి
                  </li>
                </ol>
                <p>
                  Note:తప్పు వివరాలతో క్రియేట్ చేయబడిన అకౌంట్ 24 గంటలలోపు రిమూవ్
                  చేయబడుతుంది*
                </p>
              </div>
              <div className="packages-inner-container" style={{ padding: 20 }}>
                {!isLoading ? (
                  <>
                    <p className="main">
                      Please fill the following details carefully to activate
                      your account
                    </p>
                    <Divider color="white" style={{ marginBottom: 10 }} />
                    {err && (
                      <p
                        className="otp-mesage-text"
                        style={{ color: "red", textAlign: "center" }}
                      >
                        Error : {err}
                      </p>
                    )}

                    <form
                      className="form-register"
                      onSubmit={this.registerAccount}
                    >
                      <TextField
                        required
                        className="input-box register"
                        label="Name"
                        variant="filled"
                        value={name}
                        onChange={this.onChangeName}
                        focused
                      />
                      <TextField
                        required
                        className="input-box register"
                        label="Surname"
                        variant="filled"
                        value={surname}
                        onChange={this.onChangeSurName}
                        focused
                      />
                      <TextField
                        required
                        className="input-box register"
                        label="Email Address"
                        variant="filled"
                        value={email}
                        onChange={this.onChangeEmail}
                        focused
                      />

                      <Select
                        required
                        id="Please-Select-District"
                        className="input-box dis"
                        // label="Please Select District"
                        value={district}
                        onChange={this.onChangeDistrict}
                        focused
                        MenuProps={MenuProps}
                      >
                        <MenuItem
                          value={10}
                          disabled
                          className="attempt-option-select"
                        >
                          Please Select District
                        </MenuItem>
                        <MenuItem
                          value="Anantapur"
                          className="attempt-option-select"
                        >
                          Anantapur
                        </MenuItem>
                        <MenuItem
                          value="Chittoor"
                          className="attempt-option-select"
                        >
                          Chittoor
                        </MenuItem>
                        <MenuItem
                          value="East Godavari"
                          className="attempt-option-select"
                        >
                          East Godavari
                        </MenuItem>
                        <MenuItem
                          value="Guntur"
                          className="attempt-option-select"
                        >
                          Guntur
                        </MenuItem>
                        <MenuItem
                          value="Krishna"
                          className="attempt-option-select"
                        >
                          Krishna
                        </MenuItem>
                        <MenuItem
                          value="Kurnool"
                          className="attempt-option-select"
                        >
                          Kurnool
                        </MenuItem>
                        <MenuItem
                          value="Prakasam"
                          className="attempt-option-select"
                        >
                          Prakasam
                        </MenuItem>
                        <MenuItem
                          value="Srikakulam"
                          className="attempt-option-select"
                        >
                          Srikakulam
                        </MenuItem>
                        <MenuItem
                          value="Sri Potti Sriramulu Nellore"
                          className="attempt-option-select"
                        >
                          Sri Potti Sriramulu Nellore
                        </MenuItem>
                        <MenuItem
                          value="Visakhapatnam"
                          className="attempt-option-select"
                        >
                          Visakhapatnam
                        </MenuItem>
                        <MenuItem
                          value="Vizianagaram"
                          className="attempt-option-select"
                        >
                          Vizianagaram
                        </MenuItem>
                        <MenuItem
                          value="West Godavari"
                          className="attempt-option-select"
                        >
                          West Godavari
                        </MenuItem>
                        <MenuItem
                          value="Kadapa"
                          className="attempt-option-select"
                        >
                          Kadapa
                        </MenuItem>
                        <MenuItem
                          value="Adilabad"
                          className="attempt-option-select"
                        >
                          Adilabad
                        </MenuItem>
                        <MenuItem
                          value="Bhadradri Kothagudem"
                          className="attempt-option-select"
                        >
                          Bhadradri Kothagudem
                        </MenuItem>
                        <MenuItem
                          value="Hyderabad"
                          className="attempt-option-select"
                        >
                          Hyderabad
                        </MenuItem>
                        <MenuItem
                          value="Jagtial"
                          className="attempt-option-select"
                        >
                          Jagtial
                        </MenuItem>
                        <MenuItem
                          value="Jangaon"
                          className="attempt-option-select"
                        >
                          Jangaon
                        </MenuItem>
                        <MenuItem
                          value="Jayashankar Bhoopalpally"
                          className="attempt-option-select"
                        >
                          Jayashankar Bhoopalpally
                        </MenuItem>
                        <MenuItem
                          value="Jogulamba Gadwal"
                          className="attempt-option-select"
                        >
                          Jogulamba Gadwal
                        </MenuItem>
                        <MenuItem
                          value="Kamareddy"
                          className="attempt-option-select"
                        >
                          Kamareddy
                        </MenuItem>
                        <MenuItem
                          value="Karimnagar"
                          className="attempt-option-select"
                        >
                          Karimnagar
                        </MenuItem>
                        <MenuItem
                          value="Khammam"
                          className="attempt-option-select"
                        >
                          Khammam
                        </MenuItem>
                        <MenuItem
                          value="Komaram Bheem Asifabad"
                          className="attempt-option-select"
                        >
                          Komaram Bheem Asifabad
                        </MenuItem>
                        <MenuItem
                          value="Mahabubabad"
                          className="attempt-option-select"
                        >
                          Mahabubabad
                        </MenuItem>
                        <MenuItem
                          value="Mahabubnagar"
                          className="attempt-option-select"
                        >
                          Mahabubnagar
                        </MenuItem>
                        <MenuItem
                          value="Mancherial"
                          className="attempt-option-select"
                        >
                          Mancherial
                        </MenuItem>
                        <MenuItem
                          value="Medak"
                          className="attempt-option-select"
                        >
                          Medak
                        </MenuItem>
                        <MenuItem
                          value="Medchal"
                          className="attempt-option-select"
                        >
                          Medchal
                        </MenuItem>
                        <MenuItem
                          value="Nagarkurnool"
                          className="attempt-option-select"
                        >
                          Nagarkurnool
                        </MenuItem>
                        <MenuItem
                          value="Nalgonda"
                          className="attempt-option-select"
                        >
                          Nalgonda
                        </MenuItem>
                        <MenuItem
                          value="Nirmal"
                          className="attempt-option-select"
                        >
                          Nirmal
                        </MenuItem>
                        <MenuItem
                          value="Nizamabad"
                          className="attempt-option-select"
                        >
                          Nizamabad
                        </MenuItem>
                        <MenuItem
                          value="Peddapalli"
                          className="attempt-option-select"
                        >
                          Peddapalli
                        </MenuItem>
                        <MenuItem
                          value="Rajanna Sircilla"
                          className="attempt-option-select"
                        >
                          Rajanna Sircilla
                        </MenuItem>
                        <MenuItem
                          value="Rangareddy"
                          className="attempt-option-select"
                        >
                          Rangareddy
                        </MenuItem>
                        <MenuItem
                          value="Sangareddy"
                          className="attempt-option-select"
                        >
                          Sangareddy
                        </MenuItem>
                        <MenuItem
                          value="Siddipet"
                          className="attempt-option-select"
                        >
                          Siddipet
                        </MenuItem>
                        <MenuItem
                          value="Suryapet"
                          className="attempt-option-select"
                        >
                          Suryapet
                        </MenuItem>
                        <MenuItem
                          value="Vikarabad"
                          className="attempt-option-select"
                        >
                          Vikarabad
                        </MenuItem>
                        <MenuItem
                          value="Wanaparthy"
                          className="attempt-option-select"
                        >
                          Wanaparthy
                        </MenuItem>
                        <MenuItem
                          value="Warangal"
                          className="attempt-option-select"
                        >
                          Warangal
                        </MenuItem>
                        <MenuItem
                          value="Yadadri Bhuvanagiri"
                          className="attempt-option-select"
                        >
                          Yadadri Bhuvanagiri
                        </MenuItem>
                      </Select>

                      {/* <DatePicker
                        disableFuture
                        className="input-box register"
                        openTo="year"
                        format="dd/MMM/yyyy"
                        label="Date of birth"
                        views={["year", "month", "date"]}
                        value={dob}
                        inputVariant="filled"vnvb
                        onChange={this.onChangeDob}
                        focused
                      /> */}
                      {/* <TextField
                        required
                        variant="filled"
                        value={dob}
                        type={"text"}
                        name="dob"
                        id="#dateFormat"
                        // inputProps={{ maxLength: 10 }}
                        onChange={this.onChangeDob}
                        label={"Your Date of Birth (DD/MM/YYYY) "}
                        className="input-box register"
                      /> */}
                      <p style={{ color: "#FFF" }}>
                        Your Date of Birth (DD/MM/YYYY){" "}
                      </p>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          marginBottom: 20,
                        }}
                      >
                        <TextField
                          required
                          focused
                          variant="filled"
                          value={dob}
                          type={"text"}
                          name="dobdate"
                          id="#dateFormat"
                          inputProps={{ maxLength: 2 }}
                          onChange={this.onChangeDob}
                          label={"Date"}
                          className="input-box register"
                          style={{ width: 120 }}
                        />
                        <h1
                          style={{
                            color: "#FFF",
                            marginRight: 10,
                            marginLeft: 10,
                          }}
                        >
                          {" "}
                          /
                        </h1>
                        <TextField
                          required
                          focused
                          variant="filled"
                          value={dob}
                          type={"text"}
                          name="dobmonth"
                          id="#dateFormat"
                          inputProps={{ maxLength: 2 }}
                          onChange={this.onChangeDob}
                          label={"Month"}
                          className="input-box register"
                          style={{ width: 120 }}
                        />
                        <h1
                          style={{
                            color: "#FFF",
                            marginRight: 10,
                            marginLeft: 10,
                          }}
                        >
                          {" "}
                          /
                        </h1>
                        <TextField
                          required
                          focused
                          variant="filled"
                          value={dob}
                          type={"text"}
                          name="dobyear"
                          id="#dateFormat"
                          inputProps={{ maxLength: 4 }}
                          onChange={this.onChangeDob}
                          label={"Year"}
                          className="input-box register"
                        />
                      </div>
                      {/* <TextField
                        className="input-box register"
                        label="Referral Code"
                        variant="filled"
                        value={refer}
                        onChange={this.onChangeRefer}
                        focused
                      /> */}
                      <Button
                        type="submit"
                        variant="contained"
                        className="btn activateacoountbtn"
                      >
                        Submit {"&"} Activate my account
                      </Button>
                    </form>
                  </>
                ) : (
                  <div className="login-buttons-container">
                    <Loader />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default Register;
