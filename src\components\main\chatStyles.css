.chatmainDiv {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.chatbackground {
  background-image: url("../chat_back.jpg");
  background-size: cover;
  /* width: 100%; */
  /* height: fit-content; */
  /* height: 100%; */
  overflow-y: auto;
  height: 100vh;
  padding: 10px;
}
.chat {
  width: 60%;
  margin-top: 0px;
}
.msg {
  padding: 10px;
  border-radius: 10px !important;
}
.sentChhat {
  background-color: orange;
  color: black;
  border-top-left-radius: 0px !important;
  font-size: 14px;
}
.recieveChhat {
  background-color: white;
  font-size: 14px;
  border-top-right-radius: 0px !important;
}
.fromchat {
  font-size: 10px;
}
.sentchat {
  margin-left: auto;
}
.chatMsgDiv {
  height: 50px;
  background-image: url("../chat_back.jpg");
  background-size: cover;
  padding: 10px;
}
.chatmsginput {
  width: 80%;
  margin-right: 10px;
  height: 33px;
  padding: 2px;
  padding-left: 10px;
  border-radius: 15px;
  border-width: 0px;
}
.chatsendbtn {
  border-radius: 15px;
  background-color: green;
  color: white;
  height: 38px;
  /* padding: 4px; */
  width: 44px;
  border-width: 0px;
}
.nomsgs {
  height: 100vh;
}
