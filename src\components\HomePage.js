import react from "react";
import axios from "axios";
import Login from "./Login";
import Header from "./Header";
import Loader from "./Loader";
import Divider from "@mui/material/Divider";
import <PERSON>ie from "js-cookie";
import DesktopMenu from "./DesktopMenu";
import Packages from "./Packages";
import PurchasedPackages from "./PurchasedPackages";
import UserProfile from "./UserProfileBox";
import commonData from "../importanValue";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
// import { storeinfo } from '../redux/reducer'
import { connect } from "react-redux";

import OneSignal from "react-onesignal";

class HomePage extends react.Component {
  state = {
    isLoading: true,
    packages: [],
    userData: [],
    su: null,
    unpaidLoading: false,
  };

  componentDidMount() {
    const { location } = this.props;
    if (location.search.includes("?support=true")) {
      window.location.href = "/support";
      return;
    }
    this.getPackagesList();
  }

  getPackagesList = async () => {
    const { packageList, userInfo } = this.props;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    // if (packageList.length === 0) {
    const paidpackages = await axios.get(
      `${commonData["api"]}/get-user-payments-data/paidpackages`,
      {
        headers,
      }
    );

    // this.props.storeinfo({ packageList: userPaymentData.data })
    this.setState({
      paidpackages: paidpackages.data[0],
    });

    const uid = localStorage.getItem("num");
    if (token !== undefined) {
      try {
        // if (userInfo.length === 0) {
        this.setState({ isLoading: true });
        const userDetails = await axios.post(
          `${commonData["api"]}/get-user-details/${uid}`
        );
        // this.props.storeinfo({ userInfo: userDetails.data.result[0] })
        this.setState({
          userData: userDetails.data.result[0],
          isLoading: false,
        });
        localStorage.setItem("user", userDetails.data.result[0].su);
        // }
        // else {
        //   this.setState({
        //     userData: userInfo
        //   });
        //   localStorage.setItem("user", userInfo.su);
        // }

        if (token !== undefined) {
          OneSignal.sendTag("number", String(uid));
        }
      } catch (err) {
        Cookie.remove("jwt_token");
      }

      // console.log(userDetails.data.result);
    }

    this.setState(
      {
        // packages: packageList,
        isLoading: false,
        unpaidLoading: true,
      },
      () => {
        const { location } = this.props;
        const { search } = location;
        if (
          search.split("?msg=")[1] ===
          "Your%20Account%20Activated%20Succesfully"
        ) {
          NotificationManager.success(`Your Account Activated Succesfully...`);
        } else if (search.split("?msg=")[1] === "welcome") {
          NotificationManager.info(
            `Hi ${this.state.userData.first_name}...
          Welcome back to our  Online Exams portal`
          );
        }
      }
    );
    this.getUnpad();
  };

  getUnpad = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const response = await axios.get(`${commonData["api"]}/get-packages-list`);

    const unpaidpackages = await axios.get(
      `${commonData["api"]}/get-user-payments-data/unpaidpackages`,
      {
        headers,
      }
    );
    // this.props.storeinfo({ packageList: userPaymentData.data })
    this.setState({
      unpaidpackages: unpaidpackages.data[0],
      packages: response.data,
      unpaidLoading: false,
    });
  };
  getPackageList = () => {
    const { packages, userData } = this.state;
    const listUGid = String(userData.gid)
      .split(",")
      .filter((item, i, ar) => ar.indexOf(item) === i);
    // .sort()
    // .reverse();
    const filteredPacks = [];
    for (let gid of listUGid) {
      const eachData = packages.filter((each) => each.gid === parseInt(gid))[0];
      if (eachData !== undefined) filteredPacks.push(eachData);
    }
    // this.props.storeinfo({ activatedPackages: filteredPacks })
    return filteredPacks;
  };

  getAvailablePackages = () => {
    const { packages, userData } = this.state;
    const finalList = {};
    const listUGid = String(userData.gid)
      .split(",")
      .filter((item, i, ar) => ar.indexOf(item) === i);
    // .reverse();
    for (let each of listUGid) {
      finalList[each] = each;
      // console.log(finalList);
    }
    // console.log(String(userData.gid).split(",").reverse());
    const allPacksgid = packages.map((each) => String(each.gid));
    for (let each of allPacksgid) {
      finalList[each] = !finalList[each] ? each : null;
    }
    const filteredPacks = [];
    for (let gid of Object.values(finalList)) {
      if (gid !== null) {
        const eachData = packages.filter((each) => String(each.gid) === gid)[0];
        // console.log(eachData);
        if (eachData !== undefined) filteredPacks.push(eachData);
      }
    }
    // console.log(filteredPacks);

    return filteredPacks;
  };

  getPacks = () => {
    const { packages } = this.state;
    let text = "";
    let x = 1;
    for (let each of packages) {
      text = text + x + " )" + each.group_name + " " + "%0a";
      x = x + 1;
    }
    // console.log(text);
    return text;
  };

  render() {
    const { isLoading, userData, paidpackages, unpaidpackages, unpaidLoading } =
      this.state;
    const token = Cookie.get("jwt_token");
    // console.log(token);
    // let packageData = token !== undefined ? this.getPackageList() : null;
    // let packages = token !== undefined ? this.getAvailablePackages() : null;
    // console.log(packageData);
    return (
      <>
        {!isLoading && (
          <>
            <div className="desktopsidebar">
              <div className="homedesktopsidebarmenuexamdetails">
                <DesktopMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="homedesktop">
                {token === undefined ? (
                  <Login />
                ) : (
                  <UserProfile data={userData} />
                )}
                <Divider color="white" className="dividers" />

                {token !== undefined && paidpackages.length > 0 ? (
                  <>
                    <p className="homepage-package-title">
                      {" "}
                      Purchased Packages
                    </p>
                    <div className="homepacksdesk">
                      {paidpackages?.map((each, i) => (
                        <PurchasedPackages
                          percent={20}
                          linkText={`whatsapp://send?text=Hai,%0aThis is *${
                            userData.first_name + " " + userData.last_name
                          }*%0aనేను రీసెంట్ గా నవచైతన్య కాంపిటీషన్స్ నుంచి *${
                            paidpackages && paidpackages[0].group_name
                          }*   తీసుకున్నాను. పరీక్షలను రాస్తూ ఉన్నాను. ఈ వెబ్ సైట్/యాప్ లో క్వాలిటీ ప్రశ్నలతో ఆన్ లైన్ పరీక్షలను అందిస్తున్నారు. 
                    బిట్స్ ఎక్కడా కాపీ చేసినవి కాకుండా, లైన్ టూ లైన్ టెక్స్ట్ బుక్ చదివితే తప్ప, సమాధానం గుర్తించలేమనే విధంగా ఉన్న ఈ పరీక్షలను మీరూ రాయాలనుకుంటే వెంటనే క్రింది లింక్ పై క్లిక్ చేసి, నచ్చిన ప్యాకేజి డిటెయిల్స్ తెలుసుకుని పేమెంట్ చేయండి - పరీక్షలు రాయండి..%0a%0aJoin Through This Link >> %20%20https%3A%2F%2Fexams.navachaitanya.net%2F?refer=${
                      userData.refercode
                    }%0a%0a*Available Packages*%0a${this.getPacks()}`}
                          data={each}
                          key={"puchasedpackagename" + i}
                        />
                      ))}
                    </div>
                  </>
                ) : (
                  <>
                    {token !== undefined && (
                      <>
                        <p className="homepage-package-title">
                          Purchased Packages
                        </p>
                        <p className="homepage-package-packages">
                          No Packages are Available
                        </p>
                      </>
                    )}
                  </>
                )}
                <Divider color="white" className="dividers" />
                <p className="homepage-package-title">Available Packages</p>
                <div className="homepacksdesk">
                  {unpaidLoading && (
                    <div>
                      <Loader />
                    </div>
                  )}{" "}
                  {/* {console.log(packageData, "lates")} */}
                  {!unpaidLoading && unpaidpackages?.length > 0 ? (
                    unpaidpackages.map((each, i) => (
                      <Packages
                        percent={20}
                        data={each}
                        key={"packagename" + i}
                        linkText={`whatsapp://send?text=Hai,%0aThis is *${
                          userData.first_name + " " + userData.last_name
                        }*%0aనేను రీసెంట్ గా నవచైతన్య కాంపిటీషన్స్ నుంచి *${
                          paidpackages && paidpackages[0].group_name
                        }*   తీసుకున్నాను. పరీక్షలను రాస్తూ ఉన్నాను. ఈ వెబ్ సైట్/యాప్ లో క్వాలిటీ ప్రశ్నలతో ఆన్ లైన్ పరీక్షలను అందిస్తున్నారు. 
                    బిట్స్ ఎక్కడా కాపీ చేసినవి కాకుండా, లైన్ టూ లైన్ టెక్స్ట్ బుక్ చదివితే తప్ప, సమాధానం గుర్తించలేమనే విధంగా ఉన్న ఈ పరీక్షలను మీరూ రాయాలనుకుంటే వెంటనే క్రింది లింక్ పై క్లిక్ చేసి, నచ్చిన ప్యాకేజి డిటెయిల్స్ తెలుసుకుని పేమెంట్ చేయండి - పరీక్షలు రాయండి..%0a%0aJoin Through This Link >> %20%20https%3A%2F%2Fexams.navachaitanya.net%2F?refer=${
                      userData.refercode
                    }%0a%0a*Available Packages*%0a${this.getPacks()}`}
                      />
                    ))
                  ) : (
                    <p className="homepage-package-packages">
                      {unpaidLoading
                        ? "Loading..."
                        : " No Packages are Available"}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}
const mapStateToProps = (state) => {
  // const { packageList, userInfo } = state.common
  return {};
};

const mapDispatchToProps = {};
export default connect(mapStateToProps, mapDispatchToProps)(HomePage);
