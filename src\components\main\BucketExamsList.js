import react from "react";
import Header from "../Header";
import DesktopMenu from "../DesktopMenu";
import Loader from "../Loader";
import sendpush from "../admin/sendNotification";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import PurchasedPackages from "../PurchasedPackages";

// import topImg from "../../pdf_top.jpeg";
import "./styles.css";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
class BucketExamsList extends react.Component {
  state = {
    isLoading: true,
    data: [],
    groupName: "",
    expiryDate: "",
    userDetails: {},
    scheduleLink: "",
  };

  componentDidMount() {
    this.getData();
    const { location } = this.props;
    const { search } = location;
    if (search !== "") {
      NotificationManager.success("Your Package Activated Successfully..");
    }
  }

  getData = async () => {
    const { match } = this.props;
    const { params } = match;
    const { guid, bucketId } = params;
    console.log(window.atob(bucketId));
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      const uid = localStorage.getItem("num");
      const userDetails = await axios.post(
        `${commonData["api"]}/get-user-details/${uid}`,
        {
          headers,
        }
      );
      const quizzesList = await axios.post(
        `${commonData["api"]}/get-group-quizzes/${guid}`,
        {},
        {
          headers,
        }
      );
      const { location } = this.props;
      const { search } = location;
      if (search !== "") {
        await sendpush({
          title: `Hai ${
            userDetails.data.result[0].first_name.slice(0, 1) +
            userDetails.data.result[0].first_name.slice(1).toLowerCase()
          }.....`,
          message: `${quizzesList.data[0][0].group_name} Package Activated Successfully..`,
          filters: [
            { field: "tag", key: "number", relation: "=", value: String(uid) },
          ],
          url: `https://exams.navachaitanya.net/exams-list/${guid}`,
          web_buttons: [
            {
              id: "like-button",
              text: " ➤ Go to Exams List",
              url: `https://exams.navachaitanya.net/exams-list/${guid}`,
            },
          ],
        });
      }

      // console.log(quizzesList.data);
      this.setState({
        groupName: quizzesList.data[0][0].group_name,
        scheduleLink: quizzesList.data[0][0].link,
        isLoading: false,
        userDetails: userDetails.data.result[0],
        // data: quizzesList.data.slice(2)[0],
        expiryDate: quizzesList.data.slice(1)[0][0],
        packageData: quizzesList.data.slice(2)[0],
      });
    } catch (err) {
      Cookie.remove("jwt_token");
    }
  };

  showBucketInfo = () => {
    const { match } = this.props;
    const { params } = match;
    const { guid, bucketId } = params;
    console.log(window.atob(bucketId));
    const { packageData, userData } = this.state;
    return (
      <>
        <p className="homepage-package-title"> Purchased Packages</p>
        <div className="homepacksdesk">
          {packageData.map((each, i) => (
            <PurchasedPackages
              data={each}
              key={"puchasedpackagename" + i}
              bucketId={guid}
            />
          ))}
        </div>
      </>
    );
  };

  render() {
    const { isLoading, data, groupName, expiryDate, scheduleLink } = this.state;
    const { match } = this.props;
    const { params } = match;
    const { guid, bucketId } = params;
    // console.log(data);
    // console.log(userDetails, expiryDate);
    return (
      <>
        {!isLoading && (
          <div className="desktopsidebar">
            <div className="desktopsidebarmenu">
              <DesktopMenu />
            </div>
            <Header />

            {/* <Divider color="white" /> */}
            <div className="desktopcontnet">
              <div className="title-quiz" id="print">
                <p className="homepage-package-title examlist-ttle">
                  {groupName}
                </p>
              </div>
              <div className="main-table-container">
                <table className="table">
                  <tbody>
                    <tr>
                      <th
                        className="tabledata "
                        style={{
                          width: "100%",
                          fontSize: 15,
                          textAlign: "center",
                        }}
                        colSpan={2}
                      >
                        Package Expiry Date :{" "}
                        {new Date(expiryDate.expiry_date).toDateString()}
                      </th>
                    </tr>

                    <tr>
                      <th className="tabledata">
                        <Button
                          variant="contained"
                          className="btn header-btns attemptbtn examschedulelink2"
                        >
                          <a
                            href={scheduleLink}
                            target="_blank"
                            className="linkto linksche2"
                            style={{ color: "#fff" }}
                          >
                            Download Exam Schedule {"&"} Syllabus
                          </a>
                        </Button>
                      </th>
                    </tr>
                  </tbody>
                </table>
              </div>
              {/* <Divider color="white" /> */}
              <div>
                {expiryDate.expired === 0 ? (
                  <>{this.showBucketInfo()}</>
                ) : (
                  <table className="table">
                    <tbody>
                      <tr>
                        <th
                          className="tabledata "
                          style={{
                            width: "100%",
                            fontSize: 18,
                            textAlign: "center",
                          }}
                          colSpan={2}
                        >
                          Your Package has been expired ... <br />
                          Please Pay the Fee to extend your Validity
                          <br />
                          <Link
                            to={`/payumoney/payment/checkout/${guid}?expired=1`}
                            className="linkto"
                          >
                            <Button
                              variant="contained"
                              className="btn header-btns packagebtn buy buypack"
                            >
                              BUY NOW
                            </Button>
                          </Link>
                        </th>
                      </tr>
                    </tbody>
                  </table>
                )}
              </div>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default BucketExamsList;
