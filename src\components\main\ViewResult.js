import React, { Component } from "react";
// import { connect } from "react-redux";
import "./styles.css";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import Header from "../Header";
import Loader from "../Loader";
import DesktopMenu from "../DesktopMenu";
import Divider from "@mui/material/Divider";
import CheckIcon from "@mui/icons-material/Check";
import commonData from "../../importanValue";
import pdfMake from "pdfmake/build/pdfmake";
import pdfFonts from "pdfmake/build/vfs_fonts";
import { jsPDF } from "jspdf";
import ClearIcon from "@mui/icons-material/Clear";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import topImg from "../../pdftop.jpeg";
// import { saveQuizDetails, saveAnswers } from "../redux/reducer";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import Button from "@mui/material/Button";
import Accordion from "@mui/material/Accordion";
import AccordionSummary from "@mui/material/AccordionSummary";
import AccordionDetails from "@mui/material/AccordionDetails";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import DialogActions from "@mui/material/DialogActions";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import sendpush from "../admin/sendNotification";
import FilesUploadComponent from "../admin/FileUpload";
import EditQuestion from "../common/EditQuestion";

// pdfMake.vfs = pdfFonts.pdfMake.vfs;
class ViewResult extends Component {
  state = {
    qNum: 0,
    isLoading: true,
    quizDetails: [],
    result: [],
    // expanded: "",
    disableback: false,
    disableNext: false,
    buttonReport: "Report as Wrong Answer",
    popUpOpen: false,
    msg: "",
    question: "",
    op1: "",
    op2: "",
    op3: "",
    op4: "",
    op5: "",
    attempt: null,
    wordfile: "",
    data: "",
    englishMedium: false,
    showEditDialog: false,
    editQuestionId: null,
    isQuestionUpdating: false,
    isEnglishMedium: false,
  };
  onFileChange = (e) => {
    this.setState({ wordfile: e.target.files[0] });
  };
  onSubmit = async (qid, link) => {
    const formData = new FormData();
    formData.append("file", this.state.wordfile);

    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
      linkfile: link,
      question: qid,
    };
    const data = await axios.post(
      `${commonData["api"]}/report-upload-image`,
      formData,
      {
        headers,
      }
    );

    this.setState({ data: data.data });
  };

  componentDidMount() {
    this.getData();

    document.addEventListener("contextmenu", (event) => {
      event.preventDefault();
    });
  }
  fetchExamDetails = async () => {
    try {
      this.setState({ isQuestionUpdating: true });
      const token = Cookie.get("jwt_token");
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
      };

      // Fetch single question instead of entire quiz
      const response = await axios.post(
        `${commonData["api"]}/support`,
        {
          type: "getQuestionDetails",
          qid: this.state.editQuestionId,
          search: "",
        },
        { headers }
      );

      // Parse question data and update only that question in state
      const questionData = JSON.parse(response.data[0][0].questionDetails);

      // Update the specific question in quizDetails array
      const newQuizDetails = [...this.state.quizDetails];
      const questionIndex = newQuizDetails[0][1].findIndex(
        (q) => q.qid === this.state.editQuestionId
      );

      if (questionIndex !== -1) {
        console.log(
          "🚀 ~ ViewResult ~ fetchExamDetails= ~   newQuizDetails[0][1][questionIndex]:",
          newQuizDetails[0][1][questionIndex]
        );
        newQuizDetails[0][1][questionIndex] = {
          ...newQuizDetails[0][1][questionIndex],
          question: `<p>${questionData.questionText || ""}</p>`,
          question_in_english: questionData.questionEngText || null,
          options: questionData.options
            .map(
              (opt) =>
                `<p>${opt.optionText}</p> score ${opt.isCorrect ? 1 : 0}]`
            )
            .join(","),
          options_english: questionData.engoptions
            ? questionData.engoptions
                .map(
                  (opt) =>
                    `<p>${opt.optionText}</p> score ${opt.isCorrect ? 1 : 0}]`
                )
                .join(",")
            : null,
          score: questionData.options
            .map((opt) => (opt.isCorrect ? 1 : 0))
            .join(","),
        };
        console.log(
          "🚀 ~ ViewResult ~ fetchExamDetails= ~   newQuizDetails[0][1][questionIndex]:",
          newQuizDetails[0][1][questionIndex]
        );

        this.setState({
          quizDetails: newQuizDetails,
          isQuestionUpdating: false,
          editQuestionId: null,
        });
      }
    } catch (error) {
      console.error("Error fetching question details:", error);
      this.setState({ isQuestionUpdating: false });
    }
  };

  getData = async () => {
    const { match } = this.props;
    const { params } = match;
    const { uid, quizid, resultid } = params;
    // const { saveQuizDetail, saveAnswers } = this.props;
    // console.log(saveQuizDetail);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const userResult = await axios.get(
      `${commonData["api"]}/view-result/` + uid + "/" + quizid + "/" + resultid,
      { headers }
    );
    const quizData = await axios.get(
      `${commonData["api"]}/quiz_details/` + quizid,
      { headers }
    );

    this.setState((old) => ({
      quizDetails: [...old.quizDetails, quizData.data],
      result: [...old.result, userResult.data],
      isLoading: false,
      attempt: quizData.data[0][0].attempt,
    }));
    // console.log(userResult);
    const { location } = this.props;
    const { search } = location;
    // console.log(search);
    if (search !== "") {
      NotificationManager.success(`Your Exam Submitted Succesfully...`);
    }
    // saveQuizDetail(quizData.data);
    // saveAnswers(userResult.data);
  };

  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      msg: "",
      question: "",
      op1: "",
      op2: "",
      op3: "",
      op4: "",
      op5: "",
    }));
  };

  handleEditClick = (questionId, isEnglishMedium) => {
    this.setState({
      editQuestionId: questionId,
      showEditDialog: true,
      isEnglishMedium: isEnglishMedium,
    });
  };

  handleEditClose = async (wasUpdated) => {
    this.setState({
      showEditDialog: false,
    });

    if (wasUpdated) {
      await this.fetchExamDetails(); // Wait for fetch to complete
    }
  };

  renderPopUp = () => {
    const { popUpOpen, msg, question, op1, op2, op3, op4, op5 } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{"Report Question"}</p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent
          className="dailogContent"
          style={{ display: "flex", flexDirection: "column" }}
        >
          <p className="questionfont" style={{ display: "flex" }}>
            Q{") "}
            <span
              style={{ marginLeft: 5 }}
              dangerouslySetInnerHTML={{ __html: question }}
            ></span>
          </p>
          {op1 !== "" && (
            <div>
              <div style={{ display: "flex" }}>
                <div
                  style={{
                    marginTop: 10,
                    marginRight: 5,
                  }}
                >
                  <input
                    type={"checkbox"}
                    value={"A"}
                    id="A"
                    onChange={(e) => this.setState({ msg: e.target.value })}
                    checked={msg === "A"}
                    style={{ display: "flex" }}
                  />{" "}
                </div>
                <div style={{ display: "flex" }}>
                  <p>{"A"}</p>
                  <p>{" ) "}</p>
                </div>
                <span
                  style={{ marginLeft: 5 }}
                  dangerouslySetInnerHTML={{
                    __html: op1,
                  }}
                ></span>
              </div>{" "}
            </div>
          )}
          {op2 !== "" && (
            <div>
              <div style={{ display: "flex" }}>
                <div
                  style={{
                    marginTop: 10,
                    marginRight: 5,
                  }}
                >
                  <input
                    type={"checkbox"}
                    value={"B"}
                    id="B"
                    onChange={(e) => this.setState({ msg: e.target.value })}
                    checked={msg === "B"}
                    style={{ display: "flex" }}
                  />{" "}
                </div>
                <div style={{ display: "flex" }}>
                  <p>{"B"}</p>
                  <p>{" ) "}</p>
                </div>
                <span
                  style={{ marginLeft: 5 }}
                  dangerouslySetInnerHTML={{
                    __html: op2,
                  }}
                ></span>
              </div>{" "}
            </div>
          )}

          {op3 !== "" && (
            <div>
              <div style={{ display: "flex" }}>
                <div
                  style={{
                    marginTop: 10,
                    marginRight: 5,
                  }}
                >
                  <input
                    type={"checkbox"}
                    value={"C"}
                    id="C"
                    onChange={(e) => this.setState({ msg: e.target.value })}
                    checked={msg === "C"}
                    style={{ display: "flex" }}
                  />{" "}
                </div>
                <div style={{ display: "flex" }}>
                  <p>{"C"}</p>
                  <p>{" ) "}</p>
                </div>
                <span
                  style={{ marginLeft: 5 }}
                  dangerouslySetInnerHTML={{
                    __html: op3,
                  }}
                ></span>
              </div>{" "}
            </div>
          )}

          {op4 !== "" && (
            <div>
              <div style={{ display: "flex" }}>
                <div
                  style={{
                    marginTop: 10,
                    marginRight: 5,
                  }}
                >
                  <input
                    type={"checkbox"}
                    value={"D"}
                    id="D"
                    onChange={(e) => this.setState({ msg: e.target.value })}
                    checked={msg === "D"}
                    style={{ display: "flex" }}
                  />{" "}
                </div>
                <div style={{ display: "flex" }}>
                  <p>{"D"}</p>
                  <p>{" ) "}</p>
                </div>
                <span
                  style={{ marginLeft: 5 }}
                  dangerouslySetInnerHTML={{
                    __html: op4,
                  }}
                ></span>
              </div>{" "}
            </div>
          )}
          {op5 !== "" && (
            <div>
              <div style={{ display: "flex" }}>
                <div
                  style={{
                    marginTop: 10,
                    marginRight: 5,
                  }}
                >
                  <input
                    type={"checkbox"}
                    value={"ALL"}
                    id="ALL"
                    onChange={(e) => this.setState({ msg: e.target.value })}
                    checked={msg === "ALL"}
                    style={{ display: "flex" }}
                  />{" "}
                </div>
                <div style={{ display: "flex" }}>
                  <p>{"ALL"}</p>
                  <p>{" ) "}</p>
                </div>
                <span
                  style={{ marginLeft: 5 }}
                  dangerouslySetInnerHTML={{
                    __html: op5,
                  }}
                ></span>
              </div>{" "}
            </div>
          )}
          <div>
            <input
              type={"checkbox"}
              value={"ALL"}
              id="ALL"
              onChange={(e) => this.setState({ msg: e.target.value })}
              checked={msg === "ALL"}
            />
            <label htmlFor="ALL" style={{ fontSize: 15 }}>
              {" "}
              ALL) All options are Wrong
            </label>

            <div className="form-group">
              <br />
              Upload Answer Proof Image
              <br />
              <input
                type="file"
                onChange={this.onFileChange}
                name="file"
                accept="image/*"
              />
            </div>
          </div>
        </DialogContent>
        <DialogActions
          style={{
            display: "flex",
            flexDirection: "row",
            justifyContent: "center",
          }}
        >
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={this.reportQuestion}
          >
            Report
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  reportQuestion = async (e) => {
    const { qNum, quizDetails, msg, wordfile, result } = this.state;
    const { match } = this.props;
    const { params } = match;
    const { quizid } = params;
    const uid = localStorage.getItem("num");
    const each = quizDetails[0][1][qNum];

    const myAns = result[0][0][0];

    if (msg !== "" && wordfile !== "") {
      const token = Cookie.get("jwt_token");
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
        "Access-Control-Allow-Origin": "*",
        fileurl: `${commonData["api"]}/reported/${each.qid}/${uid}.${
          String(wordfile.type).split("/")[1]
        }`,
      };
      // console.log(
      //   `${commonData["api"]}/reported/${each.qid}/${uid}.${
      //     String(wordfile.type).split("/")[1]
      //   }`
      // );

      this.onSubmit(each.qid, ` ${uid}.${String(wordfile.type).split("/")[1]}`);
      this.setState({ buttonReport: "Reporting ...." });
      const report = await axios.post(
        `${commonData["api"]}/report-question/${quizid}/${each.qid}`,
        { msg },
        { headers }
      );
      this.setState({
        buttonReport: "Report as Wrong Answer",
        popUpOpen: false,
        msg: "",
        question: "",
        op1: "",
        op2: "",
        op3: "",
        op4: "",
        wordfile: "",
      });
      const reportMsg = report.data[0][0].result;
      const id = report.data[0][0].id;
      if (reportMsg === "error") {
        this.setState({ wordfile: "" });
        NotificationManager.error("Already Reported..");
      } else if (reportMsg === "success") {
        // await sendpush({
        //   title: `New Question Reported By Student...`,
        //   message: `Exam Name : ${quizDetails[0][0][0].quiz_name}\nQuestion : ${question}\nReported by : ${uid}`,
        //   filters: [
        //     {
        //       field: "tag",
        //       key: "user",
        //       relation: "=",
        //       value: "admin",
        //     },
        //   ],
        //   url: `/admin/qbank/reported-questions/${quizid}/${id}`,
        //   web_buttons: [
        //     {
        //       id: "like-button",
        //       text: "➤ Edit Question",
        //       url: `/admin/qbank/reported-questions/${quizid}/${id}`,
        //     },
        //   ],
        // });
        const sendMsg = (phone_new) => {
          axios.post(
            `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
            {
              businessId: 1,
              verifyToken: Math.random() * 15000,
              phoneNumber: phone_new,
              message: [
                id,
                uid,
                myAns.name,
                commonData["app"],
                `${quizid}/${id}`,
              ],
              messageType: "promotion",
              templateLang: "en",
              templateName: "user_reported_status",
            }
          );
        };
        sendMsg(**********);
        sendMsg(**********);
        sendMsg(**********);
        NotificationManager.success("Reported Successfully..");
      }
    } else if (msg == "") {
      NotificationManager.error("Please Select Correct Answer");
    } else if (wordfile == "") {
      NotificationManager.error("Please Upload Answer Proof Image");
    }
  };

  createData = (value, name) => {
    return { value, name };
  };
  timeConvert = (n) => {
    let hours = n / 3600;
    let h = Math.floor(hours);
    h = h < 10 ? "0" + h : h;
    let rem = n % 3600;
    let minutes = rem / 60;
    let m = Math.floor(minutes);
    m = m < 10 ? "0" + m : m;
    rem = rem % 60;
    rem = rem < 10 ? "0" + rem + "s" : rem + "s";
    h = h > 0 ? h + "h : " : "";
    m = m > 0 ? m + "m : " : "";
    // rem = rem < 60 ? rem + " Seconds" : rem;
    return h + m + rem;
  };

  Table = () => {
    const { quizDetails, result, attempt } = this.state;
    // console.log(quizDetails, result);
    const quizInfo = quizDetails[0][0][0];
    const myAns = result[0][0][0];

    let rank = 0;
    try {
      rank = result[0][1].filter((each) => each.contact === myAns.whatsapp)[0]
        .rank;
    } catch (er) {
      rank = 0;
    }
    const rows = [
      this.createData(myAns.name, "Student Name"),
      this.createData(quizInfo.quiz_name, "Exam Name"),
      // this.createData(attemptNo, "Attempt No"),
      this.createData(myAns.score_obtained, "Marks Secured"),
      this.createData(
        (
          (myAns.score_obtained / quizInfo["@q"].split(",").length) *
          100
        ).toFixed(1) + " %",
        "Your Percentage"
      ),
      this.createData(
        quizInfo.rankView ? rank : "Will be Updated after 9 PM today",
        "Rank in 1st Attempt"
      ),
      this.createData(
        new Date(myAns.start_time).toDateString() +
          " " +
          new Date(myAns.start_time).toLocaleTimeString(),
        "Attempt Time"
      ),
      this.createData(this.timeConvert(myAns.timeSpent), "Time Spent"),
      // this.createData("", "ALL THE BEST !!"),
    ];

    return (
      <>
        <TableContainer
          component={Paper}
          className="tablecontainer-container"
          sx={{
            // width: "90vw",
            height: "auto",
            paddingLeft: "20px",
            paddingRight: "20px",
            paddingBottom: 2,
            paddingTop: 2,
          }}
        >
          <Accordion
            TransitionProps={{ unmountOnExit: true }}
            className="accroding-style"
            // expanded={expanded === "scoreandrank"}
            // onChange={(event, isExpanded) => {
            //   const acc = isExpanded ? "scoreandrank" : false;
            //   this.setState({ expanded: acc });
            // }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon className="expandIcon" />}
              aria-controls="panel1a-content"
              id="panel1a-header"
            >
              <p>Your Score{" & "} Rank</p>
            </AccordionSummary>
            <AccordionDetails>
              <Table
                sx={{
                  // width: "100vw",
                  height: "auto",
                  // padding: "20px",
                  border: "2px solid white",
                }}
                // lg={{ width: "95vw", padding: "20px", margin: "30px" }}
                aria-label="simple table"
              >
                <TableBody>
                  {rows.map((row, i) => (
                    <TableRow
                      key={row.name + "result98" + i}
                      sx={{ border: "2px solid white" }}
                    >
                      <TableCell
                        component="th"
                        scope="row"
                        className="tablecell"
                      >
                        {row.name}
                      </TableCell>
                      <TableCell align="center" className="tablecell">
                        {row.value}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </AccordionDetails>
          </Accordion>
        </TableContainer>
      </>
    );
  };

  renderQA = () => {
    if (this.state.isQuestionUpdating) {
      return <Loader />;
    }

    const { qNum, quizDetails, result, buttonReport, englishMedium } =
      this.state;
    const enable_english_medium = localStorage.getItem("enable_english_medium");

    const each = quizDetails[0][1][qNum];

    const quizInfo = result[0][0][0];
    const optionsAll =
      englishMedium && enable_english_medium === "1"
        ? each.options_english
        : each.options;
    let correctAns = optionsAll
      .split("],")
      .filter((op) => op.split("score")[1].includes("1"));

    correctAns =
      correctAns.length === 0
        ? optionsAll
            .split("],")
            .filter((op) => op.split("score")[1].includes("0"))[0]
            .split("score")[0]
        : correctAns[0].split("score")[0];

    const options = ["A", "B", "C", "D", "E"];
    const scoreEach = {};
    JSON.parse(result[0][0][0].score_individual).forEach(
      (each) => (scoreEach[Object.keys(each)] = Object.values(each)[0])
    );
    // console.log(quizInfo);
    const yourAnswer = scoreEach[qNum]
      ? optionsAll
          .split("],")
          [scoreEach[qNum].split("score")[0]].split("score")[0]
      : null;
    // console.log(scoreEach);
    const back = `.qoptions-result{
      background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1'  height='70px' width='150px' transform='rotate(-15)'><text x='0' y='25' fill='white' opacity='0.1' font-size='23'>${quizInfo.whatsapp}</text></svg>") !important;}`;

    return (
      <>
        <style>{back}</style>
        {enable_english_medium === "1" &&
          each?.question_in_english &&
          each?.options_english && (
            <label style={{ color: "#FFF" }}>
              English Medium
              <input
                type="checkbox"
                checked={englishMedium}
                onChange={() =>
                  this.setState((p) => ({ englishMedium: !p.englishMedium }))
                }
                style={{ marginLeft: 10 }}
              />
            </label>
          )}
        <div
          className="qoptions-result paddin"
          onCopy={(e) => {
            e.preventDefault();
            return false;
          }}
        >
          {
            <div key={qNum * 99}>
              <p className="questionfont" style={{ display: "flex" }}>
                Q:{qNum + 1}
                {") "}
                <span
                  style={{ marginLeft: 5 }}
                  dangerouslySetInnerHTML={{
                    __html:
                      englishMedium && enable_english_medium === "1"
                        ? each.question_in_english
                        : each.question,
                  }}
                ></span>
              </p>

              {optionsAll
                .split("],")
                .slice(0, 5)
                .map((op, index) => (
                  <div key={index * 7 + "op"} style={{ display: "flex" }}>
                    {options[index] + " ) "}
                    <label
                      htmlFor={"op" + index + qNum}
                      className="questionfont"
                      style={{ marginLeft: 5 }}
                    >
                      <span
                        dangerouslySetInnerHTML={{
                          __html: op.split("score")[0],
                        }}
                      ></span>
                    </label>
                    <br />
                  </div>
                ))}
              <div style={{ display: "flex" }}>
                <p style={{ marginRight: 5 }}>Correct Answer : {"  "}</p>
                <span dangerouslySetInnerHTML={{ __html: correctAns }}></span>
              </div>
              {each.description && (
                <div style={{ display: "flex" }}>
                  <p style={{ marginRight: 5 }}>Explanation : {"  "}</p>
                  <span
                    dangerouslySetInnerHTML={{
                      __html:
                        englishMedium && enable_english_medium === "1"
                          ? each.description_in_english
                          : each.description,
                    }}
                  ></span>
                </div>
              )}
              {scoreEach[qNum] && (
                <div style={{ display: "flex" }}>
                  <div style={{ display: "flex" }}>
                    <p style={{ marginRight: 5 }}>Your Answer : {"  "}</p>
                    <span
                      dangerouslySetInnerHTML={{ __html: yourAnswer }}
                    ></span>
                  </div>

                  <div className="correctWrongicon">
                    {yourAnswer === correctAns ? (
                      <CheckIcon className="save" />
                    ) : (
                      <ClearIcon className="back clearIconcss" />
                    )}
                  </div>
                </div>
              )}
              {each.verified === 0 ? (
                <div className="report">
                  <Button
                    variant="contained"
                    className="btn reportbtn"
                    onClick={() =>
                      this.setState({
                        popUpOpen: true,
                        question:
                          englishMedium && enable_english_medium === "1"
                            ? each.question_in_english
                            : each.question,
                        op1: optionsAll.split("],")[0]
                          ? optionsAll.split("],")[0].split("score")[0]
                          : "",
                        op2: optionsAll.split("],")[1]
                          ? optionsAll.split("],")[1].split("score")[0]
                          : "",
                        op3: optionsAll.split("],")[2]
                          ? optionsAll.split("],")[2].split("score")[0]
                          : "",
                        op4: optionsAll.split("],")[3]
                          ? optionsAll.split("],")[3].split("score")[0]
                          : "",
                        op5: optionsAll.split("],")[4]
                          ? optionsAll.split("],")[4].split("score")[0]
                          : "",
                      })
                    }
                  >
                    {buttonReport}
                  </Button>
                </div>
              ) : (
                <div className="report">
                  <Button
                    variant="contained"
                    className="btn reportbtn verifiedq"
                  >
                    Question Verified ✔
                  </Button>
                </div>
              )}
              {localStorage.getItem("user") === "2" && (
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => this.handleEditClick(each.qid, false)}
                  style={{ marginLeft: 10, marginTop: 20 }}
                >
                  (TM) Edit Question
                </Button>
              )}
              {localStorage.getItem("user") === "2" && (
                <Button
                  variant="contained"
                  size="small"
                  onClick={() => this.handleEditClick(each.qid, true)}
                  style={{ marginLeft: 10, marginTop: 20 }}
                >
                  (EM) Edit Question
                </Button>
              )}
            </div>
          }
        </div>
      </>
    );
  };

  renderTop = () => {
    const { quizDetails, result } = this.state;
    const scoreEach = {};
    JSON.parse(result[0][0][0].score_individual).forEach(
      (each) => (scoreEach[Object.keys(each)] = Object.values(each)[0])
    );
    const nums = quizDetails[0][1];
    // console.log(result);
    return (
      <>
        <div className="buttons2" style={{ marginTop: 20 }}>
          <p style={{ marginBottom: 10 }}>Questions</p>
          {nums.map((e, i) => (
            <button
              className={` numbuttonresult`}
              onClick={() => this.setState({ qNum: i })}
              key={i - 1 + "viewResult"}
              id={i + "viewResult"}
            >
              {/* {scoreEach[i] ? (
                <p>
                  {scoreEach[i].split("score")[1] === "1" ? (
                    <CheckIcon className="save checkiconq" />
                  ) : (
                    <ClearIcon className="back clearIconcss checkiconq" />
                  )}
                </p>
              ) : (
                <p className="back clearIconcss checkiconq notattempteed">-</p>
              )} */}
              {i + 1}
            </button>
          ))}
        </div>
      </>
    );
  };

  shareRanksheet = async () => {
    const { quizDetails, result, attempt } = this.state;
    const quizInfo = quizDetails[0][0][0];
    const { match } = this.props;
    const { params } = match;
    const { uid, quizid } = params;
    const response = await fetch(
      `${commonData["api"]}/download-pdf/ranksheet/${quizid}/${
        quizInfo.quiz_name + "_" + "Ranksheet_PDF"
      }`
    );
    const buffer = await response.arrayBuffer();

    const pdf = new File([buffer], "hello.pdf", { type: "application/pdf" });
    var filesArray = [];

    filesArray.push(pdf);

    navigator["share"]({ files: filesArray })
      .then(() => console.log("Share was successful."))
      .catch((error) => console.log("Sharing failed", error));
  };

  renderScoreBoard = () => {
    const { quizDetails, result, attempt } = this.state;

    const { match } = this.props;
    const { params } = match;
    const { uid, quizid } = params;
    // console.log(quizDetails, result);
    const quizInfo = quizDetails[0][0][0];
    const minScore = (quizInfo["@q"].split(",").length * 35) / 100;
    // console.log(minScore);
    const rows = result[0][1].filter((val) => val.score_obtained >= minScore);

    return (
      <>
        <div style={{ display: "flex", justifyContent: "center" }}>
          <a
            href={`${
              commonData["api"]
            }/download-pdf/ranksheet/${quizid}/${"Ranksheet_PDF"}`}
            className="linkto"
            target="_blank"
          >
            <div
              style={{
                marginBottom: 20,
                color: "white",
              }}
              className="rankdownloadbtn"
            >
              <button
                className="btn header-btns"
                style={{
                  cursor: "pointer",
                  borderRadius: 10,
                }}
              >
                Download Rank Sheet
              </button>{" "}
            </div>
          </a>{" "}
          <div
            style={{
              marginBottom: 20,
              color: "white",
            }}
            onClick={this.shareRanksheet}
            className="rankdownloadbtn deskshareapp"
          >
            <button
              className="btn header-btns"
              style={{
                cursor: "pointer",
                borderRadius: 10,
              }}
            >
              Share Rank Sheet
            </button>
          </div>
        </div>
        {rows.length > 0 && (
          <div className="ranktopperofday">
            <p style={{ textAlign: "center" }}>
              <i className="bi bi-hand-thumbs-up"> </i>
              Congratulations to Top {rows.slice(0, 3).length} Rankers
            </p>
            <div className="ranknametop2">
              {rows.slice(0, 2).map((e) => (
                <div className="ranknametop" key={"ranknametop" + e.name}>
                  <p className="ranknametoprank">{e.rank}</p>
                  <p className="ranknametopname">{e.name}</p>
                  {/* <p className="ranknametopscore">Score : {e.score_obtained}</p> */}
                </div>
              ))}
            </div>
            <div className="ranknametop2">
              {rows.slice(2, 3).map((e) => (
                <div className="ranknametop" key={"ranknametop" + e.name}>
                  <p className="ranknametoprank">{e.rank}</p>
                  <p className="ranknametopname">{e.name}</p>
                  {/* <p className="ranknametopscore">Score : {e.score_obtained}</p> */}
                </div>
              ))}
            </div>
          </div>
        )}
        <TableContainer component={Paper} className="tablecontainer-container2">
          <Table
            sx={{
              width: "100%",
              height: "auto",
              // padding: "20px",
            }}
            lg={{
              width: "100%",
              height: "auto",
              // padding: "20px",
            }}
            // lg={{ width: "80vw", padding: "20px", margin: "30px" }}
            aria-label="simple table"
          >
            <TableBody>
              <TableRow style={{ backgroundColor: "black", color: "white" }}>
                <TableCell
                  className="categoryHeader"
                  component="th"
                  scope="row"
                  align="center"
                >
                  Name
                </TableCell>
                <TableCell className="categoryHeader" align="center">
                  District
                </TableCell>
                <TableCell className="categoryHeader" align="center">
                  Score Obtained
                </TableCell>
                <TableCell className="categoryHeader" align="center">
                  Rank
                </TableCell>
              </TableRow>

              {rows.map((row, idx) => (
                <TableRow
                  key={row.name + idx + "result56"}
                  // sx={{ "&:last-child td, &:last-child th": { border: 0 } }}
                >
                  <TableCell
                    // component="th"
                    scope="row"
                    align="center"
                    className={`tablecell ${
                      row.contact === uid ? "rankNew" : ""
                    }`}
                  >
                    {row.name}
                  </TableCell>
                  <TableCell
                    align="center"
                    className={`tablecell ${
                      row.contact === uid ? "rankNew" : ""
                    }`}
                  >
                    {row.district}
                  </TableCell>
                  <TableCell
                    align="center"
                    className={`tablecell ${
                      row.contact === uid ? "rankNew" : ""
                    }`}
                  >
                    {row.score_obtained}
                  </TableCell>
                  <TableCell
                    align="center"
                    className={`tablecell ${
                      row.contact === uid ? "rankNew" : ""
                    }`}
                  >
                    {row.rank}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <p style={{ fontSize: 14, color: "#FFF", marginTop: 10 }}>
            *Note:మీరు మొదటిసారి రాసిన పరీక్ష స్కోర్ {"&"} మొత్తం మార్కులలో 35
            శాతం వచ్చిన వారి పేర్లు మాత్రమే లీడర్ బోర్డ్ లో చూపబడుతోంది.
          </p>
        </TableContainer>
      </>
    );
  };

  renderQuestBtns = () => {
    const { quizDetails, qNum } = this.state;
    const nums = quizDetails[0][1];
    // console.log(nums);
    return (
      <div className="attempt-page-btns">
        <Button
          variant="contained"
          className="btn header-btns attemptbtn attempt-btns back shadeing"
          onClick={() => {
            if (qNum > 0) {
              const each = nums[this.state.qNum - 1];

              let enableEM =
                this.state.englishMedium &&
                each?.question_in_english &&
                each?.options_english;
              this.setState((prev) => ({
                qNum: prev.qNum - 1,
                disableNext: false,
                englishMedium: this.state.englishMedium
                  ? enableEM
                    ? prev.englishMedium
                    : !prev.englishMedium
                  : false,
              }));
            } else {
              this.setState((prev) => ({
                disableback: true,
              }));
            }
          }}
        >
          Previous
        </Button>
        <Button
          variant="contained"
          className="btn header-btns attemptbtn attempt-btns save shadeing"
          onClick={() => {
            if (qNum < nums.length - 1) {
              const each = nums[this.state.qNum + 1];

              let enableEM =
                this.state.englishMedium &&
                each?.question_in_english &&
                each?.options_english;
              this.setState((prev) => ({
                qNum: prev.qNum + 1,
                disableback: false,
                englishMedium: this.state.englishMedium
                  ? enableEM
                    ? prev.englishMedium
                    : !prev.englishMedium
                  : false,
              }));
            } else {
              this.setState((prev) => ({
                disableback: false,
                disableNext: true,
              }));
            }
          }}
        >
          Next
        </Button>
      </div>
    );
  };

  renderSubjectWiseAnalysis = () => {
    const { quizDetails, result } = this.state;
    const scoreEach = {};
    JSON.parse(result[0][0][0].score_individual).forEach(
      (each) => (scoreEach[Object.keys(each)] = Object.values(each)[0])
    );
    // console.log(scoreEach);
    let categoryQuestionCount = {};
    quizDetails[0][1].forEach((each, i) => {
      // console.log(categoryQuestionCount);
      categoryQuestionCount[each.cid] = categoryQuestionCount[each.cid]
        ? categoryQuestionCount[each.cid] + "," + i
        : String(i);
    });
    // console.log(categoryQuestionCount);
    // let firstQuestion = {
    //   [Object.keys(categoryQuestionCount)[0]]:
    //     "0," + Object.values(categoryQuestionCount)[0],
    // };
    // categoryQuestionCount = { ...categoryQuestionCount, ...firstQuestion };
    const categoryName = {};
    const categoryidName = quizDetails[0][2];
    categoryidName.forEach(
      (each) =>
        (categoryName[each.category.split(",")[0]] = each.category
          .split(",")[1]
          .trim())
    );

    const categoryData = [];
    Object.keys(categoryQuestionCount).forEach((each) =>
      categoryData.push({
        [categoryName[each]]: categoryQuestionCount[each].split(","),
      })
    );
    // console.log(categoryQuestionCount);

    // const attemptedQuestions = {};

    const attemptedAnswersData = [];
    categoryData.forEach((each, i) => {
      Object.values(each).forEach((e, index) => {
        const data = e.map((qidss) => {
          for (let qidate of Object.keys(scoreEach)) {
            if (qidss === String(parseInt(qidate))) {
              return scoreEach[qidate].split("score")[1];
            }
          }
        });
        // console.log(data);
        attemptedAnswersData.push({ [Object.keys(each)[index]]: data });
      });
      // console.log(each);
    });
    // console.log(categoryData);
    const totalMarks = attemptedAnswersData.map((row) => {
      const k = Object.values(row)[0].filter(
        (each) => each !== undefined
      ).length;

      const score =
        k !== 0
          ? Object.values(row)[0]
              .filter((each) => each !== undefined)
              .reduce((val, acc) => parseInt(val) + parseInt(acc))
          : 0;
      return score;
    });

    const NotAttempt = attemptedAnswersData.map((row) => {
      return (
        Object.values(row)[0].length -
        (Object.values(row)[0].filter((each) => each !== undefined).length === 0
          ? 0
          : Object.values(row)[0].filter((each) => each !== undefined).length)
      );
    });

    // console.log(attemptedAnswersData);
    return (
      <>
        <TableContainer
          component={Paper}
          className="tablecontainer-container"
          sx={{
            // width: "90vw",
            height: "auto",
            paddingLeft: "20px",
            paddingRight: "20px",
            paddingBottom: 1,
          }}
        >
          <Accordion
            TransitionProps={{ unmountOnExit: true }}
            className="accroding-style"
            // expanded={expanded === "category"}
            // onChange={(event, isExpanded) => {
            //   const acc = isExpanded ? "category" : false;
            //   this.setState({ expanded: acc });
            // }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon className="expandIcon" />}
              aria-controls="panel1a-content"
              id="panel1a-header"
            >
              <p>Subject Wise Analysis</p>
            </AccordionSummary>
            <AccordionDetails>
              <Table
                sx={{
                  // width: "100vw",
                  height: "auto",
                  // padding: "20px",
                  border: "2px solid white",
                }}
                // lg={{ width: "95vw", padding: "20px", margin: "30px" }}
                aria-label="simple table"
              >
                <TableBody sx={{ width: "100%" }}>
                  <TableRow sx={{ border: "2px solid white" }}>
                    <TableCell className="categoryHeader">
                      {"Category Name"}
                    </TableCell>
                    <TableCell className="categoryHeader">{"Marks"}</TableCell>

                    <TableCell className="categoryHeader">
                      <p>Not</p>
                      <p>Attempted</p>
                    </TableCell>
                  </TableRow>
                  {attemptedAnswersData.map((row, i) => (
                    <TableRow
                      key={row.name + "result98" + i}
                      sx={{ border: "2px solid white" }}
                    >
                      <TableCell
                        component="th"
                        scope="row"
                        className="tablecell"
                      >
                        {Object.keys(row)[0]}
                      </TableCell>
                      <TableCell align="center" className="tablecell">
                        {Object.values(row)[0].filter(
                          (each) => each !== undefined
                        ).length !== 0
                          ? Object.values(row)[0]
                              .filter((each) => each !== undefined)
                              .reduce(
                                (val, acc) => parseInt(val) + parseInt(acc)
                              )
                          : 0}
                      </TableCell>

                      <TableCell align="center" className="tablecell">
                        {
                          Object.values(row)[0].length -
                            (Object.values(row)[0].filter(
                              (each) => each !== undefined
                            ).length === 0
                              ? 0
                              : Object.values(row)[0].filter(
                                  (each) => each !== undefined
                                ).length) /*+
                          " out of " +
                            Object.values(row)[0].length*/
                        }
                      </TableCell>
                    </TableRow>
                  ))}
                  <TableRow sx={{ border: "2px solid white" }}>
                    <TableCell className="categoryHeader">
                      {"Total Score Secured"}
                    </TableCell>
                    <TableCell className="categoryHeader">
                      {totalMarks.length !== 0
                        ? totalMarks.reduce(
                            (val, acc) => parseInt(val) + parseInt(acc)
                          )
                        : 0}
                    </TableCell>
                    <TableCell className="categoryHeader">
                      {NotAttempt.length !== 0
                        ? NotAttempt.reduce(
                            (val, acc) => parseInt(val) + parseInt(acc)
                          )
                        : 0}
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </AccordionDetails>
          </Accordion>
        </TableContainer>
      </>
    );
  };

  getBase64ImageFromURL = (url) => {
    return new Promise((resolve, reject) => {
      var img = new Image();
      img.setAttribute("crossOrigin", "anonymous");

      img.onload = () => {
        var canvas = document.createElement("canvas");
        canvas.width = img.width;
        canvas.height = img.height;

        var ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0);

        var dataURL = canvas.toDataURL("image/png");

        resolve(dataURL);
      };

      img.onerror = (error) => {
        reject(error);
      };

      img.src = url;
    });
  };

  getLocalTime = (time) => {
    return (
      new Date(time).toLocaleDateString() +
      " " +
      new Date(time).toLocaleTimeString()
    );
  };

  getHtml = () => {
    const { result } = this.state;
    const rows = result[0][1];
    return <p>test</p>;
  };
  handleClose = () => {
    this.setState((prev) => ({ dialogOpen: !prev.dialogOpen }));
  };
  renderAnswerSheet = () => {
    // const { groupName, expiryDate, userDetails } = this.state;
    // const n = await this.getBase64ImageFromURL("../../pdf_top.jpeg");
    const { result, quizDetails, dialogOpen } = this.state;
    const rows = result[0][1];
    return (
      <Dialog
        open={dialogOpen}
        onClose={this.handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          {"Do you really want to submit this Exam ?"}
        </DialogTitle>
        <DialogContent>
          <table
            className="table "
            id="tttt"
            style={{
              width: "130vw",
              overflow: "scroll",
              minWidth: 10,
              minHeight: 10,
              margin: 20,
            }}
          >
            <thead>
              <tr>
                <th className="tablehead examdetails">Exam Name</th>
                <th className="tablehead examdetails">
                  {"quizDetails.quiz_name"}
                </th>
              </tr>
            </thead>
            <thead>
              <tr>
                <th className="tablehead examdetails">Time Spent</th>
                <th className="tablehead examdetails">
                  {"this.timeConvert(timeSpent)"}
                </th>
              </tr>
            </thead>
            <thead>
              <tr>
                <th className="tablehead examdetails">Attempted Questions</th>
                <th className="tablehead examdetails">
                  {"Object.keys(answers).length"}
                </th>
              </tr>
            </thead>
            <thead>
              <tr>
                <th className="tablehead examdetails">
                  Not Attempted Questions
                </th>
                <th className="tablehead examdetails">
                  {"questions.length - Object.keys(answers).length"}
                </th>
              </tr>
            </thead>
          </table>
          <div className="submit-popup-btn">
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.handleClose}
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                const doc = new jsPDF("p", "pt", "a4", [400, 480]);
                doc.html(document.querySelector("#tttt"), {
                  callback: (doc) => {
                    doc.save("a4.pdf");
                  },
                });
              }}
              className="btn header-btns attemptbtn attempt-btns save popbtn"
            >
              Submit Exam
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  render() {
    const { quizDetails, isLoading } = this.state;

    return (
      <div>
        {isLoading ? (
          <div className="loader-main-container">
            <Loader />
          </div>
        ) : (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetails">
                <DesktopMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop22">
                <p className="homepage-package-title attempt-title viewresulttitle">
                  {/* <p style={{ fontSize: 15 }}> */}
                  {"Detailed Result Analysis - Question & Answers"}
                  {/* </p> */}

                  <p style={{ textDecoration: "underline" }}>
                    {quizDetails[0][0][0].quiz_name}
                  </p>
                </p>
                {this.Table()}
                {this.renderSubjectWiseAnalysis()}
                <TableContainer
                  component={Paper}
                  className="tablecontainer-container"
                  sx={{
                    // width: "90vw",
                    height: "auto",
                    paddingLeft: "20px",
                    paddingRight: "20px",
                    paddingBottom: 1,
                  }}
                >
                  <Accordion
                    TransitionProps={{ unmountOnExit: true }}
                    className="accroding-style"
                    // expanded={expanded === "qa"}
                    // onChange={(event, isExpanded) => {
                    //   const acc = isExpanded ? "qa" : false;
                    //   this.setState({ expanded: acc });
                    // }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon className="expandIcon" />}
                      aria-controls="panel1a-content"
                      id="panel1a-header"
                    >
                      <p>Question {" & "} Answers</p>
                    </AccordionSummary>
                    {quizDetails[0][0][0]["question_selection"] === 1 ? (
                      quizDetails[0][0][0].rankView === 1 ? (
                        <AccordionDetails>
                          {/* {this.renderQA()} */}
                          {this.state.isQuestionUpdating ? (
                            <Loader />
                          ) : (
                            this.renderQA()
                          )}
                          {this.renderQuestBtns()}
                          {this.renderTop()}
                        </AccordionDetails>
                      ) : (
                        <p style={{ textAlign: "center" }}>
                          Answer Sheet Will be Updated after 9 PM today
                        </p>
                      )
                    ) : (
                      <AccordionDetails>
                        {/* {this.renderQA()} */}
                        {this.renderQA()}
                        {this.renderQuestBtns()}
                        {this.renderTop()}
                      </AccordionDetails>
                    )}
                  </Accordion>
                </TableContainer>

                <div
                  style={{
                    paddingLeft: "20px",
                    paddingRight: "20px",
                    paddingBottom: "10px",
                  }}
                >
                  <Accordion
                    className="accroding-style"
                    // expanded={expanded === "Score"}
                    // onChange={(event, isExpanded) => {
                    //   const acc = isExpanded ? "Score" : false;
                    //   this.setState({ expanded: acc });
                    // }}
                  >
                    <AccordionSummary
                      expandIcon={<ExpandMoreIcon className="expandIcon" />}
                      aria-controls="panel1a-content"
                      id="panel1a-header"
                    >
                      <p>Student Score Board</p>
                    </AccordionSummary>
                    {quizDetails[0][0][0].rankView === 1 ? (
                      <AccordionDetails>
                        {this.renderScoreBoard()}
                      </AccordionDetails>
                    ) : (
                      <p style={{ textAlign: "center" }}>
                        Rank Sheet Will be Updated after 9 PM today
                      </p>
                    )}
                  </Accordion>
                </div>

                {/* {(quizDetails[0][0][0].question_selection === 1 && quizDetails[0][0][0].rankView === 1) ? (this.renderAnswerSheet()) : (
                  <p style={{ textAlign: "center" }}>
                    Answer Sheet Will be Updated after 9 PM today
                  </p>
                )} */}

                {/* <button
                  onClick={() => {
                    this.setState({ dialogOpen: true });
                  }}
                >
                  download
                </button> */}
                {this.renderPopUp()}
                <div>
                  <NotificationContainer />
                </div>
              </div>
            </div>
            <EditQuestion
              open={this.state.showEditDialog}
              questionId={this.state.editQuestionId}
              onClose={this.handleEditClose}
              isEnglishMedium={this.state.isEnglishMedium}
            />
          </>
        )}
      </div>
    );
  }
}

export default ViewResult;
