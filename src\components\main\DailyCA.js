import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import DesktopMenu from "../DesktopMenu";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import commonData from "../../importanValue";
import "./styles.css";

class DailyCA extends react.Component {
  state = { isLoading: true, data: [], uid: "" };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const resultList = await axios.get(
      `${commonData["api"]}/all-exam-schedules/`,
      {
        headers,
      }
    );
    // console.log(resultList.data);
    const uid = localStorage.getItem("uid");
    this.setState({
      data: resultList.data[0].filter((e) => e.category === "ca"),
      isLoading: false,
      uid: uid,
    });
  };

  render() {
    const { isLoading, data } = this.state;
    console.log(data);
    return (
      <>
        {!isLoading && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetails">
                <DesktopMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop22">
                <div className="title-quiz" id="print">
                  <p className="homepage-package-title examlist-ttle">
                    Study Materials
                  </p>
                </div>

                {/* <Divider color="white" /> */}
                <div className="main-table-container">
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="tablehead">Name</th>
                        <th className="tablehead">Action</th>
                      </tr>
                    </thead>
                    {data.length > 0 ? (
                      data.map((each, index) => (
                        <tbody key={`schdulelist${each.gid}`}>
                          <tr>
                            <td className="tabledata">
                              <p>{each.group_name}</p>
                            </td>
                            <td className="tabledata">
                              <a
                                href={each.drive_link}
                                target={"_blank"}
                                className="linkto"
                              >
                                <div
                                  className="gotoexamsbtnhome"
                                  style={{ marginTop: -20 }}
                                >
                                  <Button
                                    variant="contained"
                                    className="btn header-btns attemptbtn"
                                  >
                                    View
                                  </Button>
                                </div>
                              </a>
                            </td>
                          </tr>
                        </tbody>
                      ))
                    ) : (
                      <tbody>
                        <tr style={{ textAlign: "center", color: "#fff" }}>
                          <td colSpan={4}>No results are available..</td>
                        </tr>
                      </tbody>
                    )}
                  </table>
                </div>
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
      </>
    );
  }
}

export default DailyCA;
