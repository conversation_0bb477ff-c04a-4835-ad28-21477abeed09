import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import TextField from "@mui/material/TextField";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import Autocomplete from "@mui/material/Autocomplete";
import "./styles.css";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
class ReferAdmin extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    referData: [],
    page: 0,
    popUpOpen: false,
    editorLoading: false,
    search: "",
    referCount: 0,
    popupType: "",
    cCount: "",
    percent: 0,
    percentEditClicked: false,
    percentageValue: 0,
    userName: "",
    popUpOpen: false,
    amount: 0,
    userNames: [],
    num: "",
    referId: "",
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const { page, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "ReferDataALL",
      search: search,
      qid: page * 25,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );

      this.setState({
        referData: data.data[0],
        referCount: data.data[1][0].count,
        percent: data.data[1][0]["@percentX"],
        isLoading: false,
        userNames: data.data[2],
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  addMinusAmount = async () => {
    const { userName, id, amount, popupType, referData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: popupType,
      search: userName + "$$" + id,
      qid: parseInt(amount),
    };
    // console.log(amount);
    if (userName === "") {
      NotificationManager.error(`Please Enter valid Data`);
    } else if (amount.trim() === "" || amount === "0" || isNaN(amount)) {
      NotificationManager.error(`Please Enter the amount`);
    } else {
      try {
        // this.setState({ isLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        const userData = referData.filter((e) => e.contact_no === id)[0];
        const userDataIndex = referData.findIndex((e) => e === userData);
        // console.log("userDataIndex", userDataIndex);
        const newData = referData;
        newData.splice(userDataIndex, 1, data.data[0][0]);

        // this.setState({referData:data.data[0]})
        // console.log(data, newData);
        NotificationManager.success(
          `${
            popupType === "addmoney"
              ? "Amount added to Wallet"
              : "Amount Withdran from Wallet"
          } successfully..`
        );
        this.setState({ referData: newData }, this.handleOpen);
        // this.handleOpen();
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };

  searchCoupon = (e) => {
    e.preventDefault();
    const { search } = this.state;

    if (search === "") {
      NotificationManager.error(`Please Enter Search Value`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
      });
      this.getData();
    }
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      userName: "",
      amount: 0,
      popupType: "",
      id: "",
    }));
  };

  renderPopUp = () => {
    const { popUpOpen, userName, userNames, popupType, id } = this.state;
    console.log(popupType);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              {popupType === "addmoney" ? "Add Amount" : "Withdraw Money"}
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={this.handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "space-around",
              marginTop: 20,
            }}
          >
            {popupType === "addmoney" ? (
              <Autocomplete
                id="free-solo-demo"
                freeSolo
                required
                getOptionLabel={(option) => option || ""} 
                onChange={(event, newValue) => {
                  // console.log(newValue);
                  this.setState({ userName: newValue });
                }}
                options={userNames.map((option) => option.userNames)}
                renderInput={(params) => (
                  <TextField {...params} label="Select the user reffered" />
                )}
              />
            ) : (
              <TextField
                required
                label="Enter The Description"
                style={{ marginTop: 20 }}
                onChange={(event) => {
                  this.setState({ userName: event.target.value });
                }}
              />
            )}
            <TextField
              required
              label="Enter The Amount"
              onChange={(event) => {
                this.setState({ amount: event.target.value });
              }}
              style={{ marginTop: 20 }}
            />
          </div>
        </DialogContent>
        <DialogActions>
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={this.addMinusAmount}
          >
            {popupType === "addmoney" ? "Add Money" : "Withdraw Money"}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  showDescription = () => {
    const { referData, num } = this.state;
    const userData = referData.filter((e) => e.contact_no === num)[0];
    userData["showData"] = userData["showData"] === 0 ? 1 : 0;
    const userDataIndex = referData.findIndex((e) => e === userData);
    // console.log("userDataIndex", userDataIndex);
    const newData = referData;
    newData.splice(userDataIndex, 1, userData);
    this.setState({ referData: newData });
  };

  deleteUserAmount = async () => {
    const { num, referId, referData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "removeReferInfo",
      search: referId,
      qid: num,
    };
    if (window.confirm("Do you really want to remove this refer Data?")) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        const userData = referData.filter((e) => e.contact_no === num)[0];
        const userDataIndex = referData.findIndex((e) => e === userData);
        const newData = referData;
        newData.splice(userDataIndex, 1, data.data[0][0]);
        this.setState({ referData: newData, num: "", referId: "" });
        NotificationManager.success(`Refer Data Deleted Succesfully...`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };

  categoriesTable = () => {
    const {
      referData,
      referCount,
      searchClicked,
      search,
      percent,
      percentEditClicked,
    } = this.state;
    // console.log(referData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;    
        width:100%;   
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All Refer Data</h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            {!searchClicked ? (
              <form
                onSubmit={this.searchCoupon}
                style={{ cursor: "pointer", display: "flex", marginBottom: 10 }}
              >
                <input
                  type={"text"}
                  value={search}
                  id="search"
                  onChange={(e) => this.setState({ search: e.target.value })}
                  placeholder="Enter Number"
                />
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                    // marginLeft: 5,
                    backgroundColor: "green",
                    padding: 5,
                    color: "#fff",
                    borderRadius: 10,
                  }}
                >
                  <button
                    type="submit"
                    style={{
                      backgroundColor: "transparent",
                      borderWidth: 0,
                      color: "#fff",
                    }}
                  >
                    <i className="bi bi-search"></i>
                  </button>
                </div>
              </form>
            ) : (
              <i
                className="bi bi-search"
                style={{
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                }}
                onClick={() => {
                  this.setState(
                    {
                      search: "",
                      searchClicked: false,
                      isLoading: true,
                    },
                    this.getData
                  );
                }}
              >
                Search Again
              </i>
            )}
          </div>
          <div className="btn exportbtn" style={{ padding: 10 }}>
            User Commission :
            {percentEditClicked ? (
              <input
                value={percent}
                style={{ width: 30 }}
                onChange={(e) => this.setState({ percent: e.target.value })}
              />
            ) : (
              percent + "%"
            )}
            <i
              className={`bi ${
                percentEditClicked ? "bi-check-circle-fill" : "bi-pencil-fill"
              } `}
              style={{ cursor: "pointer", color: "black", paddingLeft: 10 }}
              onClick={() => {
                this.setState({ percentEditClicked: !percentEditClicked });
                if (percentEditClicked) {
                  this.updatePercent();
                }
              }}
            ></i>
          </div>
        </div>
        <div style={{ overflowX: "scroll" }}>
          <table>
            <thead>
              <tr>
                <th>Student Name</th>
                <th style={{ textAlign: "center" }}>Phone</th>
                <th>Earned</th>
                <th>Action</th>
              </tr>
            </thead>
            <tbody>
              {referData.length > 0 ? (
                referData.map((e, i) => {
                  return (
                    <tr key={"refered" + e.contact_no}>
                      <td>
                        <div style={{ display: "flex", marginBottom: 5 }}>
                          <p>
                            {e.showData ? (
                              <span
                                style={{
                                  cursor: "pointer",
                                  marginRight: 15,
                                  marginBottom: 5,
                                }}
                                onClick={() =>
                                  this.setState(
                                    { num: e.contact_no },
                                    this.showDescription
                                  )
                                }
                              >
                                &#9660;
                              </span>
                            ) : (
                              <span
                                style={{
                                  cursor: "pointer",
                                  marginRight: 15,
                                  marginBottom: 5,
                                }}
                                onClick={() =>
                                  this.setState(
                                    { num: e.contact_no },
                                    this.showDescription
                                  )
                                }
                              >
                                &#9654;
                              </span>
                            )}
                            {e.studentName}
                          </p>
                        </div>
                        {e.showData === 1 && (
                          <div
                            style={{
                              border: "2px solid green",
                              borderRadius: 15,
                              padding: 10,
                            }}
                          >
                            {e.referData.split(",").map((ee, i) => (
                              <p
                                style={{
                                  marginBottom: 10,
                                }}
                                key={"referInfoEach" + i}
                              >
                                {i +
                                  1 +
                                  " ) " +
                                  ee.split("$$")[1] +
                                  " - ₹" +
                                  ee.split("$$")[2]}{" "}
                                {/* <span
                                onClick={() =>
                                  this.setState(
                                    {
                                      referId: ee.split("$$")[0],
                                      num: e.contact_no,
                                    },
                                    this.deleteUserAmount
                                  )
                                }
                              ></span> */}
                                <i
                                  className="bi bi-trash-fill"
                                  style={{ cursor: "pointer" }}
                                  onClick={() =>
                                    this.setState(
                                      {
                                        referId: ee.split("$$")[0],
                                        num: e.contact_no,
                                      },
                                      this.deleteUserAmount
                                    )
                                  }
                                ></i>
                              </p>
                            ))}
                          </div>
                        )}
                      </td>

                      <td>{e.contact_no}</td>
                      <td style={{ textAlign: "center" }}>{e.earned}</td>
                      <td>
                        <div
                          style={{ display: "flex", justifyContent: "center" }}
                        >
                          <i
                            className={`bi bi-plus-circle`}
                            style={{ cursor: "pointer", marginRight: 30 }}
                            onClick={() =>
                              this.setState({
                                id: e.contact_no,
                                popupType: "addmoney",
                                popUpOpen: true,
                              })
                            }
                          ></i>
                          <i
                            className={`bi bi-dash-circle`}
                            style={{ cursor: "pointer" }}
                            onClick={() =>
                              this.setState({
                                id: e.contact_no,
                                popupType: "removemoney",
                                popUpOpen: true,
                              })
                            }
                          ></i>
                        </div>
                      </td>
                    </tr>
                  );
                })
              ) : (
                <tr
                  style={{
                    display: "flex",
                    justifyContent: "center",
                    marginTop: 20,
                  }}
                >
                  <td colSpan={4}>
                    <p style={{ color: "black", textAlign: "center" }}>
                      No Data is Available...
                    </p>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
        {this.renderPaginationButtons(referCount)}
      </div>
    );
  };

  renderPaginationButtons = (totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    // console.log(paidcount);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount - 1}
        >
          Next
        </Button>
      </div>
    );
  };

  updatePercent = async () => {
    const { percent } = this.state;

    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "percentUpdate",
      search: "",
      qid: parseInt(percent),
    };
    if (parseInt(percent) < 0) {
      NotificationManager.error(`Please Enter valid value`);
    } else {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );

        NotificationManager.success(`Percentage Updated Successfully...`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  render() {
    const { isLoading, login, popUpOpen } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.categoriesTable()}
                {this.renderPopUp()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default ReferAdmin;
