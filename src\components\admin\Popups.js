import React, { Component } from "react";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";

class PopUps extends Component {
  render() {
    return (
      <Dialog
        open={dialogOpen}
        onClose={this.handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          {"Do you really want to submit this Exam ?"}
        </DialogTitle>
        <DialogContent>
          <div className="submit-popup-btn">
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.handleClose}
            >
              Cancel
            </Button>
            <Button
              onClick={this.submitQuiz}
              className="btn header-btns attemptbtn attempt-btns save popbtn"
            >
              Submit Exam
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }
}

export default PopUps;
