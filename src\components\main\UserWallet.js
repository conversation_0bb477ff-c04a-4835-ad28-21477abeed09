import React from "react";
import Header from "../Header";
import Loader from "../Loader";
// import axios from "axios";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "./styles.css";
import Divider from "@mui/material/Divider";
import DesktopMenu from "../DesktopMenu";
import axios from "axios";
import commonData from "../../importanValue";

import <PERSON><PERSON> from "js-cookie";
import ScratchCard from "./ScratchCard";

class UserWallet extends React.Component {
  state = {
    isLoading: false,
    userData: [],
    referData: [],
    availablePacks: [],
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const uid = localStorage.getItem("num");

    const body = {
      type: "userWallet",
      search: "",
      qid: uid,
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.post(`${commonData["api"]}/support`, body, {
        headers,
      });
      // console.log(data);
      this.setState({
        userData: data.data[0][0],
        referData: data.data[1],
        availablePacks: data.data[2],
        isLoading: false,
      });
      localStorage.setItem(
        "userWallet",
        data.data[0][0].amountTotal === null ? "0" : data.data[0][0].amountTotal
      );
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  getPacks = () => {
    const { availablePacks } = this.state;
    let text = "";
    let x = 1;
    for (let each of availablePacks) {
      text = text + x + " )" + each.group_name + " " + "%0a";
      x = x + 1;
    }
    // console.log(text);
    return text;
  };
  render() {
    const { isLoading, userData, referData } = this.state;
    // console.log("userData", referData);
    return (
      <>
        <div className="desktopsidebar">
          <div className="desktopsidebarmenuexamdetails">
            <DesktopMenu />
          </div>
          <Header />

          <Divider color="white" />
          <div className="viewresultsdesktop22 edituser">
            <div className="packages-container" style={{ marginTop: 30 }}>
              <div className="packages-inner-container" style={{ padding: 20 }}>
                {!isLoading ? (
                  <>
                    <p className="main">My Wallet</p>
                    <table className="table">
                      <thead>
                        <tr>
                          <th className="tablehead examdetails fontUSerpay">
                            Total Earnings : Rs.{" "}
                            {userData.amountTotal === null
                              ? "0"
                              : userData.amountTotal}
                          </th>

                          <th className="tablehead examdetails fontUSerpay">
                            Used : Rs. 0
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td
                            colSpan={3}
                            className="tablehead examdetails fontUSerpay"
                          >
                            <p style={{ textAlign: "center" }}>
                              {" "}
                              Get Discount Here
                            </p>
                            <div className="sharelink">
                              <div>
                                <ScratchCard
                                  linkText={`whatsapp://send?text=Hai,%0aThis is *${
                                    userData.first_name +
                                    " " +
                                    userData.last_name
                                  }*%0aనేను రీసెంట్ గా నవచైతన్య కాంపిటీషన్స్ నుంచి *${
                                    userData.latestPackage
                                  }*   తీసుకున్నాను. పరీక్షలను రాస్తూ ఉన్నాను. ఈ వెబ్ సైట్/యాప్ లో క్వాలిటీ ప్రశ్నలతో ఆన్ లైన్ పరీక్షలను అందిస్తున్నారు. 
                                బిట్స్ ఎక్కడా కాపీ చేసినవి కాకుండా, లైన్ టూ లైన్ టెక్స్ట్ బుక్ చదివితే తప్ప, సమాధానం గుర్తించలేమనే విధంగా ఉన్న ఈ పరీక్షలను మీరూ రాయాలనుకుంటే వెంటనే క్రింది లింక్ పై క్లిక్ చేసి, నచ్చిన ప్యాకేజి డిటెయిల్స్ తెలుసుకుని పేమెంట్ చేయండి - పరీక్షలు రాయండి..%0a%0aJoin Through This Link >> %20%20https%3A%2F%2Fexams.navachaitanya.net%2F?refer=${
                                  userData.refercode
                                }%0a%0a*Available Packages*%0a${this.getPacks()}`}
                                  loadData={() => this.getData()}
                                  countData={
                                    referData.filter(
                                      (e) => e.joined === "Scratch Card"
                                    ).length
                                  }
                                />
                              </div>
                            </div>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                    <br />
                    <p className="main">
                      My Friends ( Joined Through Referral ){" "}
                    </p>
                    <table className="table">
                      <thead>
                        <tr>
                          <th className="tablehead examdetails fontUSerpay">
                            Name
                          </th>
                          <th className="tablehead examdetails fontUSerpay">
                            Amount Earned
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {referData.length > 0 ? (
                          referData.map((e) => (
                            <tr key={e.id + e.joined}>
                              <td className="tabledata">{e.joined}</td>
                              <td className="tabledata">{e.amountearned}</td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td className="tabledata" colSpan={2}>
                              No data Available
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </>
                ) : (
                  <div className="login-buttons-container">
                    <Loader />
                  </div>
                )}
              </div>
              <div>
                <NotificationContainer />
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default UserWallet;
