import { Component } from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
import TextField from "@mui/material/TextField";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
// import { Editor } from "@tinymce/tinymce-react";
import sendpush from "./sendNotification";

import "./styles.css";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

class AutoResponder extends Component {
  state = {
    isLoading: false,
    login: "valid",
    popUpOpen: false,
    page: 0,
    search: "",
    data: [],
    pageCount: 0,
    id: "0",
    selectedPackages: [],
    packagesAllList: [],
    popUpOpen: false,
    title: "",
    description: "",
    descriptionText: "",
  };
  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const { page, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "autoRespond",
      search: search,
      qid: "0",
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );

      console.log(data);
      this.setState({
        data: data.data[0],
        isLoading: false,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  dataTable = () => {
    const { data, pageCount } = this.state;
    // console.log(CouponData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>Auto Respond Message Creator</h3>

          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  popUpOpen: !p.popUpOpen,
                  title: "",
                  description: "",
                  id: 0,
                }));
              }}
            >
              Add New Message
            </Button>
          </div>
        </div>
        <table>
          <thead>
            <tr>
              <th>Question</th>
              <th>Auto Reply</th>
              {/* <th style={{ textAlign: "center" }}>Send Again</th> */}
              <th style={{ textAlign: "center" }}>Action</th>
            </tr>
          </thead>
          <tbody>
            {data.length > 0 ? (
              data.map((e, i) => {
                const { recieve, reply } = e;
                return (
                  <tr key={"supoortauto" + e.id}>
                    <td>
                      <div style={{ display: "flex" }}>
                        <p>{recieve}</p>
                      </div>
                    </td>

                    <td
                      style={{
                        textAlign: "center",
                        height: 60,
                        overflow: "hidden",
                      }}
                    >
                      {" "}
                      <div
                        style={{ height: 60, overflow: "hidden" }}
                        dangerouslySetInnerHTML={{
                          __html: reply,
                        }}
                      ></div>
                    </td>

                    <td style={{ textAlign: "center" }}>
                      {/* <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 10, cursor: "pointer" }}
                        // onClick={() =>

                        // }
                      ></i> */}
                      <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 15, cursor: "pointer" }}
                        onClick={() =>
                          this.setState({
                            id: e.id,
                            popUpOpen: true,
                            title: recieve,
                            description: reply,
                            descriptionText: reply,
                          })
                        }
                      ></i>
                      <i
                        className="bi bi-trash-fill"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          this.setState(
                            {
                              id: e.id,
                            },
                            this.deleteData
                          )
                        }
                      ></i>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: 20,
                }}
              >
                <td colSpan={4}>
                  <p style={{ color: "black", textAlign: "center" }}>
                    No Data is Available...
                  </p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      descriptionText: "",
      title: "",
      description: "",
      id: "0",
    }));
  };

  savedata = async () => {
    const { descriptionText, title, id } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: id !== 0 ? "SupportEdit" : "SupportAdd",
      search: title + "_" + id,
      qid: String(descriptionText),
    };
    if (title === "") {
      NotificationManager.error(`Please Enter Title`);
    } else if (descriptionText === "") {
      NotificationManager.error(`Please Enter Description`);
    } else {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );

        // console.log(data.data[0][0].result);
        if (data.data[0][0].result === "success") {
          this.setState(
            {
              popUpOpen: false,
            },
            NotificationManager.success(`Title Updated Succesfully...`)
          );
          this.getData();
        } else {
          NotificationManager.error(`Title Already Exists..`);
        }
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };
  deleteData = async () => {
    const { id, data } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "deleteAutoMessage",
      search: "",
      qid: id,
    };
    if (window.confirm("Do you really want to remove this data ?")) {
      try {
        await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
          headers,
        });
        // console.log(CouponData);
        this.setState(
          {
            data: data.filter((e) => e.id != id),
          },
          NotificationManager.success(`Data Deleted Succesfully...`)
        );
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };
  renderPopUp = () => {
    const { popUpOpen, title, descriptionText, id } = this.state;
    // const allGids = packagesAllList.map((e) => e.gid).join(",");
    // console.log(allGids);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{id === 0 ? "Add" : "Edit"} New Message</p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <TextField
            required
            className="input-box register catgroy"
            label="Question "
            variant="filled"
            value={title}
            onChange={(e) => this.setState({ title: e.target.value })}
            focused
          />
          <div>
            
            <TextField
            required
            className="input-box register catgroy"
            label="Auto Reply"
            variant="filled"
            value={descriptionText}
            onChange={(e) => this.setState({ descriptionText: e.target.value })}
            focused
          />
            {/* <Editor
              // onInit={(evt, editor) => (editorRef.current = editor)}
              initialValue={description}
              init={{
                height: 200,
                width: "100%",
                menubar: false,

                plugins: [
                  "advlist autolink lists link image charmap print preview anchor",
                  "searchreplace visualblocks code fullscreen",
                  "insertdatetime media table paste code help wordcount",
                ],
                toolbar:
                  "undo redo | formatselect | " +
                  "bold italic backcolor | alignleft aligncenter " +
                  "alignright | bullist numlist | subscript superscript |  " +
                  " code " +
                  "removeformat",
                content_style:
                  "body { font-family:Ramabhadra; font-size:14px }",
              }}
              onEditorChange={(e) => this.setState({ descriptionText: e })}
            /> */}
          </div>
          <br />
        </DialogContent>
        <DialogActions
          style={{ display: "flex", justifyContent: "center", marginLeft: -10 }}
        >
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={this.savedata}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>
    );
  };
  render() {
    const { isLoading, login, popUpOpen } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.dataTable()}
                {popUpOpen && this.renderPopUp()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default AutoResponder;
