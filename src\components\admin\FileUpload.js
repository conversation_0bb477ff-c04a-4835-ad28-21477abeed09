import React, { Component } from 'react'
import axios from 'axios'
import <PERSON><PERSON> from 'js-cookie'
import commonData from '../../importanValue'
import { NotificationManager, NotificationContainer } from 'react-notifications'
import 'react-notifications/lib/notifications.css'
export default class FilesUploadComponent extends Component {
  constructor (props) {
    super(props)
    this.onFileChange = this.onFileChange.bind(this)
    this.onSubmit = this.onSubmit.bind(this)
    this.state = {
      wordfile: '',
      data: ''
    }
  }
  onFileChange (e) {
    // console.log(e.target.files);
    this.setState({ wordfile: e.target.files[0] })
  }
  onSubmit = async e => {
    e.preventDefault()
    if (this.state.wordfile === '') {
      return NotificationManager.error(`Please select file to import..`)
    }
    const formData = new FormData()
    formData.append('file', this.state.wordfile)
    // console.log(formData);

    const token = Cookie.get('jwt_token')
    const headers = {
      'Content-Type': 'application/json',
      authorization: token,
      typeofAction: this.props.type,
      'Access-Control-Allow-Origin': '*'
    }

    this.props.loading(true)
    try {
      const data = await axios.post(
        `${commonData['api']}/upload-doc`,
        formData,
        {
          headers
        }
      )
      // if (data.data.text) {
      //   const file = new Blob([data.data.sqlMessage], {
      //     type: 'text/plain'
      //   })
      //   const element = document.createElement('a')
      //   element.href = URL.createObjectURL(file)
      //   element.download = 'error-log-file.txt'
      //   document.body.appendChild(element) // Required for this to work in FireFox
      //   element.click()
      //   NotificationManager.error(
      //     `There is some error in word file check errors...`
      //   )
      //   this.props.loading(false)
      // } else {
      this.props.loading(false)
      NotificationManager.success(`Data imported Succesfully...`)
      this.props.refresh()
      // }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`)
      this.props.loading(false)
    }
    // this.setState({ data: data.data });
  }

  // getFormattedData = () => {
  //     // const { data } = this.state
  //     const data = `<p>CATEGORY: 20 , TELUGU SUBJECT</p><p>Q:4) క్రింది వాక్యాలను పరిశీలించండి.</p><p>ఎ) కుక్క 50,000 హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు.</p><p>బి) గబ్బిలము 1<sub>,00,000 </sub>హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు.</p><p>సి) డాల్ఫిన్స్ 1,<sup>20,000 </sup>హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు</p><p>సరియైనది (వి) ఎంచుకోండి</p><p>	A:). ఎ, బి మాత్రమే	</p><p>B:). బి, సి మాత్రమే</p><p>C:). ఎ, సి మాత్రమే	</p><p>D:). <strong>ఎ, బి మరియు సి</strong></p><p>Q:4) క్రింది వాక్యాలను పరిశీలించండి.</p><p>ఎ) కుక్క 5220,000 హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు.</p><p>బి) గబ్బిలము 1<sub>,00,000 </sub>హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు.</p><p>సి) డాల్ఫిన్స్ 1,<sup>20,000 </sup>హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు</p><p>సరియైనది (వి) ఎంచుకోండి</p><p>	A:). ఎ, బి మాత్రమే	</p><p>B:). బి, సి మాత్రమే</p><p>C:). ఎ, సి మాత్రమే	</p><p>D:). <strong>ఎ, బి మరియు సి</strong></p><p>CATEGORY: 22 , ENGLISH SUBJECT</p><p>Q:4) క్రింది వాక్యాలను పరిశీలించండి.</p><p>ఎ) కుక్క 50,000 హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు.</p><p>బి) గబ్బిలము 1<sub>,00,000 </sub>హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు.</p><p>సి) డాల్ఫిన్స్ 1,<sup>20,000 </sup>హెర్ట్జ్ పౌనఃపున్యం వరకూ గల ధ్వనులను వినగలదు</p><p>సరియైనది (వి) ఎంచుకోండి</p><p>	A:). ఎ, బి మాత్రమే	</p><p>B:). బి, సి మాత్రమే</p><p>C:). ఎ, సి మాత్రమే	</p><p>D:). <strong>ఎ, బి మరియు సి</strong></p>`
  //     const allcategoryQ = data.split("CATEGORY:").slice(1)
  //     const allQuestions = []
  //     allcategoryQ.forEach((each) => {
  //         const categroyName = each.split(/Q:[0-9]+\)/)[0].split(",")[0].toString().trim()
  //         const eachQUestion = each.split(/Q:[0-9]+\)/).slice(1).map((eachQ) => {
  //             const question = eachQ.split(/[A-Z]:\)/)[0]

  //             const options = eachQ.split(/[A-Z]:\)/).slice(1).map((q, i) => q.includes("<strong>") ? '<p>' + q.replace("<p>", '') + '$$1' : '<p>' + q.replace("<p>", '') + '$$0')
  //             return ({
  //                 Q: '<p>' + question,
  //                 options,
  //                 categoryId: parseInt(categroyName.trim())

  //                 // options.map((q, i) => q.includes("<strong>") ? i : 0)
  //             })
  //         })
  //         console.log(eachQUestion)
  //         // allQuestions.push({ [categroyName]: eachQUestion })
  //     })
  // }

  render () {
    // console.log(this.getFormattedData());
    return (
      <div className='container'>
        <div className='row'>
          <form onSubmit={this.onSubmit}>
            <div className='form-group'>
              <input type='file' onChange={this.onFileChange} />
            </div>
            <div className='form-group'>
              <button
                className='btn btn-primary'
                type='submit'
                style={{
                  padding: 15,
                  border: 'none',
                  color: 'white',
                  borderRadius: 15,
                  marginTop: 10,
                  background: 'green',
                  width: 100
                }}
              >
                Upload
              </button>
            </div>
          </form>
        </div>
      </div>
    )
  }
}
