{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@date-io/date-fns": "^1.3.13", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@google-analytics/data": "^2.8.0", "@material-ui/core": "^4.12.4", "@material-ui/pickers": "^3.3.10", "@mui/icons-material": "^5.3.1", "@mui/lab": "^5.0.0-alpha.67", "@mui/material": "^5.4.0", "@reduxjs/toolkit": "^1.7.2", "@testing-library/jest-dom": "^5.16.1", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@tinymce/tinymce-react": "^3.13.1", "axios": "^0.24.0", "bootstrap": "^5.1.3", "bootstrap-less": "^3.3.8", "crypto-js": "^4.2.0", "date-fns": "^2.28.0", "docxtemplater": "^3.29.0", "file-saver": "^2.0.5", "fs": "^0.0.1-security", "fs-extra": "^10.0.1", "html-to-pdfmake": "^2.3.9", "html2canvas": "^1.4.1", "js-cookie": "^3.0.1", "jspdf": "^2.5.2", "jssha": "^3.2.0", "jszip": "^3.7.1", "papaparse": "^5.4.1", "pdf-creator-node": "^2.3.4", "pdf-lib": "^1.17.1", "pdfmake": "^0.2.4", "react": "^17.0.2", "react-data-export": "^0.6.0", "react-dom": "^17.0.2", "react-excel-renderer": "^1.1.0", "react-is": "^17.0.2", "react-js-pagination": "^3.0.3", "react-loader-spinner": "^5.1.0", "react-notifications": "^1.7.3", "react-onesignal": "^2.0.3", "react-redux": "^7.2.6", "react-responsive-pagination": "^1.3.2", "react-router-dom": "^5.3.0", "react-rte": "^0.16.5", "react-scratchcard": "^1.1.2", "react-scripts": "5.0.0", "react-toastify": "^8.1.1", "react-xml-parser": "^1.1.8", "request": "^2.88.2", "socket.io-client": "^4.7.5", "tus-js-client": "^4.3.1", "uuid": "^8.3.2", "web-vitals": "^2.1.3", "xlsx": "^0.18.5"}, "scripts": {"local": "serve -s build -l 9000", "build": "vite build", "test": "react-scripts test", "eject": "react-scripts eject", "start": "vite", "preview": "vite preview", "dev": "vite"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@vitejs/plugin-react": "^4.5.1", "vite": "^6.3.5"}}