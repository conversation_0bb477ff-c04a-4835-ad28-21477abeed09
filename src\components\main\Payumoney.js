import commonData from "../../importanValue";
import React from "react";
import axios from "axios";
import { v4 as uuidv4 } from "uuid";
import Header from "../Header";
import <PERSON>ie from "js-cookie";
import Loader from "../Loader";
import Button from "@mui/material/Button";
import Divider from "@mui/material/Divider";
import DesktopMenu from "../DesktopMenu";
import TextField from "@mui/material/TextField";
import { Redirect } from "react-router-dom";
import jsSHA from "jssha";

class PayuMoney extends React.Component {
  state = {
    userData: {},
    isLoading: true,
    groupDetails: {},
    amount: 0,
    coupon: "",
    err: "",
    couponPercent: 0,
    paid: false,
    gid: "",
    useWallet: false,
    finalWalletBal: 0,
    validity_no_of_days: 0,
  };

  isWebView = () => {
    const userAgent = navigator.userAgent || navigator.vendor || window.opera;
    return /wv|WebView/.test(userAgent);
  };

  checkUserPaid = async () => {
    const { match, history } = this.props;
    const { params } = match;
    const { gid } = params;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const num = localStorage.getItem("num");
    const body = {
      type: "checkUserPaid",
      search: num,
      qid: gid,
    };
    try {
      const res = await axios.post(`${commonData["api"]}/support`, body, {
        headers,
      });
      if (res.data.length > 0) {
        const check = res.data[0].length > 0;
        if (check) {
          history.replace(`/exams-list/${gid}/MA==`);
        } else {
        }
      }
    } catch (err) {}
  };
  componentDidMount() {
    this.checkUserPaid();
    this.getd();
  }

  getd = async () => {
    const uid = localStorage.getItem("num");
    const { match, location } = this.props;
    const { params } = match;
    const { gid } = params;
    const { search } = location;
    try {
      const groupDetails = await axios.post(
        `${commonData["api"]}/get-group-details/${gid}`
      );
      const userDetails = await axios.post(
        `${commonData["api"]}/get-user-details/${uid}`
      );

      if (
        userDetails.data.result[0].gid.split(",").indexOf(gid) !== -1 &&
        search.split("?expired=")[1] !== "1"
      ) {
        this.setState({
          userData: userDetails.data.result[0],
          isLoading: false,
          groupDetails: groupDetails.data[0],
          amount: groupDetails.data[0].price,
          paid: true,
          gid: gid,
          finalWalletBal: userDetails.data.result[0].userAmount,
        });
      } else {
        this.setState({
          userData: userDetails.data.result[0],
          isLoading: false,
          groupDetails: groupDetails.data[0],
          amount: groupDetails.data[0].price,
          paid: false,
          finalWalletBal: userDetails.data.result[0].userAmount,
        });
      }
    } catch (err) {
      Cookie.remove("jwt_token");
    }
  };

  getHash = () => {
    const { userData, amount, groupDetails, bucketId } = this.state;
    const { match } = this.props;
    const { params } = match;
    const { gid } = params;
    const expiryDate = new Date();
    const expiry =
      groupDetails.valid_for_days !== 0
        ? parseInt(groupDetails.valid_for_days)
        : 365;
    expiryDate.setDate(expiryDate.getDate() + expiry);
    const pay = {
      email: userData.email,
      firstname: userData.first_name,
      txnid: uuidv4(),
      productinfo: gid + "date" + expiryDate.toISOString().slice(0, 10),
      phone: userData.contact_no,
    };

    const hashString =
      commonData.payukey + //store in in different file
      "|" +
      pay.txnid +
      "|" +
      amount +
      "|" +
      pay.productinfo +
      "|" +
      pay.firstname +
      "|" +
      pay.email +
      "|||||||||||" +
      commonData.paysalt;
    const sha = new jsSHA("SHA-512", "TEXT");
    sha.update(hashString);
    //Getting hashed value from sha module
    const hash = sha.getHash("HEX");
    pay.key = commonData.payukey; //store in in different file;
    pay.surl = `${commonData["api"]}/payment_gateway/payumoney/success`;
    pay.furl = `${commonData["api"]}/payment_gateway/payumoney/fail`;
    pay.hash = hash;
    return pay;
  };

  activateFreePackage = async () => {
    const {
      userData,
      groupDetails,
      bucketId,
      finalWalletBal,
      validity_no_of_days,
    } = this.state;
    const token = Cookie.get("jwt_token");
    const { match, history } = this.props;
    const { params } = match;
    const { gid } = params;
    const expiryDate = new Date();

    // Set expiry based on coupon validity if it exists and is greater than 0
    const expiry =
      validity_no_of_days > 0
        ? validity_no_of_days
        : groupDetails.valid_for_days !== 0
        ? parseInt(groupDetails.valid_for_days)
        : 365;

    expiryDate.setDate(expiryDate.getDate() + expiry);
    const pay = {
      firstname: userData.first_name,
      net_amount_debit: 0,
      phone: userData.contact_no,
      productinfo: gid,
      txnid: uuidv4(),
      addedon: new Date(),
      status: "success",
      validity: expiryDate.toISOString().slice(0, 10),
      usedwalletbal: finalWalletBal,
    };
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const submitResult = await axios.post(
      `${commonData["api"]}/payment_gateway/free/success`,
      pay,
      { headers }
    );
    if (submitResult.status === 200) {
      history.replace(`/`);
    }
  };

  renderForm = () => {
    const pay = this.getHash();
    const { amount } = this.state;
    return (
      <form
        method="POST"
        action="https://secure.payu.in/_payment"
        name="payuForm"
      >
        <input type="hidden" name="key" value={pay.key} />
        <input type="hidden" name="hash" value={pay.hash} />
        <input type="hidden" name="txnid" value={pay.txnid} />
        <input type="hidden" name="phone" value={pay.phone} />
        <input type="hidden" name="amount" value={amount} />

        <input
          type="hidden"
          name="firstname"
          id="firstname"
          value={pay.firstname}
        />
        <input type="hidden" name="email" id="email" value={pay.email} />
        <input type="hidden" name="productinfo" value={pay.productinfo} />
        <input type="hidden" name="surl" value={pay.surl} size="64" />
        <input type="hidden" name="furl" value={pay.furl} size="64" />
        <input
          type="hidden"
          name="service_provider"
          value="payu_paisa"
          size="64"
        />
        <div className="main-table-container paynowcontainer">
          {amount > 0 ? (
            <p></p>
          ) : (
            <Button
              variant="contained"
              onClick={this.activateFreePackage}
              className="btn header-btns packagebtn buy buypack"
            >
              Pay {"₹ " + amount}
            </Button>
          )}
        </div>
      </form>
    );
  };

  onInstaBuyNowClick = (app = true) => {
    const {
      userData,
      amount,
      groupDetails,
      finalWalletBal,
      validity_no_of_days,
    } = this.state;
    const { match } = this.props;
    const { params } = match;
    const { gid } = params;
    const expiryDate = new Date();

    // Set expiry based on coupon validity if it exists and is greater than 0
    const expiry =
      validity_no_of_days > 0
        ? validity_no_of_days
        : groupDetails.valid_for_days !== 0
        ? parseInt(groupDetails.valid_for_days)
        : 365;

    expiryDate.setDate(expiryDate.getDate() + expiry);
    const token = Cookie.get("jwt_token");
    const phoneNum = localStorage.getItem("userDetailsApp") || this.isWebView();
    const pay = {
      purpose: "Purchase of " + groupDetails.group_name + " Package",
      email: userData.email,
      firstname: userData.first_name.split(" ")[0],
      txnid: uuidv4(),
      phone: userData.contact_no,
      amount: amount,
      redirect_url: `${commonData["api"]}${
        phoneNum ? "/payment_instamojo" : "/phonepe/status"
      }?user_id=${userData.contact_no}&gid=${gid}&firstname=${
        userData.first_name.split(" ")[0]
      }&net_amount_debit=${amount}&expiryDate=${expiryDate
        .toISOString()
        .slice(0, 10)}&fbw=${finalWalletBal}&CouponCode=${this.state.coupon}`,
    };
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    axios
      .post(
        `${commonData["api"]}/${
          phoneNum ? "instamojo/pay" : "phonepe/payment"
        }`,
        pay,
        { headers }
      )
      .then(async (res) => {
        this.setState({ isLoading: true });
        axios.post(
          `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
          {
            businessId: 1,
            verifyToken: Math.random() * 15000,
            phoneNumber: "9441687174",
            message: [
              pay.firstname + "-" + pay.phone,
              gid + "-" + groupDetails.group_name,
              commonData["app"] + "/admin/users?userNum=" + pay.phone,
            ],
            messageType: "promotion",
            templateLang: "en",
            templateName: "user_payment_process_started_admin_notification",
          }
        );
        axios.post(
          `https://phpstack-702151-4218790.cloudwaysapps.com/send-message`,
          {
            businessId: 1,
            verifyToken: Math.random() * 15000,
            phoneNumber: "9492387460",
            message: [
              pay.firstname + "-" + pay.phone,
              gid + "-" + groupDetails.group_name,
              commonData["app"] + "/admin/users?userNum=" + pay.phone,
            ],
            messageType: "promotion",
            templateLang: "en",
            templateName: "user_payment_process_started_admin_notification",
          }
        );
        window.location.href = res.data;
      })
      .catch((error) => console.log(error.response.data));
  };

  renderInstamojoButton = () => {
    const { amount, validity_no_of_days, groupDetails } = this.state;

    // Calculate expiry date to display
    const expiryDate = new Date();
    const expiry =
      validity_no_of_days > 0
        ? validity_no_of_days
        : groupDetails.valid_for_days !== 0
        ? parseInt(groupDetails.valid_for_days)
        : 365;
    expiryDate.setDate(expiryDate.getDate() + expiry);

    // Format date as DD-MM-YYYY
    const formattedDate = expiryDate
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, "-");

    return (
      <div
        className="main-table-container paynowcontainer"
        style={{
          display: "flex",
          flexDirection: "column",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        {/* {validity_no_of_days > 0 && (
          <div
            style={{
              color: "white",
              textAlign: "center",
              marginBottom: "10px",
            }}
          >
            <p>
              Package valid until: <strong>{formattedDate}</strong>
            </p>
          </div>
        )} */}
        {amount > 0 ? (
          <div>
            <div style={{ color: "white" }}>
              <Button
                variant="contained"
                onClick={this.onInstaBuyNowClick}
                className="btn header-btns packagebtn buy buypack"
                style={{ margin: "10px 0" }}
              >
                Pay {"₹ " + amount}
              </Button>
            </div>
          </div>
        ) : (
          <div>
            <Button
              variant="contained"
              onClick={this.activateFreePackage}
              className="btn header-btns packagebtn buy buypack"
              style={{ margin: "10px 0" }}
            >
              Pay {"₹ " + amount}
            </Button>
          </div>
        )}
      </div>
    );
  };

  applyCoupon = async (e) => {
    e.preventDefault();
    const { match } = this.props;
    const { params } = match;
    const { gid } = params;
    const { coupon, groupDetails } = this.state;
    if (coupon.length >= 5) {
      try {
        const couponDetails = await axios.post(
          `${commonData["api"]}/get-discount-coupon-details/${coupon}/${gid}`
        );
        console.log(
          "🚀 ~ PayuMoney ~ applyCoupon= ~ couponDetails:",
          couponDetails
        );

        this.setState({
          err: "",
          coupon: "",
          couponPercent: couponDetails.data[0].amount,
          amount: (
            groupDetails.price -
            (couponDetails.data[0].amount / 100) * groupDetails.price
          ).toFixed(2),
          validity_no_of_days: couponDetails.data[0].no_of_days_validity,
        });
      } catch (er) {
        this.setState({
          err: "Enter Valid Coupon Code",
          amount: groupDetails.price,
        });
      }
    } else {
      this.setState({
        err: "Enter Valid Coupon Code",
        amount: groupDetails.price,
      });
    }
  };

  useWalletAmount = async (e) => {
    const { userData, amount, useWallet } = this.state;
    if (!useWallet) {
      let finalAmount =
        amount - userData.userAmount > 0 ? amount - userData.userAmount : 0;
      this.setState({
        err: "",
        amount: finalAmount.toFixed(2),
        finalWalletBal: amount - finalAmount,
        useWallet: true,
      });
    }
  };

  render() {
    const {
      isLoading,
      groupDetails,
      amount,
      coupon,
      err,
      couponPercent,
      paid,
      userData,
      useWallet,
      finalWalletBal,
      validity_no_of_days,
    } = this.state;

    // Calculate expiry date to display in the table
    const expiryDate = new Date();
    const expiry =
      validity_no_of_days > 0
        ? validity_no_of_days
        : groupDetails.valid_for_days !== 0
        ? parseInt(groupDetails.valid_for_days)
        : 365;
    expiryDate.setDate(expiryDate.getDate() + expiry);

    // Format date as DD-MM-YYYY
    const formattedDate = expiryDate
      .toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      })
      .replace(/\//g, "-");

    return (
      <>
        {!isLoading && !paid && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetails">
                <DesktopMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop22">
                <div className="title-quiz">
                  <p className="homepage-package-title examlist-ttle">
                    Payment Information
                  </p>
                </div>
                <div className="main-table-container">
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="tablehead">Package Name</th>
                        <th className="tablehead">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td className="tabledata">
                          <p>{groupDetails.group_name}</p>
                        </td>
                        <td className="tabledata">
                          <p>{"₹ " + groupDetails.price}</p>
                        </td>
                      </tr>
                      {groupDetails.isNormalPackage === "1" && (
                        <tr>
                          <th className="tabledata" colSpan={2}>
                            <Button
                              variant="contained"
                              className="btn header-btns attemptbtn examschedulelink2"
                            >
                              <a
                                href={groupDetails.scheduleLink}
                                target="_blank"
                                className="linkto linksche2"
                                style={{ color: "#fff" }}
                              >
                                Download Exam Schedule {"&"} Syllabus
                              </a>
                            </Button>
                          </th>
                        </tr>
                      )}

                      <tr style={{ padding: 10 }}>
                        <th colSpan={2} style={{ background: "white" }}>
                          Package Information{" "}
                          {this.isWebView() ? "App " : "Website"}
                          <div
                            style={{ background: "white" }}
                            dangerouslySetInnerHTML={{
                              __html: groupDetails.description,
                            }}
                          ></div>
                        </th>
                      </tr>
                      <tr style={{ padding: 10 }}>
                        <th colSpan={2} style={{ color: "white" }}>
                          Student Information
                          <p>
                            {userData.contact_no}, {userData.first_name}
                          </p>
                        </th>
                      </tr>

                      {amount > 0 && (
                        <>
                          <tr>
                            <td className="tabledata" colSpan={2}>
                              <form
                                className="paynowcontainer"
                                onSubmit={this.applyCoupon}
                              >
                                <TextField
                                  required
                                  className="input-box"
                                  style={{ width: "60%" }}
                                  label="Enter COUPON Name"
                                  variant="filled"
                                  value={coupon}
                                  onChange={(e) =>
                                    this.setState({
                                      coupon: e.target.value,
                                      err: "",
                                    })
                                  }
                                />

                                <div className="applyTextCoupondiv">
                                  <Button
                                    variant="contained"
                                    type="submit"
                                    className="btn header-btns packagebtn applyCoupon"
                                  >
                                    APPLY DISCOUNT
                                  </Button>
                                </div>
                              </form>
                              {err && (
                                <p
                                  className="otp-mesage-text"
                                  style={{ color: "red", textAlign: "center" }}
                                >
                                  Error : {err}
                                </p>
                              )}
                            </td>
                          </tr>

                          <tr>
                            <td colSpan={2}>
                              <div
                                style={{
                                  display: "flex",
                                  paddingTop: 25,
                                  paddingBottom: 20,
                                  justifyContent: "center",
                                }}
                              >
                                <div
                                  style={{
                                    color: "white",
                                    fontSize: 15,
                                    textAlign: "center",
                                    marginRight: 20,
                                  }}
                                >
                                  {"Your Wallet Balance : ₹ " +
                                    userData.userAmount}
                                </div>
                                <div className="applyTextCoupondiv">
                                  <Button
                                    variant="contained"
                                    onClick={this.useWalletAmount}
                                    className="btn header-btns packagebtn applyCoupon"
                                    disabled={
                                      useWallet || userData.userAmount <= 0
                                    }
                                  >
                                    Use Wallet Balance
                                  </Button>
                                </div>
                              </div>
                            </td>
                          </tr>
                        </>
                      )}
                      {groupDetails.price !== amount && (
                        <tr>
                          <td className="tabledata" colSpan={2}>
                            {useWallet ? (
                              <p className="apply-coupon-text">
                                Wallet Amount used Successfully...
                              </p>
                            ) : (
                              <p className="apply-coupon-text">
                                {couponPercent}% Discount Coupon Applied
                                Successfully...
                              </p>
                            )}
                            <p className="apply-coupon-text">
                              {"Latest Price of Package : ₹ " + amount}
                            </p>
                            {!useWallet && validity_no_of_days > 0 && (
                              <p
                                className="apply-coupon-text"
                                style={{ color: "black" }}
                              >
                                Package Valid Until:{" "}
                                <strong>{formattedDate}</strong>
                              </p>
                            )}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
                {this.renderInstamojoButton()}
              </div>
            </div>
          </>
        )}
        {!isLoading && paid && <Redirect to={`/`} />}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {}
      </>
    );
  }
}

export default PayuMoney;
