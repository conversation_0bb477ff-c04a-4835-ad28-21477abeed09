<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Web site created using create-react-app"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Ramabhadra&display=swap"
      rel="stylesheet"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="manifest" href="/manifest.json" />
    <title>NavaChaitanya Competetion Online Exams portal</title>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script>
      !(function (t, e) {
        var o, n, p, r;
        e.__SV ||
          ((window.posthog = e),
          (e._i = []),
          (e.init = function (i, s, a) {
            function g(t, e) {
              var o = e.split(".");
              2 == o.length && ((t = t[o[0]]), (e = o[1])),
                (t[e] = function () {
                  t.push([e].concat(Array.prototype.slice.call(arguments, 0)));
                });
            }
            ((p = t.createElement("script")).type = "text/javascript"),
              (p.async = !0),
              (p.src =
                s.api_host.replace(".i.posthog.com", "-assets.i.posthog.com") +
                "/static/array.js"),
              (r = t.getElementsByTagName("script")[0]).parentNode.insertBefore(
                p,
                r
              );
            var u = e;
            for (
              void 0 !== a ? (u = e[a] = []) : (a = "posthog"),
                u.people = u.people || [],
                u.toString = function (t) {
                  var e = "posthog";
                  return (
                    "posthog" !== a && (e += "." + a), t || (e += " (stub)"), e
                  );
                },
                u.people.toString = function () {
                  return u.toString(1) + ".people (stub)";
                },
                o =
                  "init push capture register register_once register_for_session unregister unregister_for_session getFeatureFlag getFeatureFlagPayload isFeatureEnabled reloadFeatureFlags updateEarlyAccessFeatureEnrollment getEarlyAccessFeatures on onFeatureFlags onSessionId getSurveys getActiveMatchingSurveys renderSurvey canRenderSurvey getNextSurveyStep identify setPersonProperties group resetGroups setPersonPropertiesForFlags resetPersonPropertiesForFlags setGroupPropertiesForFlags resetGroupPropertiesForFlags reset get_distinct_id getGroups get_session_id get_session_replay_url alias set_config startSessionRecording stopSessionRecording sessionRecordingStarted loadToolbar get_property getSessionProperty createPersonProfile opt_in_capturing opt_out_capturing has_opted_in_capturing has_opted_out_capturing clear_opt_in_out_capturing debug".split(
                    " "
                  ),
                n = 0;
              n < o.length;
              n++
            )
              g(u, o[n]);
            e._i.push([i, s, a]);
          }),
          (e.__SV = 1));
      })(document, window.posthog || []);
      posthog.init("phc_jczBXXKqHE1zQAOWXv0S0ht3pTDuDVbz4j3HePd0UQ1", {
        api_host: "https://us.i.posthog.com",
        person_profiles: "identified_only", // or 'always' to create profiles for anonymous users as well
      });
    </script>
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=UA-217887901-1"
    ></script>
    <script
      src="https://kit.fontawesome.com/7813a5ae25.js"
      crossorigin="anonymous"
    ></script>
    <script
      src="https://cdn.tiny.cloud/1/5o91t0d8pct3d0g026r36v2vgusedk7vuq1juc9wsrbw29uq/tinymce/7/tinymce.min.js"
      referrerpolicy="origin"
    ></script>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css"
    />
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());

      gtag("config", "UA-217887901-1");

      try {
        const userData = RoboTemplatesWebViewApp.getUserInfo();
        const oneSignalId = RoboTemplatesWebViewApp.getOneSignalUserId();
        const userName = localStorage.getItem("name");
        const num = localStorage.getItem("num");
        if (num) {
          posthog.identify(num, {
            name: userName,
            phone: num,
          });
        }
        localStorage.setItem("userDetailsApp", userData);
        localStorage.setItem("oneSignalId", oneSignalId);
        // document.cookie =
        //   "jwt_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
      } catch (error) {
        console.log("🚀 ~ error:", error);
        localStorage.setItem("userDetailsApp", "");
        localStorage.setItem("oneSignalId", "");
      }
    </script>

    <!--End of Tawk.to Script-->
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <div id="root"></div>
    
    <script type="module" src="/src/index.js"></script>
  </body>
  <script src="https://unpkg.com/jspdf@latest/dist/jspdf.umd.min.js"></script>
</html>
