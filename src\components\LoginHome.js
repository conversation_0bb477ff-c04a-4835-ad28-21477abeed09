import react from "react";
import axios from "axios";
import Login from "./Login";
import Header from "./Header";
import Loader from "./Loader";
import Divider from "@mui/material/Divider";
import <PERSON>ie from "js-cookie";
import DesktopMenu from "./DesktopMenu";
import Packages from "./Packages";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";

import commonData from "../importanValue";

class LoginHome extends react.Component {
  state = { isLoading: true, packages: [] };

  componentDidMount() {
    const token = Cookie.get("jwt_token");
    if (token === undefined) {
      NotificationManager.error("Please Login/Register First");
    }
    this.getPackagesList();
  }

  getPackagesList = async () => {
    const response = await axios.get(`${commonData["api"]}/get-packages-list`);
    const token = Cookie.get("jwt_token");
    const uid = localStorage.getItem("uid");
    this.setState({ packages: response.data, isLoading: false });
  };

  render() {
    const { isLoading, packages } = this.state;
    const token = Cookie.get("jwt_token");

    return (
      <>
        {!isLoading && (
          <>
            <div className="desktopsidebar">
              <div className="homedesktopsidebarmenuexamdetails">
                <DesktopMenu />
              </div>
              <Header />

              <Divider color="white" className="dividers" />
              <div className="homedesktop">
                {token === undefined ? <Login /> : null}

                <Divider color="white" className="dividers" />
                <p className="homepage-package-title">Available Packages</p>
                <div className="homepacksdesk">
                  {packages.map((each, i) => (
                    <Packages data={each} key={"packagename" + i} />
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default LoginHome;
