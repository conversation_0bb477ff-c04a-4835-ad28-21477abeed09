import {
  But<PERSON>,
  Checkbox,
  Divider,
  ListItemText,
  MenuItem,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import Header from "../Header";
import AdminMenu from "./AdminMenu";
import Select from "@mui/material/Select";
import axios from "axios";
import commonData from "../../importanValue";
import Cookies from "js-cookie";
import { ExcelRenderer } from "react-excel-renderer";

import {
  NotificationContainer,
  NotificationManager,
} from "react-notifications";
import { useLocation } from "react-router-dom/cjs/react-router-dom.min";
const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 400,
    },
  },
};
const MessageComponent = () => {
  const [message, setMessage] = useState("");
  const [error, setErr] = useState("");
  const [numbers, setNumbers] = useState("");
  const [message1, setMessage1] = useState("");
  const [message2, setMessage2] = useState("");
  const [rows, setRows] = useState([]);
  const [allMessages, setMessages] = useState([]);
  const [allMessages2, setMessages2] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showSentMessages, setShowMessages] = useState(false);
  const [cols, setCols] = useState([]);
  const [packagesAllList, setPackages] = useState([]);
  const [selectedUser, setSelectedUsers] = useState([]);
  const [selectedPackageIds, setSelectedPackageIds] = useState([]);
  const [selectedValue, setSelectedValue] = useState("en");
  const [selectedTypeValue, setSelectedTypeValue] = useState("none");
  const [storefilehandlerkey, setkey] = useState("");
  const location = useLocation();

  const handleTypeChange = (event) => {
    setSelectedTypeValue(event.target.value);
  };
  // Function to handle changes in the dropdown selection
  const handleSelectionChange = (event) => {
    setSelectedValue(event.target.value);
  };
  const handleMessageChange = (event) => {
    setMessage(event.target.value);
  };
  const handleMessageChange1 = (event) => {
    setMessage1(event.target.value);
  };
  const handleMessageChange2 = (event) => {
    setMessage2(event.target.value);
  };
  const handleNumberChange2 = (event) => {
    setNumbers(event.target.value.trim());
  };

  useEffect(() => {
    const nextUrl = location.search.split("?type=")[1];
    console.log("🚀 ~ useEffect ~ nextUrl:", nextUrl);

    const fetchData = async () => {
      try {
        const token = Cookies.get("jwt_token");
        const headers = {
          "Content-Type": "application/json",
          authorization: token,
          "Access-Control-Allow-Origin": "*",
        };

        const response = await axios.get(
          `${commonData.api}/adminmasterdata/packagelist`,
          {
            headers,
          }
        );

        // Assuming the API response contains an array of packages
        setPackages(response.data[0]);
      } catch (error) {
        console.error("Error fetching package list:", error);
      }
    };
    if (nextUrl === "show") {
      viewMessages();
      setShowMessages(true);
      return;
    }
    fetchData();
    setShowMessages(false);
  }, []);

  const viewMessages = async () => {
    try {
      const response = await axios.get(
        "https://phpstack-702151-4218790.cloudwaysapps.com/get-messages"
      );
      console.log("🚀 ~ viewMessages ~ response:", response);

      // Assuming the API response contains an array of packages
      setMessages2(response.data[0]);
      setMessages(response.data[1]);
    } catch (error) {
      console.error("Error fetching package list:", error);
    }
  };

  const handleSendClick = async () => {
    console.log("Message:", message);
    console.log("Selected Package IDs:", selectedPackageIds);
    setIsLoading(true);
    setSelectedUsers([]);
    if (selectedPackageIds.length > 0) {
      const body3 = {
        type: "GETALLUsersBasedOnGid",
        search: selectedPackageIds.join(","),
        qid: 0,
      };
      const token = Cookies.get("jwt_token");
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
        "Access-Control-Allow-Origin": "*",
      };

      const GETALLCOUNT = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body3,
        {
          headers,
        }
      );
      if (GETALLCOUNT) {
        const allUser = GETALLCOUNT.data[0];
        setSelectedUsers(allUser);
      }
    } else if (rows.length > 0) {
      const allUsers = [];
      for (let index = 0; index < rows.length; index++) {
        const element = rows[index];
        const val = element[cols.indexOf("contact_no")];
        if (val && String(val).trim() !== "") {
          allUsers.push({ contact_no: String(val).trim() });
        }
      }
      setSelectedUsers(allUsers.filter((e) => e && String(e).trim() !== ""));
    } else if (String(numbers).trim().length > 0) {
      const allUsersInfo = [];
      let error = "";
      numbers.split(",").forEach((e) => {
        if (e && String(e).trim().length > 0 && e.trim().length === 10) {
          allUsersInfo.push({
            contact_no: e,
          });
        } else {
          error = e + "," + error;
        }
      });
      if (error) {
        setErr("Error in this numbers or check extra commas >> " + error);
        NotificationManager.error("Rectify the issue to move forward.");
        setIsLoading(false);
        setSelectedUsers([]);
        return;
      }
      setErr("");
      setSelectedUsers(allUsersInfo);
    } else {
      //   if (!message || !message1 || !message2)
      //     NotificationManager.error("Please enter the message.");
      if (
        selectedPackageIds.length === 0 &&
        selectedUser.length === 0 &&
        rows.length === 0 &&
        numbers.trim().length === 0
      ) {
        NotificationManager.error(
          "Please select any group/Import through Excel/Add Numbers"
        );
      }

      //   if (selectedUser.length === 0)
      //     NotificationManager.error("Please select atleast one user");
    }
    setIsLoading(false);
    // Add additional logic for sending the message
  };
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

  const sendMessage = async () => {
    try {
      if (
        message &&
        message1 &&
        message2 &&
        (selectedPackageIds.length > 0 ||
          selectedUser.length > 0 ||
          numbers.trim().length > 0)
      ) {
        if (selectedTypeValue !== "none") {
          if (storefilehandlerkey === "") {
            NotificationManager.error("Please upload the image/doc.");
            return;
          }
        }
        // for (const each of selectedUser) {
        await axios.post(`${commonData["msgApi"]}`, {
          businessId: 1,
          verifyToken: Math.random() * 15000,
          phoneNumber: selectedUser,
          message: [message, message1, message2],
          messageType: "promotion",
          templateLang: selectedValue,
          body: "multiple",
          format: selectedTypeValue,
          fileKey: storefilehandlerkey,
        });
        // await delay(100000);
        // }
        NotificationManager.success("Messages are added in the Queue.");
        viewMessages();
      } else {
        if (!message || !message1 || !message2)
          NotificationManager.error("Please enter the message.");
        if (
          selectedPackageIds.length === 0 &&
          selectedUser.length === 0 &&
          rows.length === 0 &&
          numbers.trim().length === 0
        )
          NotificationManager.error(
            "Please select any group/Import through Excel/add phone numbers"
          );

        if (selectedUser.length === 0)
          NotificationManager.error("Please select atleast one user");
      }
    } catch (error) {
      NotificationManager.error("Something Went Wrong");
    }
    // Add additional logic for sending the message
  };

  const onChangePackageData = (e) => {
    setSelectedPackageIds(
      typeof value === "string" ? e.target.value.split(",") : e.target.value
    );
  };

  const fileHandler = (event) => {
    let fileObj = event.target.files[0];
    //just pass the fileObj as parameter
    // console.log(fileObj.name.split(".xlsx"));
    ExcelRenderer(fileObj, (err, resp) => {
      if (err) {
        NotificationManager.error("Please upload proper file");
        console.log(err);
      } else {
        setCols(resp.rows.slice(0, 1)[0]);
        setRows(resp.rows.slice(1, resp.rows.length));
        NotificationManager.success("File Added");
      }
    });
  };

  const handleFileChange = (e) => {
    e.preventDefault();
    const file = e.target.files && e.target.files[0];
    console.log("🚀 ~ handleFileChange ~ file:", file);
    if (file) {
      uploadImage(file, file.type, file.size);
    }
  };

  const uploadImage = async (fileData, fileType, fileLength) => {
    console.log("🚀 ~ uploadImage ~ fileData:", fileData);
    try {
      const formData = new FormData();
      formData.append("file", fileData);
      console.log(formData);

      const token = Cookies.get("jwt_token");
      const headers = {
        "Content-Type": "application/json",
        authorization: token,
        "Access-Control-Allow-Origin": "*",
        linkfile: fileData.name,
        question: "whatsapp",
        fileType,
        fileLength,
      };
      const data = await axios.post(
        `${commonData["api"]}/report-upload-image`,
        formData,
        {
          headers,
        }
      );
      console.log(data.data);

      setkey(
        "https://examsapi.navachaitanya.net/reported/whatsapp/" + fileData.name
      );
    } catch (err) {}
  };
  return (
    <div className="desktopsidebar">
      <div className="desktopsidebarmenuexamdetailsAdmin">
        <AdminMenu />
        <div>
          <NotificationContainer />
        </div>
      </div>
      <Header />

      <Divider color="white" />

      <div className="viewresultsdesktop admin">
        <div style={styles.container}>
          {!showSentMessages && (
            <>
              <h3>Send Whatsapp messages to Users</h3>
              <div style={styles.container}>
                <div style={{ display: "flex" }}>
                  <label
                    htmlFor="dropdown2"
                    style={{
                      fontSize: "16px",
                      marginBottom: "8px",
                      display: "block",
                    }}
                  >
                    Select Header type:
                  </label>
                  <select
                    id="dropdown2"
                    value={selectedTypeValue}
                    onChange={handleTypeChange}
                    style={{
                      padding: "8px",
                      fontSize: "14px",
                      borderRadius: "4px",
                      border: "1px solid #ccc",
                      width: "200px",
                      marginBottom: "16px",
                    }}
                  >
                    <option value="none">None</option>
                    <option value="pdf">PDF</option>
                    <option value="image">Image</option>
                  </select>
                  {selectedTypeValue !== "none" && (
                    <div style={{ marginLeft: 50 }}>
                      <input
                        type="file"
                        accept={
                          selectedTypeValue === "pdf"
                            ? "application/pdf"
                            : "image/*"
                        }
                        onChange={handleFileChange}
                      />
                    </div>
                  )}
                </div>
                <label style={styles.label}>
                  Message Line 1:
                  <input
                    type="text"
                    value={message}
                    onChange={handleMessageChange}
                    style={styles.input}
                  />
                </label>
                <label style={styles.label}>
                  Message Line 2:
                  <input
                    type="text"
                    value={message1}
                    onChange={handleMessageChange1}
                    style={styles.input}
                  />
                </label>
                <label style={styles.label}>
                  Message Line 3:
                  <input
                    type="text"
                    value={message2}
                    onChange={handleMessageChange2}
                    style={styles.input}
                  />
                </label>
                <label
                  htmlFor="dropdown"
                  style={{
                    fontSize: "16px",
                    marginBottom: "8px",
                    display: "block",
                  }}
                >
                  Select Language of Message:
                </label>
                <select
                  id="dropdown"
                  value={selectedValue}
                  onChange={handleSelectionChange}
                  style={{
                    padding: "8px",
                    fontSize: "14px",
                    borderRadius: "4px",
                    border: "1px solid #ccc",
                    width: "200px",
                    marginBottom: "16px",
                  }}
                >
                  <option value="te">Telugu</option>
                  <option value="en">English</option>
                </select>
                <div
                  style={{
                    marginTop: 10,
                    border: "1px solid green",
                    padding: 5,
                    margin: 10,
                  }}
                >
                  <label style={styles.label}>Get users from Package or</label>
                  <div>
                    <Select
                      required
                      id="Please-Select-District"
                      className="input-box dis"
                      // label="Please Select Package"
                      style={{ width: 500 }}
                      value={selectedPackageIds}
                      name="selectedPackages"
                      multiple
                      displayEmpty
                      onChange={onChangePackageData}
                      renderValue={(selectedPackages) => {
                        if (selectedPackages.length === 0) {
                          return <em>Please Select Package</em>;
                        }

                        return selectedPackages.join(", ");
                      }}
                      MenuProps={MenuProps}
                    >
                      <MenuItem
                        disabled
                        value=""
                        className="attempt-option-select"
                      >
                        Please Select Package
                      </MenuItem>
                      <MenuItem
                        className="selectionbox"
                        value={"All Users"}
                        key={"multiselect All Users"}
                      >
                        <Checkbox
                          checked={selectedPackageIds.indexOf("All Users") > -1}
                        />
                        <ListItemText primary={"All Users"} />
                      </MenuItem>
                      {packagesAllList
                        .filter((e) => e?.price > 0)
                        .map((e, i) => (
                          <MenuItem
                            className="selectionbox"
                            value={e.gid}
                            key={"multiselect" + i}
                          >
                            <Checkbox
                              checked={selectedPackageIds.indexOf(e.gid) > -1}
                            />
                            <ListItemText primary={e.group_name} />
                          </MenuItem>
                        ))}
                    </Select>
                  </div>
                  <div style={{ marginTop: 20 }}>
                    <label style={styles.label}>Import from Excel</label>
                    <div>
                      <input
                        type="file"
                        onChange={fileHandler}
                        style={{ padding: "10px" }}
                      />
                    </div>
                  </div>
                  <a
                    href="https://docs.google.com/spreadsheets/d/1DD5USXWri1A6T0qfdMYICUQdxYTIpCKaNAooNXoatD0/edit?usp=sharing"
                    target="_blank"
                  >
                    Demo Excel file
                  </a>
                  <br />
                  <br />
                  <p>
                    Or add Users numbers in comma separated
                    <br /> example: 7396019228,8464011520
                  </p>
                  <div style={{ width: "80%" }}>
                    <label style={styles.label}>
                      Add phoneNumbers
                      <textarea
                        type="text"
                        value={numbers}
                        onChange={handleNumberChange2}
                        style={styles.input}
                      />
                    </label>
                  </div>
                  <p style={{ color: "red" }}>{error}</p>
                  <div style={{ marginTop: 20 }}>
                    <button onClick={handleSendClick} style={styles.button}>
                      Get Users
                    </button>
                    <span style={{ marginLeft: 10 }}>
                      {isLoading
                        ? "Fetching the Users Info"
                        : `(${selectedUser.length}) Users are available`}
                    </span>
                  </div>
                </div>

                {/* <div>Selected Users ({selectedUser.length})</div> */}
                {selectedUser.length > 0 && (
                  <div>
                    {" "}
                    <Button
                      variant="contained"
                      className="btn"
                      style={{ marginTop: 40 }}
                      onClick={sendMessage}
                    >
                      Send Message to ({selectedUser.length}) Users
                    </Button>
                  </div>
                )}
              </div>
            </>
          )}
          {showSentMessages && (
            <>
              <h3>All sent Messages </h3>
              <div style={styles.container}>
                {allMessages.length > 0 && (
                  <>
                    <style>
                      {" "}
                      {`/* Add some styling to the table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

/* Style the table headers */
th {
  background-color: #f2f2f2;
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

/* Style the table cells */
td {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

/* Add some hover effect to the table rows */
tr:hover {
  background-color: #f5f5f5;
}
`}
                    </style>
                    <table>
                      <thead>
                        <tr>
                          {/* Dynamically create headers based on object properties */}
                          {Object.keys(allMessages2[0]).map((header) => (
                            <th key={header}>{header}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {allMessages2.map((item) => (
                          <tr key={item.msgId}>
                            {/* Render each object property as a table cell */}
                            {Object.values(item).map((value) => (
                              <td key={value}>{value}</td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </>
                )}
                {allMessages.length > 0 && (
                  <>
                    <style>
                      {" "}
                      {`/* Add some styling to the table */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

/* Style the table headers */
th {
  background-color: #f2f2f2;
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid #ddd;
}

/* Style the table cells */
td {
  padding: 8px;
  border-bottom: 1px solid #ddd;
}

/* Add some hover effect to the table rows */
tr:hover {
  background-color: #f5f5f5;
}
`}
                    </style>
                    <table>
                      <thead>
                        <tr>
                          {/* Dynamically create headers based on object properties */}
                          {Object.keys(allMessages[0]).map((header) => (
                            <th key={header}>{header}</th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {allMessages.map((item) => (
                          <tr key={item.msgId}>
                            {/* Render each object property as a table cell */}
                            {Object.values(item).map((value) => (
                              <td key={value}>{value}</td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </>
                )}
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

const styles = {
  container: {
    // maxWidth: "800px",
    margin: "20px auto",
    padding: "20px",
    border: "1px solid #ddd",
    borderRadius: "8px",
    boxShadow: "0 4px 8px rgba(0, 0, 0, 0.1)",
    backgroundColor: "#fff",
  },
  label: {
    display: "block",
    marginBottom: "10px",
  },
  input: {
    width: "100%",
    padding: "8px",
    fontSize: "16px",
    border: "1px solid #ddd",
    borderRadius: "4px",
  },
  select: {
    width: "100%",
    padding: "8px",
    fontSize: "16px",
    border: "1px solid #ddd",
    borderRadius: "4px",
    height: "100px", // Adjust the height as needed
  },
  button: {
    backgroundColor: "#4CAF50",
    color: "white",
    padding: "10px 15px",
    fontSize: "16px",
    border: "none",
    borderRadius: "4px",
    cursor: "pointer",
  },
};

export default MessageComponent;
