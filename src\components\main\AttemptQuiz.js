import axios from "axios";
import React, { Component } from "react";
import commonData from "../../importanValue";
import <PERSON><PERSON> from "js-cookie";
import "./styles.css";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import Button from "@mui/material/Button";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import Loader from "../Loader";
class AttemptQuiz extends Component {
  constructor() {
    super();
    this.state = {
      answers: {},
      qNum: 0,
      sec: 0,
      starttime: new Date(),
      disabledback: false,
      disabledSave: false,
      quizDetails: [],
      questions: [],
      isLoading: true,
      categories: [],
      setCategory: "",
      dialogOpen: false,
      saveStyle: "save",
      phone: "",
      nightMode: false,
      allowNegativeMarking: false,
      englishMedium: false, // Initialize as false, will be set in getData if available
      showAlert: true,
      previousStatus: false,
      only_for_practice: false,
      practiceScore: 0,
      answeredQuestions: {}, // Track which questions have been answered and if they're correct
      resumeDialogOpen: false, // Dialog to ask if user wants to resume from last position
      savedQuestionNum: null, // The saved question number to resume from
      exitPracticeDialogOpen: false, // Dialog to confirm exit from practice exam
    };
    this.alertUser = this.alertUser.bind(this);
  }

  componentDidMount() {
    // console.log(window.innerWidth);
    this.getData();
    // const answers = localStorage.getItem("stora");
    /*if (answers !== null) {
      this.state = JSON.parse(answers);
    }*/
    window.addEventListener("beforeunload", this.alertUser);
    // window.addEventListener("focus", this.alertUser);
    // window.fullScreen = true;
    document.addEventListener("contextmenu", (event) => {
      event.preventDefault();
    });

    // console.log(window);
    // window.removeEventListener("beforeunload", this.alertUser);
  }

  // Save the last question ID to localStorage for practice mode
  saveLastQuestionPosition = (questionNum) => {
    const { match } = this.props;
    const { params } = match;
    const { quid: link } = params;
    const quid = link.split("$$$_")[0];

    // Create a key specific to this quiz
    const storageKey = `practice_last_question_${quid}`;

    // Save the question number
    localStorage.setItem(storageKey, questionNum.toString());
  };

  // Handle resume dialog response
  handleResumeResponse = (resume) => {
    if (resume && this.state.savedQuestionNum !== null) {
      // Resume from saved position
      this.setState({
        qNum: this.state.savedQuestionNum,
        resumeDialogOpen: false,
      });
    } else {
      // Start from beginning
      this.setState({
        resumeDialogOpen: false,
      });

      // Clear the saved position
      const { match } = this.props;
      const { params } = match;
      const { quid: link } = params;
      const quid = link.split("$$$_")[0];
      const storageKey = `practice_last_question_${quid}`;
      localStorage.removeItem(storageKey);
    }
  };

  // Render resume dialog
  renderResumeDialog = () => {
    const { resumeDialogOpen, savedQuestionNum } = this.state;

    if (!resumeDialogOpen) return null;

    return (
      <div className="modal" style={{ display: "flex" }}>
        <div
          className="modal-content"
          style={{ width: "90%", maxWidth: "500px" }}
        >
          <div className="modal-header">
            <h3>Resume Practice Session</h3>
          </div>
          <div className="modal-body">
            <p>
              You have a saved practice session at question{" "}
              {savedQuestionNum + 1}.
            </p>
            <p>
              Would you like to resume from where you left off or start a new
              session?
            </p>
          </div>
          <div
            className="modal-footer"
            style={{ display: "flex", justifyContent: "space-between" }}
          >
            <button
              className="btn btn-secondary"
              onClick={() => this.handleResumeResponse(false)}
              style={{
                padding: "8px 16px",
                backgroundColor: "#F44336" /* Using existing red color */,
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
              }}
            >
              Start New
            </button>
            <button
              className="btn btn-primary"
              onClick={() => this.handleResumeResponse(true)}
              style={{
                padding: "8px 16px",
                backgroundColor: "#FFC107" /* Using existing yellow color */,
                color: "black",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontWeight: "bold",
              }}
            >
              Resume
            </button>
          </div>
        </div>
      </div>
    );
  };

  alertUser = async (e) => {
    e.preventDefault();
    if (this.state.only_for_practice) {
      return;
    }
    await this.setState({ dialogOpen: true });
    // alert("Are you sure want to Leave");
    e.returnValue = "";
    // window.confirm("Are you sure you want to leave?");
    // e.returnValue = "Are you sure you want to leave?";
  };

  closeFullscreen = () => {
    // var elem = document.documentElement;
    // try {
    //   if (document.exitFullscreen) {
    //     document.exitFullscreen();
    //   } else if (document.mozCancelFullScreen) {
    //     document.mozCancelFullScreen();
    //   } else if (document.webkitExitFullscreen) {
    //     document.webkitExitFullscreen();
    //   } else if (document.msExitFullscreen) {
    //     window.top.document.msExitFullscreen();
    //   }
    // } catch (error) {}
  };

  getData = async () => {
    const { match } = this.props;
    const { params } = match;
    const { quid: link } = params;
    const quid = link.split("$$$_")[0];
    const allowNegativeMarking = window.atob(link.split("$$$_")[1]) === "true";
    const only_for_practice = window.atob(link.split("$$$_")[2]) === "true";
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const quizList = await axios.get(
      `${commonData["api"]}/quiz_details/${quid}`,
      { headers }
    );
    // console.log(quizList);

    // Check if English medium questions are available
    const enable_english_medium = localStorage.getItem("enable_english_medium");
    const firstQuestion = quizList.data[1][0]; // Get first question to check
    const hasEnglishQuestions =
      firstQuestion?.question_in_english && firstQuestion?.options_english;
    const shouldEnableEnglish =
      enable_english_medium === "1" && hasEnglishQuestions;

    // Check if there's a saved position for this quiz in practice mode
    let savedQuestionNum = null;
    if (only_for_practice) {
      const storageKey = `practice_last_question_${quid}`;
      const savedPosition = localStorage.getItem(storageKey);
      if (savedPosition !== null) {
        savedQuestionNum = parseInt(savedPosition, 10);
      }
    }

    this.setState(
      {
        quizDetails: quizList.data[0][0],
        questions: quizList.data[1],
        isLoading: false,
        categories: quizList.data[2],
        setCategory: quizList.data[2][0].category.split(",")[0],
        sec: parseInt(quizList.data[0][0].duration) * 60,
        phone: quizList.data[5].phone,
        allowNegativeMarking,
        englishMedium: shouldEnableEnglish,
        previousStatus: shouldEnableEnglish,
        only_for_practice,
        savedQuestionNum,
        resumeDialogOpen: savedQuestionNum !== null,
      },
      () => (this.countdown = setInterval(this.timer, 1000))
    );
  };
  timer = () => {
    const { sec, dialogOpen } = this.state;
    if (sec > 0 && !dialogOpen) {
      this.setState((prev) => ({ sec: prev.sec - 1 }));
    } else if (sec > 0 && dialogOpen) {
      this.setState({ sec: sec });
    } else if (sec <= 0) {
      this.submitQuiz();
    }
  };

  submitQuiz = async () => {
    const {
      answers,
      starttime,
      quizDetails,
      questions,
      sec,
      allowNegativeMarking,
    } = this.state;
    const timeSpent = parseInt(quizDetails.duration) * 60 - sec;
    const { match, history } = this.props;
    const { params } = match;
    const { quid: link } = params;
    const quid = link.split("$$$_")[0];
    const token = Cookie.get("jwt_token");
    const uid = localStorage.getItem("num");
    clearInterval(this.countdown);
    this.setState({ isLoading: true });
    const totalScore = Object.values(answers).map((each) => {
      if (each.slice(2, 3) === "]") {
        return each.split("]")[0].trim();
      }
      return each.split("ope")[0];
    });
    const sumScore =
      totalScore.length !== 0
        ? totalScore
            .reduce(
              (sum, num) =>
                sum +
                (parseFloat(num) === 1 ? 1 : allowNegativeMarking ? -0.333 : 0),
              0
            )
            .toFixed(2)
        : 0;
    // console.log(totalScore);
    const individualAnswers = Object.keys(answers).map((ind) => {
      const each = answers[ind];
      let ans = "";
      if (each.slice(2, 3) === "]") {
        ans =
          each.split("]")[1].split("ope")[1].trim() +
          "score" +
          each.split("]")[0].trim();
      } else {
        ans = each.split("ope")[1] + "score" + each.split("ope")[0].trim();
      }
      return { [ind.split("options")[1]]: ans };
    });
    // console.log(individualAnswers);

    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const submitTest = {
      starttime: starttime,
      endtime: new Date(),
      scoreTotal: sumScore,
      scorei: JSON.stringify(individualAnswers),
    };
    // console.log(submitTest);
    const submitResult = await axios.post(
      `${commonData["api"]}/quiz-attempt/submit-quiz/${quid}`,
      submitTest,
      { headers }
    );
    // await sendpush({
    //   title: `Exam Submitted Successfully...`,
    // message:
    //   filters: [
    //     { field: "tag", key: "number", relation: "=", value: String(uid) },
    //   ],
    //   url: `/view-result/${uid}/${quid}/${submitResult.data[0].rid}`,
    //   web_buttons: [
    //     {
    //       id: "like-button",
    //       text: "➤ View Detailed Result Analysis - Q & A",
    //       url: `/view-result/${uid}/${quid}/${submitResult.data[0].rid}`,
    //     },
    //   ],
    // });

    await axios.post(`${commonData["msgApi"]}`, {
      businessId: 1,
      verifyToken: Math.random() * 15000,
      phoneNumber: uid,
      message: [
        quizDetails.quiz_name,
        this.timeConvert(timeSpent),
        Object.keys(answers).length,
        questions.length - Object.keys(answers).length,
        sumScore,
        `${commonData["app"]}/view-result/${uid}/${quid}/${submitResult.data[0].rid}`,
      ],
      messageType: "promotion",
      templateLang: "en",
      templateName: "exam_submission_status",
    });
    const onesign = [];
    if (submitResult.data[0].onesignalId) {
      onesign.push(submitResult.data[0].onesignalId);
    }

    if (onesign.length > 0) {
      axios.post(`https://api.onesignal.com/notifications`, {
        app_id: "************************************",
        name: "string",
        contents: {
          en:
            "Click here to View Detailed Result Analysis\n" +
            `Exam Name :  ${
              quizDetails.quiz_name
            }\nTime Spent : ${this.timeConvert(
              timeSpent
            )}\nAttempted Questions : ${
              Object.keys(answers).length
            }\nNot Attempted Questions : ${
              questions.length - Object.keys(answers).length
            }\nTotal Score Obtained : ${sumScore}`,
        },
        headings: {
          en: "Your Exam Submitted Successfully..\n",
        },
        url: `${commonData["app"]}/view-result/${uid}/${quid}/${submitResult.data[0].rid}`,
        include_player_ids: onesign,
      });
    }
    this.setState({ isLoading: false });
    history.replace(
      `/view-result/${uid}/${quid}/${submitResult.data[0].rid}?msg=examsubmitted`
    );
    this.closeFullscreen();
  };

  componentWillUnmount() {
    // console.log("jsfghj");
    window.removeEventListener("beforeunload", this.alertUser);
    window.removeEventListener("blur", this.alertUser);
    clearInterval(this.countdown);
  }

  storeAnswers = (e) => {
    const answers = Object.assign({}, this.state.answers);
    const questionNum = e.target.name.replace("options", "");
    const newAnswer = e.target.value;

    // Parse the selected option index from the value (format: "1ope2" where 2 is the option index)
    const selectedOptionIndex = parseInt(newAnswer.split("ope")[1], 10);

    answers[e.target.name] = newAnswer;

    this.setState({ answers }, () => {
      // If it's a practice quiz, show immediate feedback
      if (this.state.only_for_practice) {
        // First, remove any existing styling from all options
        this.resetAnswerStyling(questionNum);

        // Highlight both the correct answer and the user's selected answer
        this.highlightAnswers(questionNum, selectedOptionIndex);
      }
    });
  };

  // Helper function to reset styling on all options for a question
  resetAnswerStyling = (questionNum) => {
    // Remove styling from all options
    const optionLabels = document.querySelectorAll(
      `label[for^="op"][for$="${questionNum}"]`
    );
    optionLabels.forEach((label) => {
      // Remove classes
      label.classList.remove("correct-answer", "incorrect-answer");

      // Remove any existing icons
      const icon = label.querySelector(".answer-icon");
      if (icon) {
        label.removeChild(icon);
      }
    });
  };

  // New function to highlight both correct answer and user's selected answer
  highlightAnswers = (questionNum, selectedOptionIndex) => {
    const { questions } = this.state;
    const question = questions[questionNum];

    if (!question) return; // Safety check

    const eachOp = this.state.englishMedium
      ? question.options_english.split("],")
      : question.options.split("],");

    let correctOptionIndex = -1;

    // First, find the correct answer
    eachOp.forEach((option, idx) => {
      try {
        const optionText = option.trim();
        const scoreParts = optionText.split("score");

        if (scoreParts.length > 1) {
          const scoreValue = scoreParts[1].trim().replace("]", "");

          // Check if this is the correct answer (score = 1)
          if (scoreValue === "1") {
            correctOptionIndex = idx;
          }
        }
      } catch (error) {
        console.error(
          `Error finding correct option for question ${questionNum}:`,
          error
        );
      }
    });

    // Now highlight both the correct answer and the user's selection
    const isCorrect = correctOptionIndex === selectedOptionIndex;

    // 1. Highlight the correct answer
    const correctLabel = document.querySelector(
      `label[for="op${correctOptionIndex}${questionNum}"]`
    );
    if (correctLabel) {
      correctLabel.classList.add("correct-answer");

      // Add the correct icon
      const icon = document.createElement("span");
      icon.className = "answer-icon correct-icon";
      icon.innerHTML = "✓";
      correctLabel.appendChild(icon);

      // Add a "Correct Answer" text indicator
      const correctText = document.createElement("div");
      correctText.className = "answer-text";
      // correctText.innerHTML = "Correct Answer";
      correctText.style.fontSize = "12px";
      correctText.style.fontWeight = "bold";
      correctText.style.marginTop = "4px";
      // correctText.style.color = "#4CAF50";
      correctLabel.appendChild(correctText);
    }

    // 2. If the user selected a different (incorrect) answer, highlight it as well
    if (!isCorrect) {
      const selectedLabel = document.querySelector(
        `label[for="op${selectedOptionIndex}${questionNum}"]`
      );
      if (selectedLabel) {
        selectedLabel.classList.add("incorrect-answer");

        // Add the incorrect icon
        const icon = document.createElement("span");
        icon.className = "answer-icon incorrect-icon";
        icon.innerHTML = "✗";
        selectedLabel.appendChild(icon);

        // Add a "Your Answer" text indicator
        const yourAnswerText = document.createElement("div");
        yourAnswerText.className = "answer-text";
        // yourAnswerText.innerHTML = "Your Answer";
        yourAnswerText.style.fontSize = "12px";
        yourAnswerText.style.fontWeight = "bold";
        yourAnswerText.style.marginTop = "4px";
        // yourAnswerText.style.color = "#F44336";
        selectedLabel.appendChild(yourAnswerText);
      }
    }

    // Update the practice score and answered questions tracking
    let updatedScore = this.state.practiceScore;
    let answeredQuestions = { ...this.state.answeredQuestions };

    // Only update the score if this question hasn't been answered correctly before
    if (
      !answeredQuestions[questionNum] ||
      !answeredQuestions[questionNum].correct
    ) {
      // If previously answered incorrectly, remove that incorrect answer from the score calculation
      if (
        answeredQuestions[questionNum] &&
        !answeredQuestions[questionNum].correct
      ) {
        // No need to adjust score for incorrect answers
      } else {
        // First time answering or changing from incorrect to correct
        updatedScore += isCorrect ? 1 : 0;
      }

      // Update the answered questions tracking
      answeredQuestions[questionNum] = {
        answered: true,
        correct: isCorrect,
        attempted: true,
        selectedOptionIndex: selectedOptionIndex,
        correctOptionIndex: correctOptionIndex,
      };
    }

    // Save the updated state
    this.setState({
      practiceScore: updatedScore,
      answeredQuestions,
    });

    // Also save the current position for resume functionality
    this.saveLastQuestionPosition(questionNum);

    return { isCorrect, correctOptionIndex };
  };

  // Keep the old function for backward compatibility
  highlightCorrectAnswer = (questionNum) => {
    const { questions } = this.state;
    const question = questions[questionNum];

    if (!question) return -1; // Safety check

    const eachOp = this.state.englishMedium
      ? question.options_english.split("],")
      : question.options.split("],");

    let correctOptionIndex = -1;

    // Find which option has score = 1
    eachOp.forEach((option, idx) => {
      try {
        const optionText = option.trim();
        const scoreParts = optionText.split("score");

        if (scoreParts.length > 1) {
          const scoreValue = scoreParts[1].trim();

          // Check if this is the correct answer (score = 1)
          if (scoreValue === "1") {
            correctOptionIndex = idx;

            // Get the label for this option
            const correctLabel = document.querySelector(
              `label[for="op${idx}${questionNum}"]`
            );
            if (correctLabel) {
              // Add correct answer styling
              correctLabel.classList.add("correct-answer");

              // Add the correct icon
              const icon = document.createElement("span");
              icon.className = "answer-icon correct-icon";
              icon.innerHTML = "✓";
              correctLabel.appendChild(icon);
            }
          }
        }
      } catch (error) {
        console.error(
          `Error processing option ${idx} for question ${questionNum}:`,
          error
        );
      }
    });

    return correctOptionIndex;
  };

  renderQA = () => {
    const { qNum, answers, questions, nightMode, englishMedium } = this.state;
    const each = questions[qNum];

    const options = ["A", "B", "C", "D", "E"];
    const eachOp = englishMedium
      ? each.options_english.split("],")
      : each.options.split("],");
    const enable_english_medium = localStorage.getItem("enable_english_medium");

    return (
      <>
        <div
          style={{
            display: "flex",
            justifyContent:
              enable_english_medium === "1" ? "space-between" : "center",
            marginTop: 15,
          }}
        >
          {!this.state.only_for_practice && (
            <label style={{ color: "#FFF" }}>
              Night Mode
              <input
                type="checkbox"
                checked={nightMode}
                onChange={() =>
                  this.setState((p) => ({ nightMode: !p.nightMode }))
                }
                style={{ marginLeft: 10 }}
              />
            </label>
          )}
          {enable_english_medium === "1" &&
            each?.question_in_english &&
            each?.options_english &&  (
              <label style={{ color: "#FFF" }}>
                English Medium
                <input
                  type="checkbox"
                  checked={englishMedium}
                  onChange={() => {
                    if (this.state.showAlert && !this.state.englishMedium) {
                      const status = window.confirm(
                        "The English version is available in real time based on Artificial Intelligence developed by the NAVAChaitanya Competitions team. Its accuracy is only 80% and we request you to consider the Telugu medium questions as authentic."
                      );
                      this.setState({
                        englishMedium: status,
                        showAlert: !status,
                        previousStatus: status,
                      });
                      return;
                    }
                    this.setState({
                      englishMedium: !this.state.englishMedium,
                      previousStatus: !this.state.englishMedium,
                    });
                  }}
                  style={{ marginLeft: 10 }}
                />
              </label>
            )}
        </div>
        <div className="qoptions" onCopy={(e) => e.preventDefault()}>
          <div key={qNum * 99 + "attempt"}>
            <p className="questionfont" style={{ display: "flex" }}>
              Q:{qNum + 1}
              {") "}
              <span
                style={{ marginLeft: 5 }}
                dangerouslySetInnerHTML={{
                  __html: !englishMedium
                    ? each.question
                    : each?.question_in_english,
                }}
              ></span>
            </p>
            <div>
              {eachOp.length >= 1 && (
                <div className="option-wrapper">
                  <div style={{ display: "flex", alignItems: "flex-start" }}>
                    <div style={{ marginTop: 10, marginRight: 5 }}>
                      <input
                        type="radio"
                        name={"options" + qNum}
                        id={"op" + 0 + qNum}
                        key={"nooke" + 0 + qNum + "attempt"}
                        value={eachOp[0].split("score")[1] + "ope0"}
                        onChange={this.storeAnswers}
                        style={{ marginBottom: "10px" }}
                        checked={
                          answers["options" + qNum] ===
                          eachOp[0].split("score")[1] + "ope0"
                        }
                      />
                    </div>
                    <label
                      htmlFor={"op" + 0 + qNum}
                      style={{
                        fontSize: 19,
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          fontSize: 19,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                      >
                        <span>{options[0]}</span>
                        <span
                          style={{
                            marginRight: "8px",
                          }}
                        >
                          {")"}
                        </span>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: eachOp[0].split("score")[0],
                          }}
                        ></span>
                      </div>
                    </label>
                  </div>
                </div>
              )}
              {eachOp.length >= 2 && (
                <div className="option-wrapper">
                  <div style={{ display: "flex", alignItems: "flex-start" }}>
                    <div style={{ marginTop: 10, marginRight: 5 }}>
                      <input
                        type="radio"
                        name={"options" + qNum}
                        id={"op" + 1 + qNum}
                        key={"nooke" + 1 + qNum + "attempt"}
                        value={eachOp[1].split("score")[1] + "ope1"}
                        onChange={this.storeAnswers}
                        style={{ marginBottom: "10px" }}
                        checked={
                          answers["options" + qNum] ===
                          eachOp[1].split("score")[1] + "ope1"
                        }
                      />
                    </div>
                    <label
                      htmlFor={"op" + 1 + qNum}
                      style={{
                        fontSize: 19,
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          fontSize: 19,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                      >
                        <span>{options[1]}</span>{" "}
                        <span
                          style={{
                            marginRight: "8px",
                          }}
                        >
                          {")"}
                        </span>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: eachOp[1].split("score")[0],
                          }}
                        ></span>
                      </div>
                    </label>
                  </div>
                </div>
              )}
              {eachOp.length >= 3 && (
                <div className="option-wrapper">
                  <div style={{ display: "flex", alignItems: "flex-start" }}>
                    <div style={{ marginTop: 10, marginRight: 5 }}>
                      <input
                        type="radio"
                        name={"options" + qNum}
                        id={"op" + 2 + qNum}
                        key={"nooke" + 2 + qNum + "attempt"}
                        value={eachOp[2].split("score")[1] + "ope2"}
                        onChange={this.storeAnswers}
                        style={{ marginBottom: "10px" }}
                        checked={
                          answers["options" + qNum] ===
                          eachOp[2].split("score")[1] + "ope2"
                        }
                      />
                    </div>
                    <label
                      htmlFor={"op" + 2 + qNum}
                      style={{
                        fontSize: 19,
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          fontSize: 19,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                      >
                        <span>{options[2]}</span>{" "}
                        <span
                          style={{
                            marginRight: "8px",
                          }}
                        >
                          {")"}
                        </span>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: eachOp[2].split("score")[0],
                          }}
                        ></span>
                      </div>
                    </label>
                  </div>
                </div>
              )}
              {eachOp.length >= 4 && (
                <div className="option-wrapper">
                  <div style={{ display: "flex", alignItems: "flex-start" }}>
                    <div style={{ marginTop: 10, marginRight: 5 }}>
                      <input
                        type="radio"
                        name={"options" + qNum}
                        id={"op" + 3 + qNum}
                        key={"nooke" + 3 + qNum + "attempt"}
                        value={eachOp[3].split("score")[1] + "ope3"}
                        onChange={this.storeAnswers}
                        style={{ marginBottom: "10px" }}
                        checked={
                          answers["options" + qNum] ===
                          eachOp[3].split("score")[1] + "ope3"
                        }
                      />
                    </div>
                    <label
                      htmlFor={"op" + 3 + qNum}
                      style={{
                        fontSize: 19,
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          fontSize: 19,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                      >
                        <span>{options[3]}</span>{" "}
                        <span
                          style={{
                            marginRight: "8px",
                          }}
                        >
                          {")"}
                        </span>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: eachOp[3].split("score")[0],
                          }}
                        ></span>
                      </div>
                    </label>
                  </div>
                </div>
              )}
              {eachOp.length >= 5 && (
                <div className="option-wrapper">
                  <div style={{ display: "flex", alignItems: "flex-start" }}>
                    <div style={{ marginTop: 10, marginRight: 5 }}>
                      <input
                        type="radio"
                        name={"options" + qNum}
                        id={"op" + 4 + qNum}
                        key={"nooke" + 4 + qNum + "attempt"}
                        value={eachOp[4].split("score")[1] + "ope4"}
                        onChange={this.storeAnswers}
                        style={{ marginBottom: "10px" }}
                        checked={
                          answers["options" + qNum] ===
                          eachOp[4].split("score")[1] + "ope4"
                        }
                      />
                    </div>
                    <label
                      htmlFor={"op" + 4 + qNum}
                      style={{
                        fontSize: 19,
                        display: "flex",
                        flexDirection: "column",
                        width: "100%",
                      }}
                    >
                      <div
                        style={{
                          fontSize: 19,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "flex-start",
                        }}
                      >
                        <span>{options[4]}</span>{" "}
                        <span
                          style={{
                            marginRight: "8px",
                          }}
                        >
                          {")"}
                        </span>
                        <span
                          dangerouslySetInnerHTML={{
                            __html: eachOp[4].split("score")[0],
                          }}
                        ></span>
                      </div>
                    </label>
                  </div>
                </div>
              )}

              {/* Add feedback message for practice mode */}
              {this.state.only_for_practice && answers["options" + qNum] && (
                <div
                  className={`practice-feedback ${
                    this.state.answeredQuestions[qNum]?.correct
                      ? "correct"
                      : "incorrect"
                  }`}
                >
                  {this.state.answeredQuestions[qNum]?.correct ? (
                    <>
                      <div className="feedback-header">
                        <span className="feedback-icon">✓</span>
                        <span className="feedback-title">
                          Correct! Good job!
                        </span>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="feedback-header">
                        <span className="feedback-icon">✗</span>
                        <span className="feedback-title">Incorrect</span>
                      </div>
                    </>
                  )}
                </div>
              )}
              {eachOp.length >= 6 && <p></p>}
            </div>
          </div>
        </div>
      </>
    );
  };
  subjectNameSelected = (e) => {
    const { questions } = this.state;
    // console.log(e.target.value);
    const questionIndex = questions.findIndex(
      (each) => each.cid === parseInt(e.target.value)
    );
    // console.log(questionIndex);
    this.setState({ qNum: questionIndex, setCategory: e.target.value });
  };

  timeConvert = (n) => {
    let hours = n / 3600;
    let h = Math.floor(hours);
    h = h < 10 ? "0" + h : h;
    let rem = n % 3600;
    let minutes = rem / 60;
    let m = Math.floor(minutes);
    m = m < 10 ? "0" + m : m;
    rem = rem % 60;
    rem = rem < 10 ? "0" + rem + "s" : rem + "s";
    h = h > 0 ? h + "h : " : "";
    m = m > 0 ? m + "m : " : "";
    // rem = rem < 60 ? rem + " Seconds" : rem;
    return h + m + rem;
  };

  renderCategorySelect = () => {
    const { categories, setCategory } = this.state;
    // console.log(categories);
    return (
      <Select
        required
        id="Please-Select-category"
        className="attempt-select"
        value={setCategory}
        style={{ marginRight: 10 }}
        onChange={this.subjectNameSelected}
        // MenuProps={MenuProps}
      >
        <MenuItem disabled value="">
          <em>Select Subject Name</em>
        </MenuItem>
        {categories.map(({ category }) => (
          <MenuItem
            key={category}
            value={category.split(",")[0]}
            className="attempt-option-select"
          >
            {category.split(",")[1]}
          </MenuItem>
        ))}
      </Select>
    );
  };
  renderTimer = () => {
    const { sec } = this.state;
    return (
      <div className="timer">
        <p className="timerleft">Time Left</p>
        <p className="timersec">{this.timeConvert(sec)}</p>
      </div>
    );
  };

  handleClose = () => {
    this.setState((prev) => ({ dialogOpen: !prev.dialogOpen }));
  };

  // Handle exit from practice exam
  handleExitPracticeExam = () => {
    // Show confirmation dialog
    this.setState({ exitPracticeDialogOpen: true });
  };

  // Confirm exit from practice exam
  confirmExitPracticeExam = () => {
    // Save the current position before exiting
    const { qNum } = this.state;
    this.saveLastQuestionPosition(qNum);

    // Close the dialog
    this.setState({ exitPracticeDialogOpen: false });

    // Navigate back to the previous page
    window.history.back();
  };

  // Cancel exit from practice exam
  cancelExitPracticeExam = () => {
    this.setState({ exitPracticeDialogOpen: false });
  };

  renderButtons = () => {
    const { questions, answers, qNum, only_for_practice, practiceScore } =
      this.state;
    const numsLen = questions.length;
    return (
      <div className="buttons-fixed">
        <div className="attempt-page-btns">
          <Button
            variant="contained"
            className="btn header-btns attemptbtn attempt-btns back"
            onClick={() => {
              if (this.state.qNum > 0) {
                const each = questions[this.state.qNum - 1];
                const enable_english_medium = localStorage.getItem(
                  "enable_english_medium"
                );
                let enableEM =
                  enable_english_medium === "1" &&
                  each?.question_in_english &&
                  each?.options_english;

                this.setState((prev) => ({
                  qNum: prev.qNum - 1,
                  disabledback: false,
                  disabledSave: false,
                  englishMedium: enableEM && prev.previousStatus,
                }));
              } else {
                this.setState({
                  disabledback: true,
                  disabledSave: false,
                });
              }
            }}
            disabled={this.state.disabledback}
          >
            Back
          </Button>
          <Button
            variant="contained"
            className="btn header-btns attemptbtn attempt-btns save"
            onClick={() => {
              // For practice mode, save the current question position
              if (only_for_practice) {
                this.saveLastQuestionPosition(qNum);
              }

              if (this.state.qNum < numsLen - 1) {
                const each = questions[this.state.qNum + 1];
                const enable_english_medium = localStorage.getItem(
                  "enable_english_medium"
                );
                let enableEM =
                  enable_english_medium === "1" &&
                  each?.question_in_english &&
                  each?.options_english;

                this.setState((prev) => ({
                  qNum: prev.qNum + 1,
                  disabledback: false,
                  disabledSave: false,
                  englishMedium: enableEM && prev.previousStatus,
                }));

                let id =
                  window.innerWidth > 767
                    ? document.getElementById(qNum + "attemptdesk")
                    : document.getElementById(qNum + "attempt");
                // console.log(id);

                if (answers["options" + qNum]) {
                  id.classList.remove("back");
                  id.classList.add("save");
                } else {
                  id.classList.remove("save");
                  id.classList.add("back");
                }
              } else if (this.state.qNum < numsLen && !only_for_practice) {
                let id =
                  window.innerWidth > 767
                    ? document.getElementById(qNum + "attemptdesk")
                    : document.getElementById(qNum + "attempt");
                if (answers["options" + qNum]) {
                  id.classList.remove("back");
                  id.classList.remove("review");
                  id.classList.add("save");
                } else {
                  id.classList.remove("save");
                  id.classList.add("back");
                }
                this.setState({
                  dialogOpen: true,
                });
              } else if (this.state.qNum < numsLen) {
                this.setState({
                  disabledSave: true,
                  disabledback: false,
                });
              }
            }}
            disabled={this.state.disabledSave}
          >
            Save {"&"} Next
          </Button>
        </div>

        <div className="attempt-page-btns">
          <Button
            variant="contained"
            className="btn header-btns attemptbtn attempt-btns clear"
            onClick={() => {
              const clearOption = "options" + qNum;
              let remainAnswers = answers;
              if (remainAnswers[clearOption]) {
                delete remainAnswers[clearOption];

                this.setState({
                  answers: remainAnswers,
                });
                let id =
                  window.innerWidth > 767
                    ? document.getElementById(qNum + "attemptdesk")
                    : document.getElementById(qNum + "attempt");
                id.classList.remove("save");
                id.classList.remove("review");
                id.classList.add("back");

                // Remove any correct/incorrect styling in practice mode
                if (this.state.only_for_practice) {
                  const optionLabels = document.querySelectorAll(
                    `label[for^="op"][for$="${qNum}"]`
                  );
                  optionLabels.forEach((label) => {
                    label.classList.remove(
                      "correct-answer",
                      "incorrect-answer"
                    );
                    // Remove any icons
                    const icon = label.querySelector(".answer-icon");
                    if (icon) {
                      label.removeChild(icon);
                    }
                  });
                }
              }
            }}
          >
            Clear
          </Button>
          <Button
            variant="contained"
            className="btn header-btns attemptbtn attempt-btns review"
            onClick={() => {
              let id =
                window.innerWidth > 767
                  ? document.getElementById(qNum + "attemptdesk")
                  : document.getElementById(qNum + "attempt");
              id.classList.remove("save");
              id.classList.remove("back");
              id.classList.add("review");
            }}
          >
            Review
          </Button>

          {only_for_practice && (
            <>
              <div className="practice-score-container">
                <div className="practice-score">
                  Score: <span className="score-value">{practiceScore}</span>/
                  {questions.length}
                </div>
              </div>
              <Button
                variant="contained"
                className="btn header-btns attemptbtn attempt-btns exit"
                onClick={this.handleExitPracticeExam}
              >
                Exit Exam
              </Button>
            </>
          )}

          {!only_for_practice && (
            <Button
              variant="contained"
              className="btn header-btns attemptbtn attempt-btns submit"
              onClick={this.handleClose}
            >
              Submit Exam
            </Button>
          )}
        </div>
      </div>
    );
  };

  renderQuestionNums = () => {
    const { questions, answeredQuestions, only_for_practice } = this.state;
    return (
      <div className="buttons">
        {questions.map((_, i) => {
          // Determine if this question has been answered correctly/incorrectly
          let buttonClass = "numbuttonattemt";
          if (only_for_practice && answeredQuestions[i]) {
            buttonClass += answeredQuestions[i].correct
              ? " correct-question"
              : " incorrect-question";
          }

          return (
            <button
              className={buttonClass}
              onClick={() => this.setState({ qNum: i })}
              key={i - 1 + "attempt"}
              id={i + "attempt"}
            >
              {i + 1}
            </button>
          );
        })}
      </div>
    );
  };

  renderDeskQuestionNums = () => {
    const { questions, answeredQuestions, only_for_practice } = this.state;
    return (
      <div className="buttons desk">
        {questions.map((_, i) => {
          // Determine if this question has been answered correctly/incorrectly
          let buttonClass = "numbuttonattemt";
          if (only_for_practice && answeredQuestions[i]) {
            buttonClass += answeredQuestions[i].correct
              ? " correct-question"
              : " incorrect-question";
          }

          return (
            <button
              className={buttonClass}
              onClick={() => this.setState({ qNum: i })}
              key={i - 1 + "attemptttquiz"}
              id={i + "attemptdesk"}
            >
              {i + 1}
            </button>
          );
        })}
      </div>
    );
  };

  renderColorInfo = () => {
    const { only_for_practice } = this.state;
    return (
      <>
        <div className="colorsdiv">
          <div className="back colorin">Answered</div>
          <div className="save colorin" style={{ marginLeft: 10 }}>
            UnAnswered
          </div>
        </div>
        <div className="colorsdiv">
          <div className="review colorin">Review Later</div>
          <div className="colorin" style={{ marginLeft: 10 }}>
            Not Visited
          </div>
        </div>
        {only_for_practice && (
          <div className="colorsdiv">
            <div className="correct-question colorin">Correct</div>
            <div
              className="incorrect-question colorin"
              style={{ marginLeft: 10 }}
            >
              Incorrect
            </div>
          </div>
        )}
      </>
    );
  };

  renderDialogBox = () => {
    const { dialogOpen, quizDetails, answers, questions, sec } = this.state;
    const timeSpent = parseInt(quizDetails.duration) * 60 - sec;

    return (
      <Dialog
        open={dialogOpen}
        onClose={this.handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
      >
        <DialogTitle id="alert-dialog-title">
          {"Do you really want to submit this Exam ?"}
        </DialogTitle>
        <DialogContent>
          <table className="table ">
            <thead>
              <tr>
                <th className="tablehead examdetails">Exam Name</th>
                <th className="tablehead examdetails">
                  {quizDetails.quiz_name}
                </th>
              </tr>
            </thead>
            <thead>
              <tr>
                <th className="tablehead examdetails">Time Spent</th>
                <th className="tablehead examdetails">
                  {this.timeConvert(timeSpent)}
                </th>
              </tr>
            </thead>
            <thead>
              <tr>
                <th className="tablehead examdetails">Attempted Questions</th>
                <th className="tablehead examdetails">
                  {Object.keys(answers).length}
                </th>
              </tr>
            </thead>
            <thead>
              <tr>
                <th className="tablehead examdetails">
                  Not Attempted Questions
                </th>
                <th className="tablehead examdetails">
                  {questions.length - Object.keys(answers).length}
                </th>
              </tr>
            </thead>
          </table>
          <div className="submit-popup-btn">
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.handleClose}
            >
              Cancel
            </Button>
            <Button
              onClick={this.submitQuiz}
              className="btn header-btns attemptbtn attempt-btns save popbtn"
            >
              Submit Exam
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  // Render exit practice exam dialog
  renderExitPracticeDialog = () => {
    const { exitPracticeDialogOpen, practiceScore, questions } = this.state;

    return (
      <Dialog
        open={exitPracticeDialogOpen}
        onClose={this.cancelExitPracticeExam}
        aria-labelledby="exit-practice-dialog-title"
        aria-describedby="exit-practice-dialog-description"
      >
        <DialogTitle id="exit-practice-dialog-title">
          {"Exit Practice Exam?"}
        </DialogTitle>
        <DialogContent>
          <div style={{ marginBottom: "20px" }}>
            Your current progress will be saved, and you can resume this practice exam later.
          </div>
          <div style={{
            backgroundColor: "rgba(255, 193, 7, 0.1)",
            padding: "15px",
            borderRadius: "8px",
            marginBottom: "20px",
            border: "1px solid #FFC107"
          }}>
            <div style={{ fontWeight: "bold", marginBottom: "10px" }}>Current Score:</div>
            <div style={{ fontSize: "18px", textAlign: "center" }}>
              <span style={{ color: "#F44336", fontWeight: "bold" }}>{practiceScore}</span>
              <span> / {questions.length}</span>
            </div>
          </div>
          <div className="submit-popup-btn">
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.cancelExitPracticeExam}
              style={{ backgroundColor: "#F44336", color: "white" }}
            >
              Cancel
            </Button>
            <Button
              onClick={this.confirmExitPracticeExam}
              className="btn header-btns attemptbtn attempt-btns save popbtn"
              style={{ backgroundColor: "#FFC107", color: "black" }}
            >
              Exit & Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  };

  render() {
    const { quizDetails, isLoading, nightMode, only_for_practice } = this.state;
    // console.log(quizDetails);
    const phone = localStorage.getItem("num");
    const back = `.qoptions{
      background-color: ${nightMode ? "black" : "white"};
      color: ${!nightMode ? "black" : "white"};;
      background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' version='1.1'  height='70px' width='150px' transform='rotate(-15)'><text x='0' y='25' fill='${
        !nightMode ? "black" : "white"
      }' opacity='0.1' font-size='22'>${phone}</text></svg>") !important;}`;

    const practiceStyles = `
      /* Answer styling for practice mode */
      .correct-answer {
        background-color: rgba(76, 175, 80, 0.15);
        border-radius: 8px;
        padding: 12px 40px 12px 12px;
        border-left: 6px solid #4CAF50;
        position: relative;
        margin-bottom: 12px;
        width: 100%;
        display: block;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
      }

      .correct-answer:hover {
        background-color: rgba(76, 175, 80, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      .incorrect-answer {
        background-color: rgba(244, 67, 54, 0.15);
        border-radius: 8px;
        padding: 12px 40px 12px 12px;
        border-left: 6px solid #F44336;
        position: relative;
        margin-bottom: 12px;
        width: 100%;
        display: block;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: all 0.2s ease;
      }

      .incorrect-answer:hover {
        background-color: rgba(244, 67, 54, 0.25);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      .answer-icon {
        position: absolute;
        right: 10px;
        top: 15px;
        font-weight: bold;
        font-size: 16px;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      .correct-icon {
        color: white;
        background-color: #4CAF50;
      }

      .incorrect-icon {
        color: white;
        background-color: #F44336;
      }

      .answer-text {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 4px;
        margin-top: 8px;
        font-size: 12px;
        font-weight: bold;
      }

      .correct-answer-text {
        background-color: rgba(76, 175, 80, 0.2);
        color: #2E7D32;
      }

      .your-answer-text {
        background-color: rgba(244, 67, 54, 0.2);
        color: #C62828;
      }
      .practice-mode-banner {
        background-color: #FFC107;
        color: #000;
        text-align: center;
        padding: 8px 0;
        font-weight: bold;
        font-size: 16px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 10px;
      }
      .practice-score-container {
        margin-left: 10px;
        display: flex;
        align-items: center;
      }
      .practice-score {
        background-color: #FFC107; /* Using existing yellow color */
        color: black;
        padding: 8px 15px;
        border-radius: 20px;
        font-weight: bold;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
      }
      .score-value {
        color: #F44336; /* Using existing red color for contrast */
        font-size: 18px;
        font-weight: bold;
      }
      .correct-question {
        background-color: rgba(76, 175, 80, 0.3) !important;
        border: 2px solid #4CAF50 !important;
        color: #000 !important;
        font-weight: bold !important;
      }
      .incorrect-question {
        background-color: rgba(244, 67, 54, 0.3) !important;
        border: 2px solid #F44336 !important;
        color: #000 !important;
        font-weight: bold !important;
      }

      /* Additional styles for practice mode */
      .practice-feedback {
        margin-top: 20px;
        margin-bottom: 15px;
        padding: 15px;
        border-radius: 8px;
        font-weight: bold;
        display: flex;
        flex-direction: column;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      .practice-feedback.correct {
        background-color: rgba(76, 175, 80, 0.1);
        border: 1px solid #4CAF50;
        color: #2E7D32;
      }

      .practice-feedback.incorrect {
        background-color: rgba(244, 67, 54, 0.1);
        border: 1px solid #F44336;
        color: #C62828;
      }

      .feedback-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }

      .feedback-icon {
        font-size: 20px;
        margin-right: 10px;
        width: 28px;
        height: 28px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: white;
      }

      .practice-feedback.correct .feedback-icon {
        background-color: #4CAF50;
      }

      .practice-feedback.incorrect .feedback-icon {
        background-color: #F44336;
      }

      .feedback-title {
        font-size: 16px;
        font-weight: bold;
      }

      .feedback-detail {
        font-size: 14px;
        font-weight: normal;
        opacity: 0.9;
        line-height: 1.4;
      }

      /* Exit button styles */
      .exit {
        background-color: #FFC107 !important;
        color: black !important;
        font-weight: bold !important;
        margin-left: 10px !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
        border: 1px solid #e0a800 !important;
      }

      .exit:hover {
        background-color: #e0a800 !important;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3) !important;
      }

      .option-wrapper {
        margin-bottom: 10px;
        transition: all 0.2s ease;
      }

      .option-wrapper:hover {
        transform: translateX(5px);
      }

      /* Resume dialog styles */
      .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
      }
      .modal-content {
        background-color: white;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        overflow: hidden;
      }
      .modal-header {
        background-color: #FFC107; /* Using the same yellow as practice banner */
        color: #000;
        padding: 15px 20px;
      }
      .modal-header h3 {
        margin: 0;
        font-size: 18px;
      }
      .modal-body {
        padding: 20px;
        color: #333;
      }
      .modal-footer {
        padding: 15px 20px;
        background-color: #f5f5f5;
        border-top: 1px solid #ddd;
      }
    `;

    return (
      <>
        {isLoading ? (
          <div className="loader-main-container">
            <Loader />
          </div>
        ) : (
          <div className="deskattempt">
            <style>{back}</style>
            {only_for_practice && <style>{practiceStyles}</style>}
            {/* Render resume dialog if needed */}
            {this.renderResumeDialog()}

            {/* Render exit practice dialog if needed */}
            {only_for_practice && this.renderExitPracticeDialog()}

            <div style={{ margin: 20 }} className="deskQuestions">
              {!only_for_practice && (
                <p className="homepage-package-title attempt-title">
                  {quizDetails.quiz_name}
                </p>
              )}
              <div className="attempt-select-subject-timeleft">
                {this.renderCategorySelect()}
                {this.renderTimer()}
              </div>
              {this.renderQA()}

              {this.renderButtons()}
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  padding: 10,
                }}
                className="QuestionNumsdesk"
              >
                {this.renderQuestionNums()}
              </div>
              <div className="QuestionNumsdesk">{this.renderColorInfo()}</div>
              {this.renderDialogBox()}
            </div>
            <div className="deskbtnsattempt">
              <p className="questionstitle">Questions</p>
              <div
                style={{
                  display: "flex",
                  flexDirection: "row",
                  justifyContent: "center",
                  padding: 25,
                }}
                className="questions-desktop"
              >
                {this.renderDeskQuestionNums()}
              </div>
              <div className="desk-colors">{this.renderColorInfo()}</div>
            </div>
          </div>
        )}
      </>
    );
  }
}

export default AttemptQuiz;
