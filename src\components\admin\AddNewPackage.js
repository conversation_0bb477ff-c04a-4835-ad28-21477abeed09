import react, { useEffect, useState } from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
// import { Editor } from "@tinymce/tinymce-react";
import ExportCSV from "./ExportCsv";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import ListItemText from "@mui/material/ListItemText";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import Checkbox from "@mui/material/Checkbox";
import { IconButton, TextField } from "@mui/material";
import { CheckCircle, Delete, Edit } from "@mui/icons-material";
import { exportToCSVD } from "./ExportCsv";
import Editor from "./Editor";
// import dayjs, { Dayjs } from 'dayjs';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};

const RenderDatesPopup = ({ gid, onClose }) => {
  const [editIndex, setEditIndex] = useState(null);
  const [editId, setEditId] = useState(null);
  const [editedData, setEditedData] = useState([]);
  const [tempDate, setTempDate] = useState("");
  const [tempCreatedOn, setTempCreatedOn] = useState("");

  useEffect(() => {
    getLaunchData();
  }, []);
  const getLaunchData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getLaunchData",
      search: "",
      qid: gid,
    };
    try {
      const launchData = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        {
          headers,
        }
      );
      setEditedData(launchData.data[0]);
    } catch (err) {}
  };

  const createNew = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "createLaunchData",
      search: "",
      qid: gid,
    };
    try {
      await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
        headers,
      });
      getLaunchData();
    } catch (err) {}
  };
  const deleteLaunchData = async (id) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "deleteLaunchData",
      search: "",
      qid: id,
    };
    try {
      await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
        headers,
      });
      getLaunchData();
    } catch (err) {}
  };
  const handleEditClick = (index) => {
    setEditIndex(index);

    setEditId(editedData[index].idlaunch_packages);
    setTempDate(editedData[index].date);
    setTempCreatedOn(editedData[index].createdOn);
  };

  const editLaunchData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "UpdateLaunch",
      search: tempDate + "$$" + tempCreatedOn,
      qid: editId,
    };
    try {
      await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
        headers,
      });
      NotificationManager.success(`Launch updated Succesfully...`);
      onClose();
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };

  const handleSaveClick = async () => {
    const newData = [...editedData];
    newData[editIndex] = { date: tempDate, createdOn: tempCreatedOn };
    setEditedData(newData);
    setEditIndex(null);
    await editLaunchData();
  };

  return (
    <Dialog open={true} maxWidth="sm" fullWidth>
      <DialogTitle>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <div> Launch Package Dates</div>
          <Button className="btn exportbtn" onClick={createNew}>
            Create New
          </Button>
          <Button onClick={onClose} style={{ color: "#000", fontSize: 20 }}>
            X
          </Button>
        </div>
      </DialogTitle>
      <DialogContent style={{ paddingTop: 15 }}>
        <style>
          {`
            table {
              font-family: Arial, sans-serif;
              border-collapse: collapse;
              width: 100%;
            }
            td, th {
              border: 1px solid #dddddd;
              text-align: left;
              padding: 10px;
            }
            tr:nth-child(even) {
              background-color: #dddddd;
            }
          `}
        </style>
        <table>
          <thead>
            <tr>
              <th>Launching Date</th>
              <th>Created On</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {editedData.length > 0 ? (
              editedData.map((item, index) => (
                <tr key={index}>
                  {editIndex === index ? (
                    <>
                      <td>
                        <TextField
                          value={tempDate}
                          onChange={(e) => setTempDate(e.target.value)}
                          style={{ padding: "10px" }}
                        />
                        {/* <input
                          type="date"
                          id="date"
                          name="date"
                          min={formattedDate}
                          value={tempDate}
                          onChange={(e) => setTempDate(e.target.value)}
                        /> */}
                      </td>
                      <td>
                        <TextField
                          value={tempCreatedOn}
                          onChange={(e) => setTempCreatedOn(e.target.value)}
                          style={{ padding: "10px" }}
                        />
                        {/* <input
                          type="date"
                          id="date"
                          name="date"
                          max={tempDate}
                          value={tempCreatedOn}
                          onChange={(e) => setTempCreatedOn(e.target.value)}
                        /> */}
                      </td>
                    </>
                  ) : (
                    <>
                      <td>{item.date}</td>
                      <td>{item.createdOn}</td>
                    </>
                  )}
                  <td>
                    <div style={{ display: "flex", justifyContent: "center" }}>
                      <IconButton
                        onClick={() => {
                          editIndex === index
                            ? handleSaveClick()
                            : handleEditClick(index);
                        }}
                      >
                        {editIndex === index ? <CheckCircle /> : <Edit />}
                      </IconButton>
                    </div>
                  </td>
                  <td>
                    <div style={{ display: "flex", justifyContent: "center" }}>
                      <IconButton
                        onClick={() => {
                          deleteLaunchData(item.idlaunch_packages);
                        }}
                      >
                        <Delete />
                      </IconButton>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan="3">No data available</td>
              </tr>
            )}
          </tbody>
        </table>
      </DialogContent>
    </Dialog>
  );
};
class AddNewPackage extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    gCount: null,
    groupData: [],
    page: 0,
    popUpOpen: false,
    groupId: null,
    name: "",
    description: "",
    descriptionText: null,
    details: "",
    schedule: "",
    price: "",
    validity: "",
    groupOrder: "",
    syllabus: "",
    enabled: true,
    popupType: null,
    addLinkBtnClicked: false,
    btnTitle: "",
    btnLink: "",
    allUsersData: [],
    packagesAllList: [],
    selectedPackages: [],
    bucketButtonClicked: false,
    dragItem: null,
    dragOverItem: null,
    dragItemGid: null,
    dragOverItemGid: null,
    enableOrdering: false,
    allowNegativeMarking: true,
    isNormalPackage: "1",
    whatsapplink: "",
    addToExistingBucket: false,
    launchEdit: false,
  };

  componentDidMount() {
    this.getQbankCount();
    this.getData();
  }

  getQbankCount = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.get(
        `${commonData["api"]}/adminmasterdata/reportedCount`,
        { headers }
      );
      //   console.log(data.data[0][0].pageCount);
      this.setState({ isLoading: false, gCount: data.data[0][0].pageCount });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  getData = async () => {
    const { page } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getPackages",
      search: "",
      qid: page,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      const packagesList = await axios.get(
        `${commonData["api"]}/adminmasterdata/packagelist`,
        {
          headers,
        }
      );
      //   console.log(data);
      this.setState({
        isLoading: false,
        groupData: data.data[0],
        categoryList: data.data[1],
        packagesAllList: packagesList.data[0],
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  getSingleData = async () => {
    const { groupId } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getPackagesSingle",
      search: "",
      qid: groupId,
    };
    try {
      // this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data.data[0][0]);
      const e = data.data[0][0];
      this.setState({
        name: e.group_name,
        schedule: e.scheduleLink === null ? "" : e.scheduleLink,
        price: e.price,
        validity: e.valid_for_days,
        groupOrder: e.groupOrder,
        enabled: e.groupEnable,
        descriptionText: e.description,
        description: e.description,
        syllabus: e.syllabusLink,
        editorLoading: false,
        selectedPackages:
          e.bucket === "0" ? [] : e.bucket.split(",").map((e) => parseInt(e)),
        allowNegativeMarking: e.allowNegativeMarking == "1",
        isNormalPackage:
          e.isNormalPackage == "2" || e.isNormalPackage == "3"
            ? e.isNormalPackage
            : "1",
        whatsapplink: e.whatsapplink,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  deleteQuestion = async () => {
    const { groupId, groupData } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      schedule: "",
      description: "",
      name: "",
      price: 0,
      validity: 0,
      groupOrder: 1,
      groupId,
      enabled: 1,
      type: "DELETE",
      syllabus: "",
      selectedPackages: [],
      isNormalPackage: "1",
    };
    if (window.confirm("Do you really want to remove this package?")) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-group`,
          body,
          { headers }
        );
        //   console.log(data);
        this.setState({
          groupData: groupData.filter((e) => e.gid !== groupId),
        });
        NotificationManager.success(`Package Deleted Succesfully...`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };

  getPackageUserData = async (name, gid) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getPackageUserData",
      search: gid,
      qid: 0,
    };
    try {
      // this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data);
      this.setState({ allUsersData: data.data[0], isLoading: false }, () => {
        data.data[0].length > 0 &&
          ExportCSV({
            csvData: data.data[0],
            fileName: "All Users_" + name,
            type: "ExportAllGidUsers",
            title: "Download All users Again",
            emptyFn: () => {
              // this.setState({ allUsersData: [], exportdatapopup: false }, () =>
              //   window.location.reload(false)
              // );
            },
          });
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  dragStart = (gid, position) => {
    this.setState({ dragItem: position, dragItemGid: gid });
    // console.log(e.target.innerHTML);
  };

  dragEnter = (gid, position) => {
    this.setState({ dragOverItem: position, dragOverItemGid: gid });
  };
  updateOrder = async (newData) => {
    const { dragItem, dragOverItem, dragItemGid, dragOverItemGid } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "updatepacageOrder",
      search:
        dragOverItemGid +
        "$$" +
        dragItem +
        "_" +
        dragItemGid +
        "$$" +
        dragOverItem,
      qid: 0,
    };
    try {
      // this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      NotificationManager.list = [];
      NotificationManager.success(`Package Order Changed Succesfully...`);
      this.setState({
        dragItem: null,
        dragOverItem: null,
        groupData: newData,
        dragItemGid: null,
        dragOverItemGid: null,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({
        dragItem: null,
        dragOverItem: null,
        dragItemGid: null,
        dragOverItemGid: null,
      });
    }
  };
  drop = (e) => {
    const copyListItems = [...this.state.groupData];
    const dragItemContent = copyListItems[this.state.dragItem - 1];
    copyListItems.splice(this.state.dragItem - 1, 1);
    copyListItems.splice(this.state.dragOverItem - 1, 0, dragItemContent);
    this.updateOrder(copyListItems);
  };
  getQuizIdsInPackage = async (gid, type, packageName) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getQuizIdsInPackage",
      search: gid,
      qid: 0,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      if (type === "englishExamQuestions" || type === "allImageQuestions") {
        await this.downloadDocxFile(
          data.data[0][0].qids.toString(),
          type,
          packageName
        );
      } else {
        await this.downloadEExcelFile(
          data.data[0][0].qids.toString(),
          packageName
        );
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  downloadDocxFile = async (examId, type = "examQuestions", packageName) => {
    // let examName = document?.getElementById(examId + "examName")?.innerText;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    try {
      NotificationManager.success("Please wait...");
      const response = await axios.get(
        `${commonData["api"]}/download-questions/${type}/${examId}`,
        { headers, responseType: "blob" } // Set responseType to 'blob'
      );

      // Get the content disposition header to extract the filename
      const fileName = "Package_Questions_" + packageName + ".docx";

      // Create a blob URL and trigger a download link
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      });
      const blobUrl = URL.createObjectURL(blob);
      const downloadLink = document.createElement("a");
      downloadLink.href = blobUrl;
      downloadLink.download = fileName;
      downloadLink.click();

      // Clean up the blob URL
      URL.revokeObjectURL(blobUrl);
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };
  downloadEExcelFile = async (selectedQuid, packageName, type) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };

    try {
      NotificationManager.info("Processing, please wait...");
      let allQuestions = [];

      // Split the comma-separated quiz IDs
      const quizIds = selectedQuid.split(",");

      // Fetch data for each quiz ID
      for (const qid of quizIds) {
        const body = {
          type: "downloadTeluguQuestionsFromExam",
          search: qid,
          qid: 0,
        };

        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );

        // Get exam name from your examsData state
        // const examName = this.state.examsData.find(exam => exam.quid === parseInt(qid))?.quiz_name || qid;

        // Add exam name to each question
        const questionsWithExamName = data.data[0].map((question) => ({
          ...question,
          // exam_name: examName
        }));

        // Accumulate questions
        allQuestions = [...allQuestions, ...questionsWithExamName];
      }

      // Sort questions by exam name if needed
      // allQuestions.sort((a, b) => a.exam_name.localeCompare(b.exam_name));

      // Download combined data
      if (allQuestions.length > 0) {
        const filename =
          quizIds.length > 1
            ? `Multiple_Exams_Questions_${packageName}_${new Date()
                .toISOString()
                .slice(0, 10)}`
            : `${packageName}_Questions`;

        exportToCSVD(allQuestions, filename);
        NotificationManager.success(
          `Successfully downloaded ${allQuestions.length} questions`
        );
      } else {
        NotificationManager.warning("No questions found");
      }
    } catch (err) {
      console.error("Download error:", err);
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  packagesTable = () => {
    const { groupData, enableOrdering } = this.state;
    // console.log(groupData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv" style={{ overflowX: "scroll" }}>
        <style>{style}</style>
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            marginBottom: 10,
          }}
        >
          <h3>All Packages</h3>
          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  enableOrdering: !p.enableOrdering,
                }));
              }}
            >
              {!enableOrdering ? "Enable" : "Disable"} Ordering
            </Button>
          </div>
          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  popUpOpen: !p.popUpOpen,
                  popupType: "Add New Package",
                  name: "",
                  description: "",
                  details: "",
                  schedule: "",
                  price: "",
                  validity: "",
                  groupOrder: p.groupData.length + 1,
                  enabled: "",
                  descriptionText: "",
                  editorLoading: false,
                  syllabus: "",
                  selectedPackages: [],
                  allowNegativeMarking: false,
                  isNormalPackage: "1",
                }));
              }}
            >
              Add New Package
            </Button>
          </div>
        </div>
        <table>
          <thead>
            <tr>
              {/* <th>Order</th> */}
              <th>Name</th>
              <th>
                Registered
                <br /> Students
              </th>
              <th>Price</th>
              <th>Is Enabled</th>
              <th>Action</th>
            </tr>
          </thead>
          <tbody>
            {groupData.map((e, index) => {
              return (
                <tr
                  key={"packagesadd" + e.gid}
                  onDragStart={() => this.dragStart(e.gid, index + 1)}
                  onDragEnter={() => this.dragEnter(e.gid, index + 1)}
                  onDragEnd={this.drop}
                  // key={index}
                  draggable={enableOrdering}
                  style={{
                    border:
                      !e?.bucket || e.bucket === "0" || e.bucket === ""
                        ? ""
                        : "3px solid orange",
                  }}
                >
                  {/* <td style={{ textAlign: "center" }}>{e.groupOrder}</td> */}
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => {
                      navigator.clipboard.writeText(
                        `${commonData["app"]}/payumoney/payment/checkout/${
                          e.gid
                        }${
                          e.bucket !== "0"
                            ? "/aG9tZQ=="
                            : "/" + window.btoa(e.bucket)
                        }`
                      );
                      NotificationManager.success("Copied to ClipBoard");
                    }}
                  >
                    {e.gid + " ) " + e.group_name}
                    <i
                      className="bi bi-clipboard"
                      style={{ cursor: "pointer", marginLeft: 10 }}
                    ></i>
                  </td>
                  <td
                    style={{ textAlign: "center", cursor: "pointer" }}
                    onClick={() => this.getPackageUserData(e.group_name, e.gid)}
                  >
                    {e.usersCount}
                  </td>
                  {/*  */}
                  <td style={{ textAlign: "center" }}>{e.price}</td>
                  <td style={{ textAlign: "center" }}>
                    {e.groupEnable === 1 ? "Yes" : "No"}
                  </td>
                  <td style={{display: "flex",  flexWrap: "wrap", }}>
                    <i
                      className="bi bi-download"
                      style={{
                        cursor: "pointer",
                        marginTop: 15,
                        marginLeft: 15,
                        border: "2px solid orange",
                        padding: 5,
                      }}
                      onClick={() =>
                        this.getQuizIdsInPackage(e.gid, "tm", e.group_name)
                      }
                    >
                      {" "}
                      TM
                    </i>{" "}
                    <i
                      className="bi bi-download"
                      style={{
                        cursor: "pointer",
                        marginTop: 15,
                        marginLeft: 15,
                        border: "2px solid orange",
                        padding: 5,
                      }}
                      onClick={() =>
                        this.getQuizIdsInPackage(
                          e.gid,
                          "englishExamQuestions",
                          e.group_name
                        )
                      }
                    >
                      {" "}
                      BL Q
                    </i>{" "}
                    <i
                      className="bi bi-download"
                      style={{
                        cursor: "pointer",
                        marginTop: 15,
                        marginLeft: 15,
                        border: "2px solid orange",
                        padding: 5,
                      }}
                      onClick={() =>
                        this.getQuizIdsInPackage(
                          e.gid,
                          "allImageQuestions",
                          e.group_name
                        )
                      }
                    >
                      Lengthy Q
                    </i>
                  </td>
                  <td>
                    <i
                      className="bi bi-pencil-fill"
                      style={{ marginRight: 15, cursor: "pointer" }}
                      onClick={() => {
                        this.setState(
                          {
                            editorLoading: true,
                            groupId: e.gid,
                            popupType: "Edit Package",
                            popUpOpen: true,
                            isNormalPackage: "1",
                            allowNegativeMarking: false,
                          },
                          () => this.getSingleData()
                        );
                      }}
                    ></i>
                    <i
                      className="bi bi-trash-fill"
                      style={{ cursor: "pointer" }}
                      onClick={() =>
                        this.setState(
                          {
                            groupId: e.gid,
                          },
                          () => this.deleteQuestion()
                        )
                      }
                    ></i>
                    <div style={{ marginTop: 10 }}>
                      <Button
                        className="btn exportbtn"
                        onClick={() => {
                          this.setState(
                            {
                              launchEdit: true,
                              groupId: e.gid,
                            }
                            // () => this.getUserPackageData()
                          );
                        }}
                      >
                        Launch
                      </Button>
                    </div>
                    
                    <div style={{ marginTop: 12 }}>
                      <Button
                        // className="btn exportbtn"
                        style={{
                          backgroundColor: e.only_for_practice === "1" ? "green" : "orange",
                          color: "white",
                          fontSize: 12,
                        }}
                        onClick={() => this.togglePracticeFlag(e.gid, e.only_for_practice)}
                      >
                        {e.only_for_practice === "1" ? "Practice: ON" : "Practice: OFF"}
                      </Button>
                    </div>

                    {/*{`/payumoney/payment/checkout/${gid}${bucket !== "0" ? "/aG9tZQ==" : "/" + window.btoa(bucket)}*/}
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      name: "",
      description: "",
      details: "",
      schedule: "",
      price: "",
      validity: "",
      groupOrder: "",
      enabled: "",
      addLinkBtnClicked: false,
      btnTitle: "",
      btnLink: "",
      selectedPackages: [],
    }));
  };

  addBtntoDescription = () => {
    const { btnTitle, btnLink } = this.state;
    const newBtn = `<a href=${btnLink} target="_blank" style='margin-left:10px' ><button style="background-color: #4CAF50;border-radius:20px;color:white;border-width:0px;padding:10px;">${btnTitle}</button></a>`;
    this.setState((p) => ({
      descriptionText: p.descriptionText + newBtn,
      btnTitle: "",
      btnLink: "",
    }));
  };

  addLinkBtn = () => {
    const { addLinkBtnClicked, btnTitle, btnLink } = this.state;
    return (
      <>
        <div style={{ display: "flex", justifyContent: "flex-end" }}>
          <Button
            className="btn addbtn"
            style={{ color: "white" }}
            onClick={() =>
              this.setState((p) => ({
                addLinkBtnClicked: !p.addLinkBtnClicked,
              }))
            }
          >
            Add Link Button to Description
          </Button>
        </div>

        {addLinkBtnClicked && (
          <div
            style={{
              display: "flex",
              justifyContent: "space-around",
              marginTop: 15,
            }}
          >
            <input
              type={"text"}
              value={btnTitle}
              className="input-container-packages"
              onChange={(e) => this.setState({ btnTitle: e.target.value })}
              placeholder="Button Title"
              style={{ width: "40%" }}
            />
            <input
              type={"text"}
              value={btnLink}
              className="input-container-packages"
              onChange={(e) => this.setState({ btnLink: e.target.value })}
              placeholder="Button Link"
            />
            <div
              style={{ cursor: "pointer" }}
              onClick={this.addBtntoDescription}
            >
              <i className={"bi bi-plus-circle"}>Add</i>
            </div>
          </div>
        )}
      </>
    );
  };
  onChangePackageData = (e) => {
    this.setState({
      selectedPackages:
        typeof value === "string" ? e.target.value.split(",") : e.target.value,
    });
  };
  createBucket = () => {
    const { packagesAllList, selectedPackages, groupId } = this.state;
    // console.log(packagesAllList);
    return (
      <div style={{ paddingTop: 20, marginBottom: 10 }}>
        <label>
          <input
            type="checkbox"
            name="addToExistingBucket"
            style={{ marginRight: 10 }}
            checked={this.state.addToExistingBucket}
            onChange={this.handleCheckboxChange}
          />{" "}
          Add to existing bucket
        </label>
        <br />
        {this.state.addToExistingBucket
          ? "Add this package to existing bucket"
          : "Create New Package Bouqet"}
        <Select
          required
          id="Please-Select-District"
          className="input-box dis"
          // label="Please Select Package"
          value={selectedPackages}
          name="selectedPackages"
          multiple
          displayEmpty
          onChange={this.onChangePackageData}
          renderValue={(selectedPackages) => {
            if (selectedPackages.length === 0) {
              return <em>Please Select Package</em>;
            }

            return selectedPackages.join(", ");
          }}
          MenuProps={MenuProps}
          style={{ width: "100%" }}
        >
          <MenuItem disabled value="" className="attempt-option-select">
            Please Select Package
          </MenuItem>
          {packagesAllList
            .filter(
              (e) =>
                (this.state.addToExistingBucket
                  ? !(e.bucket == "0" || e.bucket == "")
                  : e.bucket == "0" || e.bucket == "") && e.gid !== groupId
            )
            .map((e, i) => (
              <MenuItem
                className="selectionbox"
                value={e.gid}
                key={"multiselect" + i}
              >
                <Checkbox checked={selectedPackages.indexOf(e.gid) > -1} />
                <ListItemText primary={e.group_name} />
              </MenuItem>
            ))}
        </Select>
      </div>
    );
  };

  packageEditor = () => {
    const {
      name,
      schedule,
      price,
      validity,
      groupOrder,
      descriptionText,
      popupType,
      isNormalPackage,
      editorLoading,
      whatsapplink,
    } = this.state;

    return (
      <>
        {popupType === "Edit Package" && !editorLoading && (
          <>
            <div style={{ margin: 4 }}>
              <input
                type={"checkbox"}
                value={isNormalPackage}
                id="isNormalPackage"
                onChange={(e) =>
                  this.setState((pr) => ({
                    isNormalPackage: "1",
                  }))
                }
                checked={isNormalPackage === "1"}
              />
              <label htmlFor="isNormalPackage" style={{ fontSize: 18 }}>
                {" "}
                Normal Package
              </label>
            </div>
            <div style={{ margin: 4 }}>
              <input
                type={"checkbox"}
                value={isNormalPackage}
                id="PDFNormalPackage"
                onChange={(e) =>
                  this.setState((pr) => ({
                    isNormalPackage: "2",
                  }))
                }
                checked={isNormalPackage === "2"}
              />
              <label htmlFor="PDFNormalPackage" style={{ fontSize: 18 }}>
                {" "}
                PDF Package
              </label>
            </div>
            <div style={{ margin: 4 }}>
              <input
                type={"checkbox"}
                value={isNormalPackage}
                id="VideoisNormalPackage"
                onChange={(e) =>
                  this.setState((pr) => ({
                    isNormalPackage: "3",
                  }))
                }
                checked={isNormalPackage === "3"}
              />
              <label htmlFor="VideoisNormalPackage" style={{ fontSize: 18 }}>
                Video
              </label>
            </div>
            <div>
              Package name <br />
              <input
                type={"text"}
                value={name}
                className="input-container-packages wne"
                onChange={(e) => this.setState({ name: e.target.value })}
              />
            </div>
            <div>
              Whatsapp Group Link <br />
              <input
                type={"text"}
                value={whatsapplink}
                className="input-container-packages wne"
                onChange={(e) =>
                  this.setState({ whatsapplink: e.target.value })
                }
              />
            </div>

            <div style={{ marginBottom: 10 }}>
              Description
              <div>{this.addLinkBtn()}</div>
              <Editor
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={descriptionText}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(ev) => this.setState({ description: ev })}
              />
            </div>
            <div className="input-packageeeditorcloum">
              {isNormalPackage === "1" && (
                <div>
                  Schedule Link <br />
                  <input
                    type={"text"}
                    value={schedule}
                    id="schedule"
                    className="input-container-packages insched"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
              )}
              {/* <div>
                Syllabus Link <br />
                <input
                  type={"text"}
                  value={syllabus}
                  id="syllabus"
                  className="input-container-packages insched"
                  onChange={(e) =>
                    this.setState({ [e.target.id]: e.target.value })
                  }
                />
              </div> */}

              <div className="input-packageeeditorrow">
                <div>
                  Price <br />
                  <input
                    type={"number"}
                    value={price}
                    id="price"
                    className="input-container-packages input-container-packages-down"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
                <div>
                  Validity in Days <br />
                  <input
                    type={"number"}
                    value={validity}
                    id="validity"
                    className="input-container-packages input-container-packages-down"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
                <div>
                  Group Order <br />
                  <input
                    type={"number"}
                    value={groupOrder}
                    id="groupOrder"
                    className="input-container-packages input-container-packages-down"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
              </div>
              <div>{this.createBucket()}</div>
            </div>
          </>
        )}
        {editorLoading && (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };

  AddpackageEditor = () => {
    const {
      name,
      schedule,
      price,
      validity,
      groupOrder,
      descriptionText,
      popupType,
      isNormalPackage,
      whatsapplink,
    } = this.state;

    return (
      <>
        {popupType === "Add New Package" && (
          <>
            <div style={{ margin: 4 }}>
              <input
                type={"checkbox"}
                value={isNormalPackage}
                id="isNormalPackage"
                onChange={(e) =>
                  this.setState((pr) => ({
                    isNormalPackage: "1",
                  }))
                }
                checked={isNormalPackage === "1"}
              />
              <label htmlFor="isNormalPackage" style={{ fontSize: 18 }}>
                {" "}
                Normal Package
              </label>
            </div>
            <div style={{ margin: 4 }}>
              <input
                type={"checkbox"}
                value={isNormalPackage}
                id="PDFNormalPackage"
                onChange={(e) =>
                  this.setState((pr) => ({
                    isNormalPackage: "2",
                  }))
                }
                checked={isNormalPackage === "2"}
              />
              <label htmlFor="PDFNormalPackage" style={{ fontSize: 18 }}>
                {" "}
                PDF Package
              </label>
            </div>
            <div style={{ margin: 4 }}>
              <input
                type={"checkbox"}
                value={isNormalPackage}
                id="VideoisNormalPackage"
                onChange={(e) =>
                  this.setState((pr) => ({
                    isNormalPackage: "3",
                  }))
                }
                checked={isNormalPackage === "3"}
              />
              <label htmlFor="VideoisNormalPackage" style={{ fontSize: 18 }}>
                Video
              </label>
            </div>
            <div>
              Package name <br />
              <input
                type={"text"}
                value={name}
                className="input-container-packages wne"
                onChange={(e) => this.setState({ name: e.target.value })}
              />
            </div>
            <div>
              Whatsapp Group Link <br />
              <input
                type={"text"}
                value={whatsapplink}
                className="input-container-packages wne"
                onChange={(e) =>
                  this.setState({ whatsapplink: e.target.value })
                }
              />
            </div>
            <div style={{ marginBottom: 10 }}>
              Description
              <div
                style={{
                  display: "flex",
                  justifyContent: "flex-end",
                }}
              >
                <div>{this.addLinkBtn()}</div>
              </div>
              <Editor
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={descriptionText}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(e) => this.setState({ description: e })}
              />
            </div>
            <div className="input-packageeeditorcloum">
              {isNormalPackage === "1" && (
                <div>
                  Schedule Link <br />
                  <input
                    type={"text"}
                    value={schedule}
                    id="schedule"
                    className="input-container-packages insched"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
              )}
              {/* <div>
                Syllabus Link <br />
                <input
                  type={"text"}
                  value={syllabus}
                  id="syllabus"
                  className="input-container-packages insched"
                  onChange={(e) =>
                    this.setState({ [e.target.id]: e.target.value })
                  }
                />
              </div> */}

              <div className="input-packageeeditorrow">
                <div>
                  Price <br />
                  <input
                    type={"number"}
                    value={price}
                    id="price"
                    className="input-container-packages input-container-packages-down"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
                <div>
                  Validity in Days <br />
                  <input
                    type={"number"}
                    value={validity}
                    id="validity"
                    className="input-container-packages input-container-packages-down"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
                <div>
                  Group Order <br />
                  <input
                    type={"number"}
                    value={groupOrder}
                    id="groupOrder"
                    className="input-container-packages input-container-packages-down"
                    onChange={(e) =>
                      this.setState({ [e.target.id]: e.target.value })
                    }
                  />
                </div>
              </div>
              <div>{this.createBucket()}</div>
            </div>
          </>
        )}
      </>
    );
  };

  savePackage = async () => {
    const {
      name,
      schedule,
      price,
      validity,
      groupOrder,
      description,
      groupId,
      enabled,
      groupData,
      syllabus,
      selectedPackages,
      allowNegativeMarking,
      isNormalPackage,
      whatsapplink,
      addToExistingBucket,
    } = this.state;

    // console.log(questionText, op1, op2, op3, op4, selectedCat, correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      schedule: isNormalPackage === "1" ? schedule : isNormalPackage,
      description: description,
      name: name + "$$$_" + selectedPackages.join(",") + "$$$_" + whatsapplink,
      price,
      validity,
      groupOrder,
      groupId,
      enabled: enabled ? 1 : 0,
      type:
        "EDITPACKAGE$$_$$" +
        (allowNegativeMarking ? 1 : 0) +
        "$$_$$" +
        addToExistingBucket,
      syllabus,
    };
    // console.log(body);
    if (name === "") {
      NotificationManager.error(`Please Enter Package Name`);
    } else if (description === "") {
      NotificationManager.error(`Please Enter Description`);
    } else if (price === "") {
      NotificationManager.error(`Please Enter Price`);
    } else if (validity === "") {
      NotificationManager.error(`Please Enter Validity`);
    }
    //  else if (schedule === "") {
    //   NotificationManager.error(`Please Enter Schedule Link`);
    // }
    else {
      try {
        this.setState({ editorLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-group`,
          body,
          { headers }
        );
        //   console.log(data);
        // const newQuestion = {
        //   group_name: name,
        //   price,
        //   valid_for_days: validity,
        //   groupOrder,
        //   gid: groupId,
        //   groupEnable: enabled ? 1 : 0,
        //   scheduleLink: schedule,
        //   descriptionText: description,
        //   syllabusLink: syllabus,
        //   allowNegativeMarking: allowNegativeMarking,
        //   bucket,
        // };
        // const index = groupData.findIndex(
        //   (ek) => ek == groupData.filter((e) => e.gid === groupId)[0]
        // );

        // const removedData = groupData.filter((e) => e.gid !== groupId);
        // const newData = removedData.splice(index, 0, newQuestion);
        // console.log(removedData);
        // console.log(newData);
        this.setState(
          {
            editorLoading: false,
            popUpOpen: false,
            name: "",
            description: "",
            details: "",
            schedule: "",
            price: "",
            validity: "",
            groupOrder: "",
            syllabus: "",
            enabled: true,
            // groupData: removedData,
            addLinkBtnClicked: false,
            btnTitle: "",
            btnLink: "",
            allowNegativeMarking: true,
            addToExistingBucket: false,
          },
          this.getData
        );
        NotificationManager.success(`Package Updated Successfully..`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ editorLoading: false });
      }
    }
  };

  addNewPackage = async () => {
    const {
      name,
      schedule,
      price,
      validity,
      groupOrder,
      description,
      groupId,
      enabled,
      selectedPackages,
      syllabus,
      allowNegativeMarking,
      isNormalPackage,
      whatsapplink,
      addToExistingBucket,
    } = this.state;

    // console.log(questionText, op1, op2, op3, op4, selectedCat, correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      schedule: isNormalPackage === "1" ? schedule : isNormalPackage,
      description: description,
      name: name + "$$$_" + selectedPackages.join(",") + "$$$_" + whatsapplink,
      price,
      validity,
      groupOrder,
      groupId,
      enabled: enabled ? 1 : 0,
      type:
        "ADDPACKAGE$$_$$" +
        (allowNegativeMarking ? 0 : 1) +
        "$$_$$" +
        addToExistingBucket,
      syllabus,
    };
    // console.log(body);
    if (name === "") {
      NotificationManager.error(`Please Enter Package Name`);
    } else if (description === "") {
      NotificationManager.error(`Please Enter Description`);
    } else if (price === "") {
      NotificationManager.error(`Please Enter Price`);
    } else if (validity === "") {
      NotificationManager.error(`Please Enter Validity`);
    } else if (schedule === "" && isNormalPackage === "1") {
      NotificationManager.error(`Please Enter Schedule Link`);
    } else if (groupOrder === "") {
      NotificationManager.error(`Please Enter group Order`);
    } else {
      try {
        this.setState({ editorLoading: true });

        const data = await axios.post(
          `${commonData["api"]}/admin/add-edit-group`,
          body,
          { headers }
        );
        // console.log(data.data[0][0].groupId);

        // console.log(group);
        this.setState(
          {
            editorLoading: false,
            popUpOpen: false,
            name: "",
            description: "",
            details: "",
            schedule: "",
            price: "",
            validity: "",
            groupOrder: "",
            syllabus: "",
            enabled: true,
            addLinkBtnClicked: false,
            btnTitle: "",
            btnLink: "",
            allowNegativeMarking: true,
            addToExistingBucket: false,
          },
          this.getData
        );
        NotificationManager.success(`Package Updated Successfully..`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ editorLoading: false });
      }
    }
  };

  handleCheckboxChange = (event) => {
    const target = event.target;
    const value = target.checked;

    this.setState({
      addToExistingBucket: value,
    });
  };
  renderPopUp = () => {
    const { popUpOpen, popupType, enabled, allowNegativeMarking } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"md"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
            <Button
              style={{ backgroundColor: "red", color: "#fff" }}
              onClick={this.handleOpen}
            >
              X
            </Button>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {popupType !== "Add New Package"
            ? this.packageEditor()
            : this.AddpackageEditor()}
        </DialogContent>
        <DialogActions className="packagepopup">
          <div>
            <input
              type={"checkbox"}
              value={enabled}
              id="enabled"
              onChange={(e) =>
                this.setState((pr) => ({ enabled: !pr.enabled }))
              }
              checked={enabled}
            />
            <label htmlFor="enabled" style={{ fontSize: 15 }}>
              {" "}
              Enable/Disable in Homepage
            </label>
          </div>
          <div>
            <input
              type={"checkbox"}
              value={allowNegativeMarking}
              id="allowNegativeMarking"
              onChange={(e) =>
                this.setState((pr) => ({
                  allowNegativeMarking: !pr.allowNegativeMarking,
                }))
              }
              checked={allowNegativeMarking}
            />
            <label htmlFor="allowNegativeMarking" style={{ fontSize: 15 }}>
              {" "}
              Negative Marking
            </label>
          </div>
          <div className="packagepopup">
            <Button
              className="btn header-btns attemptbtn attempt-btns submit popbtn"
              onClick={this.handleOpen}
            >
              Cancel
            </Button>
            <Button
              className="btn header-btns attemptbtn attempt-btns popbtn"
              onClick={
                popupType !== "Add New Package"
                  ? this.savePackage
                  : this.addNewPackage
              }
            >
              Save changes
            </Button>
          </div>
        </DialogActions>
      </Dialog>
    );
  };

  togglePracticeFlag = async (groupId, currentStatus) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    
    const body = {
      type: "updatePracticeFlag",
      search: currentStatus === "1" ? "0" : "1",
      qid: groupId,
     };
    
    try {
      // this.setState({ isLoading: true });
      await axios.post(`${commonData["api"]}/admin/qbankdata`, body, { headers });
      
      // Update the UI to reflect the change
      this.setState(prevState => ({
        groupData: prevState.groupData.map(item => {
          if (item.gid === groupId) {
            return {
              ...item,
              only_for_practice: item.only_for_practice  === "1" ? "0" : "1"
            };
          }
          return item;
        }),
        isLoading: false
      }));
      
      NotificationManager.success(`Practice flag updated successfully`);
    } catch (err) {
      NotificationManager.error(`Failed to update practice flag`);
      this.setState({ isLoading: false });
    }
  };

  render() {
    const { isLoading, login, popUpOpen, launchEdit, groupId } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />

              <div className="viewresultsdesktop admin">
                {this.packagesTable()}
                {popUpOpen && this.renderPopUp()}
                {launchEdit && (
                  <RenderDatesPopup
                    gid={groupId}
                    onClose={() =>
                      this.setState({ groupId: null, launchEdit: false })
                    }
                    data={[
                      {
                        idlaunch_packages: 1,
                        date: "2000-05-20",
                        createdOn: "2000-05-20",
                        gid: "175",
                      },
                    ]}
                  />
                )}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default AddNewPackage;
