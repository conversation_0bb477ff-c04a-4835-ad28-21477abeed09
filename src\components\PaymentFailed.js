import Failed from "./failed.png";
import "./styles.css";
import React from "react";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import { withRouter } from "react-router";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
class PaymentFailed extends React.Component {
  componentDidMount() {
    const { location } = this.props;
    const { search } = location;
    if (search !== "") {
      NotificationManager.error("Payment Failed ..Try Again");
    }
    console.log(this.props);
  }
  render() {
    return (
      <div className="not-found-div">
        <img
          src={Failed}
          className="not-found-img paymentfailedimg"
          alt="not-found-image"
          // style={{ width: "100vw" }}
        />
        <Link to="/" className="linkto">
          <Button variant="contained" className="btn" style={{ marginTop: 20 }}>
            Go to HomePage
          </Button>
        </Link>
        <div>
          <NotificationContainer />
        </div>
      </div>
    );
  }
}
export default withRouter(PaymentFailed);
