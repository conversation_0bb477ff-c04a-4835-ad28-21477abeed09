import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import <PERSON>ie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";

import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import EditorNew from "./Editor";

class ReportedQuestions extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    qCount: null,
    qReportData: [],
    page: "0",
    popUpOpen: false,
    qNum: null,
    category: null,
    questionText: "",
    op1: "",
    op2: "",
    op3: "",
    op4: "",
    op1Num: "",
    op2Num: "",
    op3Num: "",
    op4Num: "",
    op5: "",
    op5Num: 0,
    correctOption: "",
    selectedCat: "",
    categoryList: [],
    editorLoading: false,
    optionsData: [],
    quizId: "",
    userImages: "",
    bonus: true,
    addOptionClicked: false,
    verifiedData: [],
    expandedReportedQuizzes: {}, // Track which reported quiz groups are expanded
    expandedVerifiedQuizzes: {}, // Track which verified quiz groups are expanded
    reportedSortBy: "date_desc", // Default sort by quiz name
    verifiedSortBy: "quiz_name", // Default sort by quiz name

    // Pagination for reported quiz groups
    reportedCurrentPage: 1,
    reportedItemsPerPage: 10,

    // Pagination for verified quiz groups
    verifiedCurrentPage: 1,
    verifiedItemsPerPage: 10,
  };

  componentDidMount() {
    // this.getQbankCount();
    // this.getData();
    const { match, from } = this.props;

    if (from === "examslist") {
      this.getQbankCount();
      this.getData();
      return;
    }
    const { params } = match;
    const { id, quizid } = params;
    if (quizid === "0" && id === "0") {
      this.getQbankCount();
      this.getData();
    } else {
      // console.log("", id, quizid);
      this.setState(
        {
          qNum: id,
          popUpOpen: true,
          editorLoading: true,
          quizId: quizid,
        },
        async () => {
          await this.getQbankCount();
          await this.getData();
          await this.getOptions();
        }
      );
    }
  }

  getQbankCount = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    try {
      this.setState({ isLoading: true });
      const data = await axios.get(
        `${commonData["api"]}/adminmasterdata/reportedCount`,
        { headers }
      );
      //   console.log(data.data[0][0].pageCount);
      this.setState({ isLoading: false, qCount: data.data[0][0].pageCount });
    } catch (err) {
      console.log(err);
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  getData = async () => {
    const { page } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: this.props.from === "examslist" ? "reported10" : "reported",
      search: "",
      qid: page,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      //   console.log(data);
      this.setState({
        qReportData: data.data[0].filter((e) => e.verified === 0),
        categoryList: data.data[1],
        isLoading: false,
        verifiedData: data.data[0].filter((e) => e.verified === 1),
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  getOptions = async () => {
    const { qNum, quizId } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "getOptions",
      search: quizId,
      qid: qNum,
    };
    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      // console.log(data.data[2][0]);
      this.setState({
        optionsData: data.data[0],
        editorLoading: false,
        correctOption: data.data[1][0].selectedOp,
        selectedCat: data.data[2][0].selectedCategory,
        op1: data.data[0].length >= 1 ? data.data[0][0].q_option : "",
        op2: data.data[0].length >= 2 ? data.data[0][1].q_option : "",
        op3: data.data[0].length >= 3 ? data.data[0][2].q_option : "",
        op4: data.data[0].length >= 4 ? data.data[0][3].q_option : "",
        op5: data.data[0].length >= 5 ? data.data[0][4].q_option : "",
        op1Num: data.data[0].length >= 1 ? data.data[0][0].oid : 0,
        op2Num: data.data[0].length >= 2 ? data.data[0][1].oid : 0,
        op3Num: data.data[0].length >= 3 ? data.data[0][2].oid : 0,
        op4Num: data.data[0].length >= 4 ? data.data[0][3].oid : 0,
        op5Num: data.data[0].length >= 5 ? data.data[0][4].oid : 0,
        question: data.data[2][0].question,
        questionText: data.data[2][0].question,
        userImages: data.data[3][0].reported,
      });
      // console.log(data);
    } catch (err) {
      console.log(err);
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  deleteOption = async () => {
    const { oid } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "deleteOption",
      search: "",
      qid: oid,
    };
    if (window.confirm("Do you really want to remove entry?")) {
      try {
        await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
          headers,
        });
        /*await this.getData();
        this.getOptions();*/
        this.getOptions();

        NotificationManager.success(`Option Deleted Succesfully...`);
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
        this.setState({ isLoading: false });
      }
    }
  };

  // Helper function to group questions by quiz_name
  groupQuestionsByQuiz = (questions) => {
    const groupedQuestions = {};

    questions.forEach((question) => {
      const quizName = question.quiz_name || "Uncategorized";

      if (!groupedQuestions[quizName]) {
        groupedQuestions[quizName] = [];
      }

      groupedQuestions[quizName].push(question);
    });

    return groupedQuestions;
  };

  // Toggle the expanded state of a reported quiz group
  toggleReportedQuizExpand = (quizName) => {
    this.setState((prevState) => ({
      expandedReportedQuizzes: {
        ...prevState.expandedReportedQuizzes,
        [quizName]: !prevState.expandedReportedQuizzes[quizName],
      },
    }));
  };

  // Toggle the expanded state of a verified quiz group
  toggleVerifiedQuizExpand = (quizName) => {
    this.setState((prevState) => ({
      expandedVerifiedQuizzes: {
        ...prevState.expandedVerifiedQuizzes,
        [quizName]: !prevState.expandedVerifiedQuizzes[quizName],
      },
    }));
  };

  // Handle sorting change for reported questions
  handleReportedSortChange = (sortBy) => {
    this.setState({
      reportedSortBy: sortBy,
      reportedCurrentPage: 1, // Reset to first page when sorting changes
    });
  };

  // Handle sorting change for verified questions
  handleVerifiedSortChange = (sortBy) => {
    this.setState({
      verifiedSortBy: sortBy,
      verifiedCurrentPage: 1, // Reset to first page when sorting changes
    });
  };

  // Handle page change for reported questions
  handleReportedPageChange = (pageNumber) => {
    this.setState({ reportedCurrentPage: pageNumber });
  };

  // Handle page change for verified questions
  handleVerifiedPageChange = (pageNumber) => {
    this.setState({ verifiedCurrentPage: pageNumber });
  };

  // Handle items per page change for reported questions
  handleReportedItemsPerPageChange = (itemsPerPage) => {
    this.setState({
      reportedItemsPerPage: itemsPerPage,
      reportedCurrentPage: 1, // Reset to first page when items per page changes
    });
  };

  // Handle items per page change for verified questions
  handleVerifiedItemsPerPageChange = (itemsPerPage) => {
    this.setState({
      verifiedItemsPerPage: itemsPerPage,
      verifiedCurrentPage: 1, // Reset to first page when items per page changes
    });
  };

  // Get paginated data for a quiz group
  getPaginatedQuizData = (quizData, currentPage, itemsPerPage) => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return quizData.slice(startIndex, endIndex);
  };

  // Render pagination component
  renderPagination = (
    totalItems,
    currentPage,
    itemsPerPage,
    onPageChange,
    onItemsPerPageChange,
    isVerified = false
  ) => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startItem = (currentPage - 1) * itemsPerPage + 1;
    const endItem = Math.min(currentPage * itemsPerPage, totalItems);

    // Generate page numbers to display
    const pageNumbers = [];
    const maxPagesToShow = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = startPage + maxPagesToShow - 1;

    if (endPage > totalPages) {
      endPage = totalPages;
      startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    const buttonClass = isVerified
      ? "pagination-button pagination-button-verified"
      : "pagination-button";

    return (
      <div className="pagination-container">
        <div className="pagination-controls">
          <button
            className={buttonClass}
            onClick={() => onPageChange(1)}
            disabled={currentPage === 1}
          >
            <i className="bi bi-chevron-double-left"></i>
          </button>

          <button
            className={buttonClass}
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
          >
            <i className="bi bi-chevron-left"></i>
          </button>

          {pageNumbers.map((number) => (
            <button
              key={number}
              className={`${buttonClass} ${
                currentPage === number ? "active" : ""
              }`}
              onClick={() => onPageChange(number)}
            >
              {number}
            </button>
          ))}

          <button
            className={buttonClass}
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
          >
            <i className="bi bi-chevron-right"></i>
          </button>

          <button
            className={buttonClass}
            onClick={() => onPageChange(totalPages)}
            disabled={currentPage === totalPages}
          >
            <i className="bi bi-chevron-double-right"></i>
          </button>
        </div>

        <div className="pagination-info">
          {totalItems > 0 ? (
            <span>
              Showing {startItem} to {endItem} of {totalItems} items
            </span>
          ) : (
            <span>No items to display</span>
          )}
        </div>

        <div className="items-per-page">
          <span className="items-per-page-label">Items per page:</span>
          <select
            className="items-per-page-select"
            value={itemsPerPage}
            onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          >
            <option value={5}>5</option>
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
          </select>
        </div>
      </div>
    );
  };

  // Sort questions based on the selected sort option
  sortQuestions = (questions, sortBy) => {
    const sortedQuestions = [...questions];

    switch (sortBy) {
      case "date_asc":
        return sortedQuestions.sort((a, b) => {
          // Use created_at field for date sorting
          const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
          const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
          return dateA - dateB;
        });
      case "date_desc":
        return sortedQuestions.sort((a, b) => {
          // Use created_at field for date sorting
          const dateA = a.created_at ? new Date(a.created_at) : new Date(0);
          const dateB = b.created_at ? new Date(b.created_at) : new Date(0);
          return dateB - dateA;
        });
      case "quiz_name":
        return sortedQuestions.sort((a, b) => {
          const quizNameA = a.quiz_name || "Uncategorized";
          const quizNameB = b.quiz_name || "Uncategorized";
          return quizNameA.localeCompare(quizNameB);
        });
      default:
        return sortedQuestions;
    }
  };

  reportedQuestionsTable = () => {
    const {
      qReportData,
      expandedReportedQuizzes,
      reportedSortBy,
      reportedCurrentPage,
      reportedItemsPerPage,
    } = this.state;

    const sortedQuestions = this.sortQuestions(qReportData, reportedSortBy);
    const groupedQuestions = this.groupQuestionsByQuiz(sortedQuestions);

    // Get quiz groups as an array for pagination
    const quizGroups = Object.entries(groupedQuestions);

    // Apply pagination to quiz groups
    const startIndex = (reportedCurrentPage - 1) * reportedItemsPerPage;
    const endIndex = startIndex + reportedItemsPerPage;
    const paginatedQuizGroups = quizGroups.slice(startIndex, endIndex);

    const style = `
      /* Modern table styling */
      table {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        margin-top: 10px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      td, th {
        text-align: left;
        padding: 12px 16px;
        border-bottom: 1px solid #e0e0e0;
      }

      th {
        background-color: #f2f2f2;
        color: #333333;
        font-size: 14px;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-weight: normal;
      }

      tr:nth-child(even) {
        background-color: #ffffff;
        color: #333333;
      }

      tr:nth-child(odd) {
        background-color: #f9f9f9;
        color: #333333;
      }

      tr:hover {
        background-color: #f0f0f0;
      }

      /* Last row without bottom border */
      tr:last-child td {
        border-bottom: none;
      }

      /* Quiz group header styling */
      .quiz-header {
        background-color: #f5f5f5;
        color: #333333;
        padding: 12px 16px;
        margin-top: 24px;
        margin-bottom: 8px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .quiz-header:hover {
        background-color: #e8e8e8;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .quiz-count {
        background-color: #4caf50; /* Green */
        color: white;
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 13px;
        min-width: 28px;
        text-align: center;
      }

      .chevron {
        margin-right: 12px;
        transition: transform 0.3s;
        color: #666666;
        font-size: 16px;
      }

      .chevron-down {
        transform: rotate(0deg);
      }

      .chevron-up {
        transform: rotate(180deg);
      }

      /* Sort options styling */
      .sort-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .section-title {
        font-size: 20px;
        color: #333333;
        margin: 0;
        display: flex;
        align-items: center;
      }

      .question-count {
        background-color: #ff9800; /* Orange */
        color: white;
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 14px;
        margin-left: 12px;
      }

      .sort-options {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
      }

      .sort-label {
        margin-right: 12px;
        color: #666666;
        font-size: 14px;
      }

      .sort-select {
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #cccccc;
        background-color: white;
        color: #333333;
        cursor: pointer;
        font-size: 14px;
        outline: none;
        transition: all 0.2s;
      }

      .sort-select:hover, .sort-select:focus {
        border-color: #999999;
        background-color: #f9f9f9;
      }

      .sort-select option {
        background-color: white;
        color: #333333;
        padding: 8px;
      }

      /* Action buttons styling */
      .action-buttons {
        display: flex;
        justify-content: center;
        gap: 12px;
      }

      .action-button {
        background-color: transparent;
        border: none;
        border-radius: 6px;
        padding: 6px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .edit-button {
        color: #2196F3; /* Blue */
      }

      .edit-button:hover {
        background-color: rgba(33, 150, 243, 0.1);
        color: #1976D2;
      }

      .delete-button {
        color: #F44336; /* Red */
      }

      .delete-button:hover {
        background-color: rgba(244, 67, 54, 0.1);
        color: #D32F2F;
      }

      .action-icon {
        font-size: 16px;
      }

      /* Reported answers styling */
      .reported-answer {
        background-color: #f9f9f9;
        border-radius: 8px;
        color: #333333;
        padding: 10px 12px;
        margin-bottom: 8px;
        width: 100%;
        font-size: 14px;
        cursor: pointer;
        border: 1px solid #e0e0e0;
        transition: all 0.2s;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .reported-answer:hover {
        background-color: #f0f0f0;
        border-color: #cccccc;
      }

      .answer-count {
        background-color: #ff9800; /* Orange */
        color: white;
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      /* Empty state styling */
      .empty-state {
        text-align: center;
        margin: 40px auto;
        color: #666666;
        background-color: #f9f9f9;
        padding: 24px;
        border-radius: 8px;
        border: 1px dashed #cccccc;
        max-width: 400px;
      }

      .empty-state-icon {
        font-size: 32px;
        margin-bottom: 16px;
        color: #999999;
      }

      /* Pagination styling */
      .pagination-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 20px;
        padding: 10px;
        background-color: #f5f5f5;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
      }

      .pagination-controls {
        display: flex;
        align-items: center;
      }

      .pagination-button {
        background-color: white;
        border: 1px solid #cccccc;
        color: #333333;
        padding: 6px 12px;
        margin: 0 4px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
        font-size: 14px;
      }

      .pagination-button:hover {
        background-color: #f0f0f0;
        border-color: #999999;
      }

      .pagination-button:disabled {
        background-color: #f9f9f9;
        color: #cccccc;
        cursor: not-allowed;
      }

      .pagination-button.active {
        background-color: #ff9800;
        color: white;
        border-color: #ff9800;
      }

      .pagination-info {
        color: #666666;
        font-size: 14px;
      }

      .items-per-page {
        display: flex;
        align-items: center;
      }

      .items-per-page-label {
        margin-right: 8px;
        color: #666666;
        font-size: 14px;
      }

      .items-per-page-select {
        padding: 6px 10px;
        border-radius: 4px;
        border: 1px solid #cccccc;
        background-color: white;
        color: #333333;
        font-size: 14px;
        cursor: pointer;
      }

      /* Verified pagination styling */
      .pagination-button-verified.active {
        background-color: #9C27B0;
        color: white;
        border-color: #9C27B0;
      }

      /* Question content styling */
      .question-content {
        display: flex;
        align-items: flex-start;
      }

      .question-number {
        color: #999999;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .question-text {
        flex-grow: 1;
      }

      /* Given answer styling */
      .given-answer {
        background-color: #f5f5f5;
        border-radius: 6px;
        padding: 8px 12px;
        border-left: 3px solid #2196F3; /* Blue */
      }
    `;

    return (
      <div
        className="paiduserdiv"
        style={{ overflow: "auto", padding: "16px" }}
      >
        <style>{style}</style>

        <div className="sort-container">
          <h2 className="section-title">
            Reported Questions
            <span className="question-count">{this.state.qReportData.length}</span>
          </h2>
          <h2 className="section-title-verified">
            Exams
            <span className="question-count-verified">{quizGroups.length}</span>
          </h2>

          <div className="sort-options">
            <span className="sort-label">Sort by:</span>
            <select
              className="sort-select"
              value={reportedSortBy}
              onChange={(e) => this.handleReportedSortChange(e.target.value)}
            >
              <option value="quiz_name">Exam Name</option>
              <option value="date_asc">Date (Oldest First)</option>
              <option value="date_desc">Date (Newest First)</option>
            </select>
          </div>
        </div>

        {quizGroups.length > 0 ? (
          <>
            {/* Render paginated quiz groups */}
            {paginatedQuizGroups.map(([quizName, questions]) => {
              const isExpanded = expandedReportedQuizzes[quizName] || false;

              return (
                <div key={`quiz-group-${quizName}`}>
                  <div
                    className="quiz-header"
                    onClick={() => this.toggleReportedQuizExpand(quizName)}
                  >
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <i
                        className={`bi bi-chevron-down ${
                          isExpanded ? "chevron-up" : "chevron-down"
                        } chevron`}
                      ></i>
                      <span>{quizName}</span>
                    </div>
                    <span className="quiz-count">{questions.length}</span>
                  </div>

                  {isExpanded && (
                    <table>
                      <thead>
                        <tr>
                          <th>Reported Question</th>
                          <th>Reported Answers</th>
                          <th>Given Answer</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {questions.map((e) => {
                          return (
                            <tr key={"reported" + e.questionid}>
                              <td style={{ width: "40%" }}>
                                <div className="question-content">
                                  <span className="question-number">
                                    {e.questionid})
                                  </span>
                                  <div
                                    className="question-text"
                                    dangerouslySetInnerHTML={{
                                      __html: e.question,
                                    }}
                                  ></div>
                                </div>
                              </td>
                              <td>
                                <div
                                  style={{
                                    display: "flex",
                                    flexDirection: "column",
                                    width: "100%",
                                  }}
                                >
                                  {e.reported?.split(",").map((v) => (
                                    <div
                                      key={e.questionid + v}
                                      className="reported-answer"
                                      onClick={() => {
                                        navigator.clipboard.writeText(
                                          `https://api.whatsapp.com/send?phone=91${v.slice(
                                            0,
                                            10
                                          )}&amp;text=Hai`
                                        );
                                        NotificationManager.success(
                                          "Copied to ClipBoard"
                                        );
                                      }}
                                    >
                                      <span>{v.slice(0, v.length - 1)}</span>
                                      <span className="answer-count">
                                        {v.slice(v.length - 1)}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </td>
                              <td>
                                <div
                                  className="given-answer"
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      e.givenAnswer
                                        ?.split("$;")
                                        ?.filter(
                                          (ev) => ev?.split("$$")[1] === "1"
                                        ).length > 0
                                        ? e.givenAnswer
                                            ?.split("$;")
                                            .filter(
                                              (ev) => ev?.split("$$")[1] === "1"
                                            )[0]
                                            ?.split("$$")[0]
                                        : "No answer provided",
                                  }}
                                ></div>
                              </td>

                              <td>
                                <div className="action-buttons">
                                  <button
                                    className="action-button edit-button"
                                    onClick={() =>
                                      this.setState(
                                        {
                                          qNum: e.questionid,
                                          popUpOpen: true,
                                          editorLoading: true,
                                          quizId: e.quid,
                                          userImages: "",
                                        },
                                        () => this.getOptions()
                                      )
                                    }
                                  >
                                    <i className="bi bi-pencil-fill action-icon"></i>
                                  </button>
                                  <button
                                    className="action-button delete-button"
                                    onClick={() =>
                                      this.setState(
                                        {
                                          qNum: e.questionid,
                                        },
                                        () => this.deleteQuestion("report")
                                      )
                                    }
                                  >
                                    <i className="bi bi-trash-fill action-icon"></i>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  )}
                </div>
              );
            })}

            {/* Pagination for quiz groups */}
            {this.renderPagination(
              quizGroups.length,
              reportedCurrentPage,
              reportedItemsPerPage,
              this.handleReportedPageChange,
              this.handleReportedItemsPerPageChange
            )}
          </>
        ) : (
          <div className="empty-state">
            <i className="bi bi-inbox empty-state-icon"></i>
            <p>No reported questions are available.</p>
          </div>
        )}
      </div>
    );
  };

  verifiedQuestionsTable = () => {
    const {
      verifiedData,
      expandedVerifiedQuizzes,
      verifiedSortBy,
      verifiedCurrentPage,
      verifiedItemsPerPage,
    } = this.state;

    const sortedQuestions = this.sortQuestions(verifiedData, verifiedSortBy);
    const groupedQuestions = this.groupQuestionsByQuiz(sortedQuestions);

    // Get quiz groups as an array for pagination
    const quizGroups = Object.entries(groupedQuestions);

    // Apply pagination to quiz groups
    const startIndex = (verifiedCurrentPage - 1) * verifiedItemsPerPage;
    const endIndex = startIndex + verifiedItemsPerPage;
    const paginatedQuizGroups = quizGroups.slice(startIndex, endIndex);

    const style = `
      /* Modern table styling */
      table {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        border-collapse: separate;
        border-spacing: 0;
        width: 100%;
        margin-top: 10px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      td, th {
        text-align: left;
        padding: 12px 16px;
        border-bottom: 1px solid #e0e0e0;
      }

      th {
        background-color: #f2f2f2;
        color: #333333;
        font-size: 14px;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-weight: normal;
      }

      tr:nth-child(even) {
        background-color: #ffffff;
        color: #333333;
      }

      tr:nth-child(odd) {
        background-color: #f9f9f9;
        color: #333333;
      }

      tr:hover {
        background-color: #f0f0f0;
      }

      /* Last row without bottom border */
      tr:last-child td {
        border-bottom: none;
      }

      /* Quiz group header styling */
      .quiz-header-verified {
        background-color: #f5f5f5;
        color: #333333;
        padding: 12px 16px;
        margin-top: 24px;
        margin-bottom: 8px;
        border-radius: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 1px solid #e0e0e0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
      }

      .quiz-header-verified:hover {
        background-color: #e8e8e8;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      .quiz-count-verified {
        background-color: #9C27B0; /* Purple */
        color: white;
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 13px;
        min-width: 28px;
        text-align: center;
      }

      .chevron-verified {
        margin-right: 12px;
        transition: transform 0.3s;
        color: #666666;
        font-size: 16px;
      }

      .chevron-down {
        transform: rotate(0deg);
      }

      .chevron-up {
        transform: rotate(180deg);
      }

      /* Sort options styling */
      .sort-container-verified {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .section-title-verified {
        font-size: 20px;
        color: #333333;
        margin: 0;
        display: flex;
        align-items: center;
      }

      .question-count-verified {
        background-color: #9C27B0; /* Purple */
        color: white;
        border-radius: 20px;
        padding: 4px 12px;
        font-size: 14px;
        margin-left: 12px;
      }

      .sort-options-verified {
        display: flex;
        align-items: center;
        background-color: #f5f5f5;
        padding: 8px 16px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
      }

      .sort-label-verified {
        margin-right: 12px;
        color: #666666;
        font-size: 14px;
      }

      .sort-select-verified {
        padding: 8px 12px;
        border-radius: 6px;
        border: 1px solid #cccccc;
        background-color: white;
        color: #333333;
        cursor: pointer;
        font-size: 14px;
        outline: none;
        transition: all 0.2s;
      }

      .sort-select-verified:hover, .sort-select-verified:focus {
        border-color: #999999;
        background-color: #f9f9f9;
      }

      .sort-select-verified option {
        background-color: white;
        color: #333333;
        padding: 8px;
      }

      /* Action buttons styling */
      .action-buttons-verified {
        display: flex;
        justify-content: center;
      }

      .action-button-verified {
        background-color: transparent;
        border: none;
        border-radius: 6px;
        padding: 6px;
        cursor: pointer;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .delete-button-verified {
        color: #F44336; /* Red */
      }

      .delete-button-verified:hover {
        background-color: rgba(244, 67, 54, 0.1);
        color: #D32F2F;
      }

      .action-icon-verified {
        font-size: 16px;
      }

      /* Question content styling */
      .question-content-verified {
        display: flex;
        align-items: flex-start;
      }

      .question-number-verified {
        color: #999999;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .question-text-verified {
        flex-grow: 1;
      }

      /* Given answer styling */
      .verified-answer {
        background-color: #f5f5f5;
        border-radius: 6px;
        padding: 8px 12px;
        border-left: 3px solid #9C27B0; /* Purple */
      }

      /* Empty state styling */
      .empty-state-verified {
        text-align: center;
        margin: 40px auto;
        color: #666666;
        background-color: #f9f9f9;
        padding: 24px;
        border-radius: 8px;
        border: 1px dashed #cccccc;
        max-width: 400px;
      }
    `;

    return (
      <div
        className="paiduserdiv"
        style={{ overflow: "auto", padding: "16px" }}
      >
        <style>{style}</style>

        <div className="sort-container-verified">
          <h2 className="section-title-verified">
            Verified Questions
            <span className="question-count-verified">
              {verifiedData.length}
            </span>
          </h2>
          {/* Total Exams count */}
          <h2 className="section-title-verified">
            Exams
            <span className="question-count-verified">{quizGroups.length}</span>
          </h2>

          <div className="sort-options-verified">
            <span className="sort-label-verified">Sort by:</span>
            <select
              className="sort-select-verified"
              value={verifiedSortBy}
              onChange={(e) => this.handleVerifiedSortChange(e.target.value)}
            >
              <option value="quiz_name">Exam Name</option>
              <option value="date_asc">Date (Oldest First)</option>
              <option value="date_desc">Date (Newest First)</option>
            </select>
          </div>
        </div>

        {quizGroups.length > 0 ? (
          <>
            {/* Render paginated quiz groups */}
            {paginatedQuizGroups.map(([quizName, questions]) => {
              const isExpanded = expandedVerifiedQuizzes[quizName] || false;

              return (
                <div key={`verified-quiz-group-${quizName}`}>
                  <div
                    className="quiz-header-verified"
                    onClick={() => this.toggleVerifiedQuizExpand(quizName)}
                  >
                    <div style={{ display: "flex", alignItems: "center" }}>
                      <i
                        className={`bi bi-chevron-down ${
                          isExpanded ? "chevron-up" : "chevron-down"
                        } chevron-verified`}
                      ></i>
                      <span>{quizName}</span>
                    </div>
                    <span className="quiz-count-verified">
                      {questions.length}
                    </span>
                  </div>

                  {isExpanded && (
                    <table>
                      <thead>
                        <tr>
                          <th>Question</th>
                          <th>Verified Answer</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {questions.map((e) => {
                          return (
                            <tr key={"verified" + e.questionid}>
                              <td style={{ width: "50%" }}>
                                <div className="question-content-verified">
                                  <span className="question-number-verified">
                                    {e.questionid})
                                  </span>
                                  <div
                                    className="question-text-verified"
                                    dangerouslySetInnerHTML={{
                                      __html: e.question,
                                    }}
                                  ></div>
                                </div>
                              </td>

                              <td>
                                <div
                                  className="verified-answer"
                                  dangerouslySetInnerHTML={{
                                    __html:
                                      e.givenAnswer
                                        ?.split("$;")
                                        .filter(
                                          (ev) => ev?.split("$$")[1] === "1"
                                        )[0]
                                        ?.split("$$")[0] ||
                                      "No answer provided",
                                  }}
                                ></div>
                              </td>

                              <td>
                                <div className="action-buttons-verified">
                                  <button
                                    className="action-button-verified delete-button-verified"
                                    onClick={() =>
                                      this.setState(
                                        {
                                          qNum: e.questionid,
                                        },
                                        () => this.deleteQuestion("verified")
                                      )
                                    }
                                  >
                                    <i className="bi bi-trash-fill action-icon-verified"></i>
                                  </button>
                                </div>
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  )}
                </div>
              );
            })}

            {/* Pagination for quiz groups */}
            {this.renderPagination(
              quizGroups.length,
              verifiedCurrentPage,
              verifiedItemsPerPage,
              this.handleVerifiedPageChange,
              this.handleVerifiedItemsPerPageChange,
              true // isVerified = true for different styling
            )}
          </>
        ) : (
          <div className="empty-state-verified">
            <i className="bi bi-check-circle empty-state-icon"></i>
            <p>No verified questions are available.</p>
          </div>
        )}
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: !p.popUpOpen,
      questionText: "",
      op1: "",
      op2: "",
      op3: "",
      op4: "",
      op5: "",
      op5Num: 0,
      correctOption: null,
      selectedCat: "",
      userImages: "",
      addOptionClicked: false,
    }));
  };

  questionEditor = () => {
    const {
      qNum,
      editorLoading,
      optionsData,
      categoryList,
      selectedCat,
      correctOption,
      question,
      addOptionClicked,
    } = this.state;
    // const question = qReportData.filter((e) => e.questionid === qNum)[0];
    // this.setState({ selectedCat: question.cid });
    return (
      <>
        {!editorLoading &&
        question !== undefined &&
        categoryList !== undefined ? (
          <>
            <div>
              Select Category{" "}
              <select
                onChange={(e) => this.setState({ selectedCat: e.target.value })}
              >
                {categoryList.map((category, i) => {
                  return (
                    <option
                      key={"category" + category.cid + i}
                      value={category.cid}
                      selected={
                        selectedCat === category.cid ||
                        question.cid === category.cid
                      }
                    >
                      {category.category_name}
                    </option>
                  );
                })}
              </select>
            </div>
            <div>
              Question
              <EditorNew
                // onInit={(evt, editor) => (editorRef.current = editor)}
                initialValue={question}
                init={{
                  height: 200,
                  width: "100%",
                  menubar: false,

                  plugins: [
                    "advlist autolink lists link image charmap print preview anchor",
                    "searchreplace visualblocks code fullscreen",
                    "insertdatetime media table paste code help wordcount",
                  ],
                  toolbar:
                    "undo redo | formatselect | " +
                    "bold italic backcolor | alignleft aligncenter " +
                    "alignright | bullist numlist | subscript superscript |  " +
                    " code " +
                    "removeformat",
                  content_style:
                    "body { font-family:Ramabhadra; font-size:14px }",
                }}
                onEditorChange={(e) => this.setState({ questionText: e })}
              />
            </div>

            {optionsData.map((e, i) => (
              <div style={{ marginTop: 15 }}>
                Option {i + 1}{" "}
                <input
                  type={"radio"}
                  value={e.oid}
                  name={qNum}
                  id={e.oid}
                  onChange={(e) =>
                    this.setState({ correctOption: e.target.value })
                  }
                  checked={correctOption === e.oid.toString()}
                />{" "}
                <label htmlFor={e.oid}>Correct option</label>
                {i >= 4 && (
                  <i
                    className="bi bi-trash-fill"
                    style={{ cursor: "pointer", marginLeft: 10 }}
                    onClick={() =>
                      this.setState(
                        {
                          oid: e.oid,
                          optionsData:
                            e.oid === 0
                              ? optionsData.slice(0, optionsData.length - 1)
                              : optionsData,
                          addOptionClicked: false,
                        },
                        () => {
                          if (e.oid !== 0) {
                            this.deleteOption();
                          }
                        }
                      )
                    }
                  ></i>
                )}
                <EditorNew
                  // onInit={(evt, editor) => (editorRef.current = editor)}
                  key={e.oid + qNum}
                  initialValue={e.q_option}
                  init={{
                    height: 100,
                    width: "90%",
                    menubar: false,

                    plugins: [
                      "advlist autolink lists link image charmap print preview anchor",
                      "searchreplace visualblocks code fullscreen",
                      "insertdatetime media table paste code help wordcount",
                    ],
                    toolbar:
                      "undo redo | formatselect | " +
                      "bold italic backcolor | alignleft aligncenter " +
                      "alignright | bullist numlist | subscript superscript |  " +
                      " code " +
                      "removeformat",
                    content_style:
                      "body { font-family:Helvetica,Arial,sans-serif; font-size:14px ,margin-top:15px;}",
                  }}
                  onEditorChange={(value) =>
                    this.setState({
                      ["op" + (i + 1)]: value,
                      ["op" + (i + 1) + "Num"]: e.oid,
                    })
                  }
                />
              </div>
            ))}
            {optionsData.length < 5 && !addOptionClicked && (
              <div style={{ display: "flex", justifyContent: "flex-end" }}>
                <Button
                  className="btn addbtn"
                  style={{ color: "white" }}
                  onClick={() =>
                    this.setState((p) => ({
                      optionsData: [
                        ...p.optionsData,
                        { oid: 0, q_option: "", score: 0 },
                      ],
                      addOptionClicked: true,
                    }))
                  }
                >
                  Add Option
                </Button>
              </div>
            )}
          </>
        ) : (
          <div>
            <Loader />
          </div>
        )}
      </>
    );
  };

  saveQuestion = async () => {
    const {
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      op5,
      op5Num,
      bonus,
    } = this.state;
    let { questionText } = this.state;
    this.setState({ isLoading: true });
    // console.log(questionText, op1, op2, op3, op4, selectedCat, correctOption);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      questionText,
      op1,
      op2,
      op3,
      op4,
      op1Num,
      op2Num,
      op3Num,
      op4Num,
      selectedCat,
      correctOption,
      qNum,
      quizId,
      op5,
      op5Num,
      type: `EDITREPORTED,${bonus}`,
    };
    // console.log(body);
    try {
      this.setState({ editorLoading: true });

      await axios.post(`${commonData["api"]}/admin/add-edit-question`, body, {
        headers,
      });
      //   console.log(data);
      this.setState(
        {
          editorLoading: false,
          popUpOpen: false,
          isLoading: false,
          addOptionClicked: false,
        },
        this.getData
      );
      NotificationManager.success(`Question Updated Successfully..`);
    } catch (err) {
      console.log(err);
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ editorLoading: false });
    }
  };
  renderPopUp = () => {
    const { popUpOpen, bonus, userImages, qNum } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"md"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{"Edit Reported Question"}</p>
            </div>
            <p onClick={this.handleOpen} style={{ cursor: "pointer" }}>
              X
            </p>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          {this.questionEditor()}
          <br />
          <br />
          {userImages !== null && (
            <>
              <div>Proof Images Uploaded by Students</div>
              <div className="repoteddiv">
                {String(userImages)
                  .split(",")
                  ?.map((eachImg) => {
                    return (
                      <>
                        <figure>
                          <img
                            src={eachImg}
                            className="repored-image"
                            alt="Student uploaded proof"
                          />
                          <figcaption
                            style={{ fontSize: 12, textAlign: "center" }}
                          >
                            Uploaded - {eachImg?.split(qNum)[1]}
                          </figcaption>
                        </figure>
                        <br />
                      </>
                    );
                  })}
              </div>
            </>
          )}
        </DialogContent>
        <DialogActions>
          <div className="reporteditdata">
            <div>
              <input
                type={"checkbox"}
                value={bonus}
                id="bonus"
                onChange={() => this.setState((pr) => ({ bonus: !pr.bonus }))}
                checked={bonus}
              />
              <label htmlFor="bonus" style={{ fontSize: 15 }}>
                {" "}
                Select this to add bonus point
              </label>
            </div>
            <div>
              <Button
                className="btn header-btns attemptbtn attempt-btns submit popbtn"
                onClick={this.handleOpen}
              >
                Cancel
              </Button>
              <Button
                className="btn header-btns attemptbtn attempt-btns popbtn"
                onClick={this.saveQuestion}
              >
                Save changes
              </Button>
            </div>
          </div>
        </DialogActions>
      </Dialog>
    );
  };

  deleteQuestion = async (type) => {
    const { qNum, qReportData, verifiedData } = this.state;

    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "reportedDelete",
      search: "",
      qid: qNum,
    };
    if (
      window.confirm("Do you really want to remove this reported Question?")
    ) {
      try {
        await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
          headers,
        });
        // console.log(qReportData);
        if (type === "report") {
          this.setState(
            {
              qReportData: qReportData.filter((e) => e.questionid !== qNum),
            },
            NotificationManager.success(`Question Deleted Succesfully...`)
          );
        } else {
          this.setState(
            {
              verifiedData: verifiedData.filter((e) => e.questionid !== qNum),
            },
            NotificationManager.success(`Question Deleted Succesfully...`)
          );
        }
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    }
  };

  render() {
    const { isLoading, login, popUpOpen } = this.state;
    const { from } = this.props;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            {from !== "examslist" ? (
              <div className="desktopsidebar">
                <div className="desktopsidebarmenuexamdetailsAdmin">
                  <AdminMenu />
                </div>
                <Header />

                <Divider color="white" />
                <div className="viewresultsdesktop admin">
                  {this.reportedQuestionsTable()}
                  {popUpOpen && this.renderPopUp()}
                  {this.verifiedQuestionsTable()}
                </div>
              </div>
            ) : (
              <div className="   ">
                {this.reportedQuestionsTable()}
                {popUpOpen && this.renderPopUp()}
              </div>
            )}
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default ReportedQuestions;
