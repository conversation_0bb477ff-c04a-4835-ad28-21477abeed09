import React, { Component } from 'react';
import { Container, Typography, Box, Paper } from '@mui/material';
import CloudflareStreamManager from './CloudflareStreamManager';

class CloudflareStreamTest extends Component {
  constructor(props) {
    super(props);
    this.state = {
      uploadedVideoId: null,
      videoDetails: null
    };
  }

  handleVideoUploaded = (videoId) => {
    console.log('✅ Video uploaded successfully:', videoId);
    this.setState({ uploadedVideoId: videoId });
  };

  handleVideoDetailsLoaded = (videoDetails) => {
    console.log('📊 Video details loaded:', videoDetails);
    this.setState({ videoDetails });
  };

  handleVideoDeleted = () => {
    console.log('🗑️ Video deleted');
    this.setState({ uploadedVideoId: null, videoDetails: null });
  };

  render() {
    const { uploadedVideoId, videoDetails } = this.state;

    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Paper elevation={2} sx={{ p: 3 }}>
          <Typography variant="h4" gutterBottom sx={{ textAlign: 'center', mb: 4 }}>
            🎬 Cloudflare Stream Manager Test
          </Typography>
          
          <Typography variant="body1" sx={{ mb: 3, textAlign: 'center' }}>
            This is a test page for the Cloudflare Stream upload functionality.
            Use this to verify that video uploads are working correctly.
          </Typography>

          <CloudflareStreamManager
            onVideoUploaded={this.handleVideoUploaded}
            onVideoDetailsLoaded={this.handleVideoDetailsLoaded}
            onVideoDeleted={this.handleVideoDeleted}
            existingVideoId={uploadedVideoId}
          />

          {/* Debug Information */}
          {(uploadedVideoId || videoDetails) && (
            <Box sx={{ mt: 4, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                🔍 Debug Information
              </Typography>
              
              {uploadedVideoId && (
                <Typography variant="body2" sx={{ fontFamily: 'monospace', mb: 1 }}>
                  <strong>Video ID:</strong> {uploadedVideoId}
                </Typography>
              )}
              
              {videoDetails && (
                <Box>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Status:</strong> {videoDetails.status?.state || 'Unknown'}
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 1 }}>
                    <strong>Ready to Stream:</strong> {videoDetails.readyToStream ? 'Yes' : 'No'}
                  </Typography>
                  {videoDetails.playback?.hls && (
                    <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem', wordBreak: 'break-all' }}>
                      <strong>HLS URL:</strong> {videoDetails.playback.hls}
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          )}
        </Paper>
      </Container>
    );
  }
}

export default CloudflareStreamTest;
