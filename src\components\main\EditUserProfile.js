import React from "react";
import Header from "../Header";
import Loader from "../Loader";
// import axios from "axios";
import TextField from "@mui/material/TextField";
import Select from "@mui/material/Select";
import MenuItem from "@mui/material/MenuItem";
import "./styles.css";
import Divider from "@mui/material/Divider";
import DesktopMenu from "../DesktopMenu";
import Button from "@mui/material/Button";
import axios from "axios";
import commonData from "../../importanValue";

import Cookie from "js-cookie";
import { NotificationManager, NotificationContainer } from "react-notifications";

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.5 + ITEM_PADDING_TOP,
      width: 250,
    },
  },
};
class EditUser extends React.Component {
  state = {
    isLoading: false,
    data: {},
    name: "",
    surname: "",
    district: "",
    dob: "",
    email: "",
    updated: false,
    phone: "",
    err: "",
    paymentData: [],
    dobdate: "",
    dobmonth: "",
    dobyear: "",
    displayHeader: false,
  };

  componentDidMount() {
    this.getData();

  }

  getData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    this.setState({ isLoading: true });
    const resultList = await axios.post(
      `${commonData["api"]}/update-user-details`,
      { type: "GET" },
      {
        headers,
      }
    );
    const userPaymentData = await axios.get(
      `${commonData["api"]}/get-user-payments-data/0`,
      {
        headers,
      }
    );
    console.log(userPaymentData.data[0]);
    if (resultList.data[0][0].date_of_birth !== '' || resultList.data[0][0].date_of_birth !== null) {
      var [dobdate, dobmonth, dobyear] = resultList.data[0][0].date_of_birth.split(" / ")
    }
    this.setState({
      data: resultList.data[0][0],
      isLoading: false,
      name: resultList.data[0][0].first_name,
      surname: resultList.data[0][0].last_name,
      district: resultList.data[0][0].district,
      dob: resultList.data[0][0].date_of_birth,
      phone: resultList.data[0][0].contact_no,
      email: resultList.data[0][0].email,
      paymentData: userPaymentData.data[0],
      dobdate, dobmonth, dobyear
    });
    const { location } = this.props;
    const { search } = location;
    if (
      search.includes("incomplete")
    ) {
      NotificationManager.error(`Please Fill your Details...`);
      this.setState({ displayHeader: false })
      localStorage.setItem("vUC", false)
    } else {
      this.setState({ displayHeader: true })
    }
  };

  renderUserPayments = () => {
    const { paymentData } = this.state;
    return (
      <div style={{ overFlowX: "scroll" }}>
        <h3 style={{ color: "white", marginTop: 10 }}>Your Payments</h3>
        <table className="table">
          <thead>
            <tr>
              <th className="tablehead examdetails fontUSerpay">
                Package Name
              </th>

              <th className="tablehead examdetails fontUSerpay">
                Amount (Paid on)
              </th>
              <th
                className="tablehead examdetails fontUSerpay"
                style={{ textAlign: "center" }}
              >
                PDF
              </th>
            </tr>
          </thead>
          <tbody>
            {paymentData.map((e) => (
              <tr className="tabledata" key={"paymentsreciepts" + e.gid}>
                <td className="tabledata fontUSerpay" style={{ fontSize: 14 }}>
                  {e.group_name}
                </td>

                <td className="tabledata fontUSerpay">
                  ₹.{e.price}
                  <p className="paymentdate">
                    <p>({new Date(e.paid_date).getDate()}-</p>
                    <p>{new Date(e.paid_date).getMonth()}-</p>
                    <p>{new Date(e.paid_date).getFullYear()})</p>
                  </p>
                </td>
                <td
                  style={{ color: "black", padding: 10, textAlign: "center" }}
                >
                  <a
                    href={`${commonData["api"]}/download-payment-reciept/${e.gid
                      }/${e.phonenumber}/${"Payment Reciept _" + e.group_name}`}
                    target="_blank"
                    className="linkto fontUSerpay"
                  >
                    <i className="bi bi-download"></i>
                  </a>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  activeAccount = async (e) => {
    e.preventDefault();
    const { name, surname, phone, email, district, dobdate, dobmonth, dobyear } = this.state;
    const newdob = dobdate + " / " + dobmonth + " / " + dobyear;
    const registrationData = {
      name,
      surname,
      email,
      district,
      dob: newdob,
      number: phone,
      type: "EDIT",
    };
    // console.log(registrationData);
    if (district == null || district == "") {
      this.setState({ err: "Select Valid District" });
    } else if (email == "" || !email.includes("@gmail")) {
      this.setState({ err: "Enter valid Email" });
    }
    else if (dobdate == "" || dobmonth == "" || dobyear == "" || dobyear.length < 4 || dobdate.length < 2 || dobmonth.length < 2) {
      this.setState({ err: "Enter Valid Date of Birth" });
    }else if(name?.trim() ==="" || surname?.trim()===''){
      this.setState({ err: "Enter Valid Name" });
    } else {
      try {
        this.setState({ isLoading: true });
        await axios.post(
          `${commonData["api"]}/create-new-user`,
          registrationData
        );
        this.getData();
        localStorage.setItem("vUC", true)
        this.setState({ isLoading: false, updated: true, err: "", displayHeader: true });
        const { location, history } = this.props;
        const { search } = location;
        if (search.includes("next")) {
          let nextUrl = search.split("?next=")[1].split("&")[0]
          if (nextUrl != "") {
            history.replace(nextUrl);
          }
        } else {
          history.replace("/?msg=welcome");
        }
      } catch (err) {
        this.setState({ isLoading: false, err: "Something Went Wrong" });
      }
    }
  };
  onChangeName = (e) => {
    if (e.target.value !== "") {
      this.setState({ name: e.target.value, updated: false, err: "" });
    } else {
      this.setState({ name: e.target.value, err: "Please Enter Valid Name" });
    }
  };
  onChangeSurName = (e) => {
    if (e.target.value !== "") {
      this.setState({ surname: e.target.value, updated: false, err: "" });
    } else {
      this.setState({
        surname: e.target.value,
        err: "Please Enter Valid Surname",
      });
    }
  };
  onChangeEmail = (e) => {
    if (e.target.value !== "" && e.target.value.includes("@gmail")) {
      this.setState({ email: e.target.value, updated: false, err: "" });
    } else {
      this.setState({
        email: e.target.value,
        err: "Please Enter Valid Email",
      });
    }
  };

  onChangeDistrict = (e) => {
    this.setState({ district: e.target.value, updated: false, err: "" });
  };
  formatNumber = (num, from, limit) => {
    num =
      Number(num) > from && Number(num) < 10 && num.length == 1
        ? "0" + num
        : num;
    if (Number(num) > limit) {
      num = num.substr(1, 2);
      num =
        Number(num) > from && Number(num) < 10 && num.length == 1
          ? "0" + num
          : num;
    }
    return num;
  };
  dateFormat = (e) => {
    var dateValue = e;
    if (/\D$/.test(dateValue)) {
      dateValue = dateValue.substr(0, dateValue.length - 3);
    }
    dateValue = dateValue.replaceAll(" ", "");
    var arr = dateValue.split("/");

    if (arr[0]) arr[0] = this.formatNumber(arr[0], 3, 31);
    if (arr[1]) arr[1] = this.formatNumber(arr[1], 1, 12);

    var result = arr.map(function (val, index) {
      return val.length == 2 && index < 2 ? val + " / " : val;
    });
    this.setState({ dob: result.join("").substr(0, 14) });
  };

  onChangeDob = (e) => {
    e.preventDefault();
    // this.dateFormat(e.target.value);
    this.setState({ [e.target.name]: e.target.value, err: "" });
  };

  render() {
    const {
      name,
      district,
      phone,
      email,
      dobdate,
      dobmonth,
      dobyear,
      updated,
      isLoading,
      surname,
      err,
      displayHeader
    } = this.state;
    return (
      <>
        <div className="desktopsidebar">
          {displayHeader && <div className="desktopsidebarmenuexamdetails">
            <DesktopMenu />
          </div>}
          {displayHeader && <Header />}

          <Divider color="white" />
          <div className="viewresultsdesktop22 edituser">
            <div className="packages-container" style={{ marginTop: 30 }}>
              <div className="packages-inner-container" style={{ padding: 20 }}>
                {!isLoading ? (
                  <>
                    <p className="main">My Profile</p>

                    <Divider color="white" style={{ marginBottom: 10 }} />
                    {updated && (
                      <p
                        className="otp-mesage-text"
                        style={{ color: "white", textAlign: "center" }}
                      >
                        Data updated successfully..
                      </p>
                    )}
                    {err && (
                      <p
                        className="otp-mesage-text"
                        style={{ color: "red", textAlign: "center" }}
                      >
                        Error : {err}
                      </p>
                    )}

                    <form
                      className="form-register"
                      onSubmit={this.activeAccount}
                    >
                      <TextField
                        className="input-box register"
                        label="Name"
                        variant="filled"
                        value={name}
                        focused
                        onChange={this.onChangeName}
                      />
                      <TextField
                        className="input-box register"
                        label="Surname"
                        variant="filled"
                        value={surname}
                        focused
                        onChange={this.onChangeSurName}
                      />
                      <TextField
                        className="input-box register"
                        label="Email Address"
                        variant="filled"
                        value={email}
                        focused
                        onChange={this.onChangeEmail}
                      // disabled
                      />
                      <TextField
                        className="input-box register"
                        label="Contact Number"
                        variant="filled"
                        value={phone}
                        focused
                        disabled
                      />

                      <Select
                        id="Please-Select-District"
                        className="input-box dis"
                        // label="Please Select District"
                        value={district}
                        MenuProps={MenuProps}
                        onChange={this.onChangeDistrict}
                      >
                        <MenuItem
                          value={10}
                          disabled
                          className="attempt-option-select"
                        >
                          Please Select District
                        </MenuItem>
                        <MenuItem
                          value="Anantapur"
                          className="attempt-option-select"
                        >
                          Anantapur
                        </MenuItem>
                        <MenuItem
                          value="Chittoor"
                          className="attempt-option-select"
                        >
                          Chittoor
                        </MenuItem>
                        <MenuItem
                          value="East Godavari"
                          className="attempt-option-select"
                        >
                          East Godavari
                        </MenuItem>
                        <MenuItem
                          value="Guntur"
                          className="attempt-option-select"
                        >
                          Guntur
                        </MenuItem>
                        <MenuItem
                          value="Krishna"
                          className="attempt-option-select"
                        >
                          Krishna
                        </MenuItem>
                        <MenuItem
                          value="Kurnool"
                          className="attempt-option-select"
                        >
                          Kurnool
                        </MenuItem>
                        <MenuItem
                          value="Prakasam"
                          className="attempt-option-select"
                        >
                          Prakasam
                        </MenuItem>
                        <MenuItem
                          value="Srikakulam"
                          className="attempt-option-select"
                        >
                          Srikakulam
                        </MenuItem>
                        <MenuItem
                          value="Sri Potti Sriramulu Nellore"
                          className="attempt-option-select"
                        >
                          Sri Potti Sriramulu Nellore
                        </MenuItem>
                        <MenuItem
                          value="Visakhapatnam"
                          className="attempt-option-select"
                        >
                          Visakhapatnam
                        </MenuItem>
                        <MenuItem
                          value="Vizianagaram"
                          className="attempt-option-select"
                        >
                          Vizianagaram
                        </MenuItem>
                        <MenuItem
                          value="West Godavari"
                          className="attempt-option-select"
                        >
                          West Godavari
                        </MenuItem>
                        <MenuItem
                          value="Kadapa"
                          className="attempt-option-select"
                        >
                          Kadapa
                        </MenuItem>
                        <MenuItem
                          value="Adilabad"
                          className="attempt-option-select"
                        >
                          Adilabad
                        </MenuItem>
                        <MenuItem
                          value="Bhadradri Kothagudem"
                          className="attempt-option-select"
                        >
                          Bhadradri Kothagudem
                        </MenuItem>
                        <MenuItem
                          value="Hyderabad"
                          className="attempt-option-select"
                        >
                          Hyderabad
                        </MenuItem>
                        <MenuItem
                          value="Jagtial"
                          className="attempt-option-select"
                        >
                          Jagtial
                        </MenuItem>
                        <MenuItem
                          value="Jangaon"
                          className="attempt-option-select"
                        >
                          Jangaon
                        </MenuItem>
                        <MenuItem
                          value="Jayashankar Bhoopalpally"
                          className="attempt-option-select"
                        >
                          Jayashankar Bhoopalpally
                        </MenuItem>
                        <MenuItem
                          value="Jogulamba Gadwal"
                          className="attempt-option-select"
                        >
                          Jogulamba Gadwal
                        </MenuItem>
                        <MenuItem
                          value="Kamareddy"
                          className="attempt-option-select"
                        >
                          Kamareddy
                        </MenuItem>
                        <MenuItem
                          value="Karimnagar"
                          className="attempt-option-select"
                        >
                          Karimnagar
                        </MenuItem>
                        <MenuItem
                          value="Khammam"
                          className="attempt-option-select"
                        >
                          Khammam
                        </MenuItem>
                        <MenuItem
                          value="Komaram Bheem Asifabad"
                          className="attempt-option-select"
                        >
                          Komaram Bheem Asifabad
                        </MenuItem>
                        <MenuItem
                          value="Mahabubabad"
                          className="attempt-option-select"
                        >
                          Mahabubabad
                        </MenuItem>
                        <MenuItem
                          value="Mahabubnagar"
                          className="attempt-option-select"
                        >
                          Mahabubnagar
                        </MenuItem>
                        <MenuItem
                          value="Mancherial"
                          className="attempt-option-select"
                        >
                          Mancherial
                        </MenuItem>
                        <MenuItem
                          value="Medak"
                          className="attempt-option-select"
                        >
                          Medak
                        </MenuItem>
                        <MenuItem
                          value="Medchal"
                          className="attempt-option-select"
                        >
                          Medchal
                        </MenuItem>
                        <MenuItem
                          value="Nagarkurnool"
                          className="attempt-option-select"
                        >
                          Nagarkurnool
                        </MenuItem>
                        <MenuItem
                          value="Nalgonda"
                          className="attempt-option-select"
                        >
                          Nalgonda
                        </MenuItem>
                        <MenuItem
                          value="Nirmal"
                          className="attempt-option-select"
                        >
                          Nirmal
                        </MenuItem>
                        <MenuItem
                          value="Nizamabad"
                          className="attempt-option-select"
                        >
                          Nizamabad
                        </MenuItem>
                        <MenuItem
                          value="Peddapalli"
                          className="attempt-option-select"
                        >
                          Peddapalli
                        </MenuItem>
                        <MenuItem
                          value="Rajanna Sircilla"
                          className="attempt-option-select"
                        >
                          Rajanna Sircilla
                        </MenuItem>
                        <MenuItem
                          value="Rangareddy"
                          className="attempt-option-select"
                        >
                          Rangareddy
                        </MenuItem>
                        <MenuItem
                          value="Sangareddy"
                          className="attempt-option-select"
                        >
                          Sangareddy
                        </MenuItem>
                        <MenuItem
                          value="Siddipet"
                          className="attempt-option-select"
                        >
                          Siddipet
                        </MenuItem>
                        <MenuItem
                          value="Suryapet"
                          className="attempt-option-select"
                        >
                          Suryapet
                        </MenuItem>
                        <MenuItem
                          value="Vikarabad"
                          className="attempt-option-select"
                        >
                          Vikarabad
                        </MenuItem>
                        <MenuItem
                          value="Wanaparthy"
                          className="attempt-option-select"
                        >
                          Wanaparthy
                        </MenuItem>
                        <MenuItem
                          value="Warangal"
                          className="attempt-option-select"
                        >
                          Warangal
                        </MenuItem>
                        <MenuItem
                          value="Yadadri Bhuvanagiri"
                          className="attempt-option-select"
                        >
                          Yadadri Bhuvanagiri
                        </MenuItem>
                      </Select>

                      {/* <DatePicker
                        disableFuture
                        className="input-box register"
                        openTo="year"
                        format="dd/MMM/yyyy"
                        label="Date of birth"
                        views={["year", "month", "date"]}
                        value={dob}
                        inputVariant="filled"
                        focused
                        onChange={this.onChangeDob}
                      /> */}
                      {/* <TextField
                        required
                        variant="filled"
                        value={dob}
                        type={"text"}
                        name="dob"
                        id="#dateFormat"
                        // inputProps={{ maxLength: 10 }}
                        onChange={this.onChangeDob}
                        label={"Your Date of Birth (DD/MM/YYYY) "}
                        className="input-box register"
                      /> */}
                      <p style={{ color: "#FFF" }}>
                        Your Date of Birth (DD/MM/YYYY){" "}
                      </p>
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          marginBottom: 20,
                        }}
                      >
                        <TextField
                          required
                          focused
                          variant="filled"
                          value={dobdate}
                          type={"text"}
                          name="dobdate"
                          id="#dateFormat"
                          inputProps={{ maxLength: 2 }}
                          onChange={this.onChangeDob}
                          label={"Date"}
                          className="input-box register"
                          style={{ width: 120 }}
                        />
                        <h1
                          style={{
                            color: "#FFF",
                            marginRight: 10,
                            marginLeft: 10,
                          }}
                        >
                          {" "}
                          /
                        </h1>
                        <TextField
                          required
                          focused
                          variant="filled"
                          value={dobmonth}
                          type={"text"}
                          name="dobmonth"
                          id="#dateFormat"
                          inputProps={{ maxLength: 2 }}
                          onChange={this.onChangeDob}
                          label={"Month"}
                          className="input-box register"
                          style={{ width: 120 }}
                        />
                        <h1
                          style={{
                            color: "#FFF",
                            marginRight: 10,
                            marginLeft: 10,
                          }}
                        >
                          {" "}
                          /
                        </h1>
                        <TextField
                          required
                          focused
                          variant="filled"
                          value={dobyear}
                          type={"text"}
                          name="dobyear"
                          id="#dateFormat"
                          inputProps={{ maxLength: 4 }}
                          onChange={this.onChangeDob}
                          label={"Year"}
                          className="input-box register"
                        />
                      </div>
                      <Button
                        type="submit"
                        variant="contained"
                        className="btn activateacoountbtn"
                      >
                        Submit {"&"} Update account
                      </Button>
                    </form>
                    {this.renderUserPayments()}
                  </>
                ) : (
                  <div className="login-buttons-container">
                    <Loader />
                  </div>
                )}
                <div>
                  <NotificationContainer />
                </div>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
}

export default EditUser;
