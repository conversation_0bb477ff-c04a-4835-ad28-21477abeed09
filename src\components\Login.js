import react from "react";
import axios from "axios";
import TextField from "@mui/material/TextField";
import Button from "@mui/material/Button";
import ArrowBackSharpIcon from "@mui/icons-material/ArrowBackSharp";
import Loader from "./Loader";
import commonData from "../importanValue";
import "./styles.css";
import { withRouter } from "react-router";

import Cookies from "js-cookie";

class Login extends react.Component {
  state = {
    phoneNum: "",
    getOtpClicked: false,
    isLoading: true,
    err: "",
    otp: "",
  };

  onSubmit = (e) => {
    e.preventDefault();
    const { phoneNum } = this.state;
    if (phoneNum.length === 10) {
      this.setState({ err: "" });
      this.setState({ getOtpClicked: true });
      this.generateOtp();
      localStorage.removeItem("num");
      localStorage.removeItem("uid");
    } else {
      this.setState({ err: "Enter Valid Number", phoneNum: "" });
    }
  };

  
  componentDidMount() {
    this.autoLogin();
  }

  autoLogin = async () => {
    const phoneNum = localStorage.getItem("userDetailsApp");
    console.log("🚀 ~ Login ~ autoLogin= ~ phoneNum:", phoneNum);
    try {
      if (phoneNum) {
        const { history, location } = this.props;
        const nextUrl = location.search.split("?next=")[1];
        this.setState({ isLoading: false });
        localStorage.setItem("num", phoneNum);
        this.setState({ isLoading: true });

        const verifyNum = await axios.post(`${commonData["api"]}/verify-user`, {
          number: phoneNum,
        });
        console.log(
          "🚀 ~ file: Login.js ~ line 74 ~ Login ~ verifyOtp= ~ verifyNum",
          verifyNum
        );

        if (verifyNum.data.length > 0) {
          const { date_of_birth, district, first_name, last_name, email } =
            verifyNum.data[0];
          let userInfoFilled = true;
          if (
            date_of_birth == null ||
            date_of_birth == "" ||
            district == null ||
            district == "" ||
            first_name == null ||
            first_name == "" ||
            last_name == null ||
            last_name == "" ||
            email == null ||
            email == ""
          ) {
            userInfoFilled = false;
          }
          const token = await axios.post(
            `${commonData["api"]}/generate-user-token`,
            {
              number: phoneNum,
            }
          );

          this.setState({ isLoading: false });
          Cookies.set("jwt_token", token.data.jwt, { expires: 365 });
          localStorage.setItem("uid", token.data.result[0].uid);
          console.log(
            "🚀 ~ file: Login.js ~ line 93 ~ Login ~ verifyOtp= ~ userInfoFilled",
            nextUrl,
            userInfoFilled
          );
          if (nextUrl !== undefined && userInfoFilled) {
            history.replace(nextUrl);
          }
          if (nextUrl === undefined && userInfoFilled) {
            history.replace("/?msg=welcome");
          } else if (nextUrl !== undefined && !userInfoFilled) {
            history.replace(`/user-profile?next=${nextUrl}&msg=incomplete`);
          } else if (nextUrl === undefined && !userInfoFilled) {
            history.replace(`/user-profile?msg=incomplete`);
          }
        } else {
          if (nextUrl !== undefined) {
            history.replace(`/register-new-account?next=${nextUrl}`);
          } else {
            history.replace(`/register-new-account`);
          }
        }
        this.setState({ isLoading: false });
      }
    } catch (error) {}
  };


  generateOtp = async () => {
    const { phoneNum } = this.state;
    try {
      const response = await axios.post(
        `${commonData["apapi"]}/send-verification-msg`,
        {
          type: "send",
          phone: phoneNum,
          otp: "",
        }
      );
      this.setState({ isLoading: false, otp: "" });
      // localStorage.setItem("sessionId", response.data.Details);
    } catch {
      this.setState({ getOtpClicked: false });
      this.setState({ err: "Enter Valid Number", phoneNum: "" });
    }
  };

  onSubmitOtp = (e) => {
    e.preventDefault();
    this.verifyOtp();
  };

  verifyOtp = async () => {
    const { otp, phoneNum } = this.state;
    // const sessionId = localStorage.getItem("sessionId");
    const { history, location } = this.props;
    const nextUrl = location.search.split("?next=")[1];
    try {
      const response = await axios.post(
        `${commonData["apapi"]}/send-verification-msg`,
        {
          type: "verify",
          phone: phoneNum,
          otp: otp,
        }
      );

      if (response) {
        this.setState({ isLoading: false });
        // localStorage.removeItem("sessionId");
        // const phoneNum = "7396019228";
        localStorage.setItem("num", phoneNum);
        // localStorage.setItem("jwt_token", "response.data.Details");
        this.setState({ isLoading: true });

        const verifyNum = await axios.post(`${commonData["api"]}/verify-user`, {
          number: phoneNum,
        });
        console.log(
          "🚀 ~ file: Login.js ~ line 74 ~ Login ~ verifyOtp= ~ verifyNum",
          verifyNum
        );

        if (verifyNum.data.length > 0) {
          const { date_of_birth, district, first_name, last_name, email } =
            verifyNum.data[0];
          let userInfoFilled = true;
          if (
            date_of_birth == null ||
            date_of_birth == "" ||
            district == null ||
            district == "" ||
            first_name == null ||
            first_name == "" ||
            last_name == null ||
            last_name == "" ||
            email == null ||
            email == ""
          ) {
            userInfoFilled = false;
          }
          const token = await axios.post(
            `${commonData["api"]}/generate-user-token`,
            {
              number: phoneNum,
            }
          );

          this.setState({ isLoading: false });
          Cookies.set("jwt_token", token.data.jwt, { expires: 365 });
          localStorage.setItem("uid", token.data.result[0].uid);
          console.log(
            "🚀 ~ file: Login.js ~ line 93 ~ Login ~ verifyOtp= ~ userInfoFilled",
            nextUrl,
            userInfoFilled
          );
          if (nextUrl !== undefined && userInfoFilled) {
            history.replace(nextUrl);
          }
          if (nextUrl === undefined && userInfoFilled) {
            history.replace("/?msg=welcome");
          } else if (nextUrl !== undefined && !userInfoFilled) {
            history.replace(`/user-profile?next=${nextUrl}&msg=incomplete`);
          } else if (nextUrl === undefined && !userInfoFilled) {
            history.replace(`/user-profile?msg=incomplete`);
          }
        } else {
          if (nextUrl !== undefined) {
            history.replace(`/register-new-account?next=${nextUrl}`);
          } else {
            history.replace(`/register-new-account`);
          }
        }
        this.setState({ isLoading: false });

        // history.replace(nextUrl);
      }
    } catch {
      this.setState({ err: "Enter Valid OTP", otp: "" });
    }
  };

  render() {
    const { getOtpClicked, phoneNum, isLoading, err, otp } = this.state;

    return (
      <div className="login-container">
        {!getOtpClicked ? (
          <form className="login-buttons-container" onSubmit={this.onSubmit}>
            <h1 className="heading">Login / Sign up</h1>
            <p className="otp-mesage-text">{err}</p>
            <TextField
              required
              className="input-box"
              label="Enter your Phone Number"
              variant="filled"
              value={phoneNum}
              inputProps={{ inputMode: "num", pattern: "[0-9]*" }}
              onChange={(e) =>
                this.setState({ phoneNum: e.target.value, err: "" })
              }
            />
            <Button type="submit" variant="contained" className="btn">
              Get OTP
            </Button>
          </form>
        ) : (
          <>
            {!isLoading ? (
              <form
                className="login-buttons-container"
                onSubmit={this.onSubmitOtp}
              >
                <div className="submit-otp-div">
                  <ArrowBackSharpIcon
                    className="back-button"
                    onClick={() =>
                      this.setState({ getOtpClicked: false, err: "" })
                    }
                  />

                  <div className="login-title-co">
                    <h1 className="heading">Login / Sign up</h1>
                  </div>
                </div>
                <p className="otp-mesage-text">
                  We have sent an OTP to your mobile number +91 {phoneNum}
                </p>
                <p className="otp-mesage-text">{err}</p>
                <TextField
                  required
                  className="input-box"
                  label="Enter OTP"
                  variant="filled"
                  value={otp}
                  inputProps={{ inputMode: "num", pattern: "[0-9]*" }}
                  onChange={(e) =>
                    this.setState({ otp: e.target.value, err: "" })
                  }
                />
                <Button type="submit" variant="contained" className="btn">
                  Validate OTP {"&"} Get Started
                </Button>
              </form>
            ) : (
              <div className="login-buttons-container">
                <Loader />
              </div>
            )}
          </>
        )}
      </div>
    );
  }
}

export default withRouter(Login);
