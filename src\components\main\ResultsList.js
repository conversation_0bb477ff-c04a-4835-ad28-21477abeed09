import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import <PERSON>ie from "js-cookie";
import Button from "@mui/material/Button";
import DesktopMenu from "../DesktopMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import "./styles.css";

class ResultsList extends react.Component {
  state = { isLoading: true, data: [], uid: "" };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const resultList = await axios.get(
      `${commonData["api"]}/all-result-list/`,
      {
        headers,
      }
    );
    // console.log(resultList.data);
    const uid = localStorage.getItem("num");
    this.setState({
      data: resultList.data[0],
      isLoading: false,
      uid: uid,
    });
  };

  render() {
    const { isLoading, data, uid } = this.state;
    // console.log(data);
    return (
      <>
        {!isLoading && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetails">
                <DesktopMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop22">
                <div className="title-quiz" id="print">
                  <p className="homepage-package-title examlist-ttle">
                    All Exam Results
                  </p>
                </div>
                {/* <Divider color="white" /> */}
                <div className="main-table-container">
                  <p style={{ color: "#fff" }}>
                    Note : మీరు మొదట రాసిన పరీక్షలు మాత్రమే ఈ పేజిలో
                    కనిపిస్తాయి. మిగతా పరీక్షలు రాసిన 24 గంటలలో డిలీట్ చేయబడతాయి
                    .
                  </p>
                  <table className="table">
                    <thead>
                      <tr>
                        <th className="tablehead">Exam Name</th>
                        <th className="tablehead">Marks</th>
                        <th className="tablehead">{"%"}</th>
                        <th className="tablehead">Action</th>
                      </tr>
                    </thead>
                    {data.length > 0 ? (
                      data.map((each, index) => (
                        <tbody key={`resultlist${index}`}>
                          <tr>
                            <td className="tabledata2">
                              <p>{each.quiz_name}</p>
                            </td>
                            <td className="tabledata2">
                              <div className="score-result_list">
                                {each.score}
                                <hr />
                                {each.noq}
                              </div>
                            </td>
                            <td className="tabledata2">
                              <p>{each.percent.toFixed(1) + "%"}</p>
                            </td>
                            <td className="tabledata2">
                              <Link
                                to={`/view-result/${uid}/${each.quid}/${each.rid}`}
                                className="linkto"
                              >
                                <div
                                  className="gotoexamsbtnhome"
                                  style={{ marginTop: -20 }}
                                >
                                  <Button
                                    variant="contained"
                                    className="btn header-btns attemptbtn viewBtn"
                                  >
                                    View
                                  </Button>
                                </div>
                              </Link>
                            </td>
                          </tr>
                        </tbody>
                      ))
                    ) : (
                      <tbody>
                        <tr style={{ textAlign: "center", color: "#fff" }}>
                          <td colSpan={4}>No results are available..</td>
                        </tr>
                      </tbody>
                    )}
                  </table>
                </div>
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
      </>
    );
  }
}

export default ResultsList;
