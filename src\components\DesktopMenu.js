import React from "react";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import <PERSON><PERSON> from "js-cookie";
import { withRouter } from "react-router";
import "./styles.css";
import GroupIcon from "@mui/icons-material/Group";
import HomeIcon from "@mui/icons-material/Home";
import LogoutIcon from "@mui/icons-material/Logout";
import FileCopyIcon from "@mui/icons-material/FileCopy";
import AutoStoriesIcon from "@mui/icons-material/AutoStories";
import BookIcon from "@mui/icons-material/Book";
import Divider from "@mui/material/Divider";
import AdminPanelSettingsIcon from "@mui/icons-material/AdminPanelSettings";
import OneSignal from "react-onesignal";
import Support from "./main/Support";
import commonData from "../importanValue";
// import { loadComponent } from "../App";
if (localStorage.getItem("user") === "1") {
  {
    OneSignal.sendTag("user", "admin");
  }
}
class DesktopMenu extends React.Component {
  logoutUser = () => {
    const { history } = this.props;
    Cookie.remove("jwt_token");
    // console.log(history);
    localStorage.removeItem("num");
    localStorage.removeItem("sessionId");
    localStorage.removeItem("uid");
    localStorage.removeItem("name");
    history.replace("/login");
  };

  render() {
    // console.log(this.props);
    const token = Cookie.get("jwt_token");
    return (
      <div style={{ height: "100vh", overflowY: "scroll" }}>
        <div>
          <img
            src="https://blogger.googleusercontent.com/img/a/AVvXsEi7kA1GOCjsRtLSvV0NagRPXSI_L8wjyhHQ5W7dQty0pv1qedRkRI7OTi5Kj8Cmrh1L7_8WYBdXkQD9KV5X4NVQBALkaRQ9xQfHMh1mZPztpTeQnmSCUoepSMgsLINrLGtX4EP-pl0_djDW7pcNvCujtKnpWYGUBCzS7yh7KqiYJHIqIZMNHs37zFaOZlse=s16000"
            alt="app-logo"
            style={{
              width: "auto",
              height: "auto",
              maxWidth: "100%",
              maxHeight: "100%",
            }}
          />
        </div>
        {/* <p className="heading-para telangana">ఆంధ్రప్రదేశ్</p>
          <h1 className="heading-title desktoptitle">నవచైతన్య కాంపిటీషన్స్</h1>
          <p className="heading-para desktoppara">చింతలపూడి , ఏలూరు జిల్లా</p> */}
        <Divider color="white" />
        {localStorage.getItem("user") === "1" && (
          <a
            href="https://books.navachaitanya.net/orders"
            target="_blank"
            rel="noreferrer"
            className="linkto"
          >
            <div className="deskmenustyle" style={{ marginTop: 15 }}>
              <div className="deskmenuIcon">➤</div>
              <p className="deskmenuPara">View Book Orders List</p>
            </div>
          </a>
        )}
        {commonData.userMenu.map((eachMen) => {
          return eachMen.enabled ? (
            <Link
              to={eachMen.url}
              className="linkto"
              target={eachMen.openInNewTab ? "_blank" : ""}
            >
              <div className="deskmenustyle" style={{ marginTop: 15 }}>
                <div className="deskmenuIcon">➤</div>
                <p className="deskmenuPara">{eachMen.name}</p>
              </div>
            </Link>
          ) : null;
        })}

        {token !== undefined && (
          <>
            {localStorage.getItem("user") === "1" && (
              <>
                {commonData.AdminMenu.map((eachMen) => {
                  return eachMen.enabled ? (
                    <Link to={eachMen.url} className="linkto">
                      <div className="deskmenustyle" style={{ marginTop: 15 }}>
                        <div className="deskmenuIcon">➤</div>
                        <p className="deskmenuPara">{eachMen.name}</p>
                      </div>
                    </Link>
                  ) : null;
                })}
              </>
            )}
            <div className="desksidemenulogout">
              <Button
                variant="contained"
                className="btn header-btns"
                onClick={this.logoutUser}
              >
                <div className="deskmenustyle">
                  <div className="deskmenuIcon">
                    <LogoutIcon />
                  </div>
                  <p className="deskmenuPara">Logout</p>
                </div>
              </Button>
            </div>
          </>
        )}
        <a
          href="/privacy-policy.html"
          target="_blank"
          rel="noreferrer"
          className="linkto"
        >
          {" "}
          <div className="deskmenustyle" style={{ marginTop: 15 }}>
            <div className="deskmenuIcon">➤</div>
            <p className="deskmenuPara"> Privacy Policy </p>
          </div>
        </a>
        <a
          href="/terms-and-conditions.html"
          target="_blank"
          rel="noreferrer"
          className="linkto"
        >
          {" "}
          <div className="deskmenustyle" style={{ marginTop: 15 }}>
            <div className="deskmenuIcon">➤</div>
            <p className="deskmenuPara"> Terms and Conditions </p>
          </div>
        </a>
        <a
          href="/refund-policy.html"
          target="_blank"
          rel="noreferrer"
          className="linkto"
        >
          <div className="deskmenustyle" style={{ marginTop: 15 }}>
            <div className="deskmenuIcon">➤</div>
            <p className="deskmenuPara">Refund Policy </p>
          </div>
        </a>
      </div>
    );
  }
}

export default withRouter(DesktopMenu);
