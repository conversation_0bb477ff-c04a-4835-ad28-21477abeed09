import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import axios from "axios";
import Cookie from "js-cookie";
import Button from "@mui/material/Button";
import AdminMenu from "./AdminMenu";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import DialogActions from "@mui/material/DialogActions";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import invalid from "../invalid.png";
import {
  NotificationManager,
  NotificationContainer,
} from "react-notifications";
import "react-notifications/lib/notifications.css";
import "./styles.css";
import ExportCSV, { exportToCSVD } from "./ExportCsv";

class CategoryList extends react.Component {
  state = {
    isLoading: false,
    login: "valid",
    categoryData: [],
    page: 0,
    popUpOpen: false,
    cateogryId: null,
    editorLoading: false,
    search: "",
    categoryCount: 0,
    popupType: "",
    cCount: 0,
    searchClicked: false,
    openCategoryPop: false,
    newCategoryMoveTo: 0,
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const { page, search } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "categoriesall",
      search: search,
      qid: page * 25,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data);
      this.setState({
        categoryData: data.data[0],
        categoryCount: data.data[1][0].count,
        isLoading: false,
      });
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  searchCategory = (e) => {
    e.preventDefault();
    const { search } = this.state;

    if (search === "") {
      NotificationManager.error(`Please Enter Search Value`);
    } else {
      this.setState({
        isLoading: true,
        searchClicked: true,
      });
      this.getData();
    }
  };

  changeUserMedium = async (status) => {
    const { cateogryId } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "enableAITranslation",
      search: status,
      qid: cateogryId,
    };
    const isConfirm = window.confirm("Do you really update the category?");
    if (isConfirm) {
      try {
        await axios.post(`${commonData["api"]}/admin/qbankdata`, body, {
          headers,
        });
        NotificationManager.success(`Category Status updated Succesfully...`);
        this.getData();
      } catch (err) {
        // console.log(err);
        this.setState({
          isLoading: false,
          login: "invalid",
        });
      }
    }
  };

  downloadTeluguQuestions = async (selectedQuid) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "downloadTeluguQuestions",
      search: selectedQuid,
      qid: 0,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data);
      this.setState({
        isLoading: false,
        // exportResultInfo: data.data[0],
      });
      exportToCSVD(data.data[0], selectedQuid + "_Category_Questions");
    } catch (err) {
      console.log(
        "🚀 ~ file: CategoryList.js:155 ~ CategoryList ~ downloadTeluguQuestions= ~ err:",
        err
      );
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };

  downloadReportedQuestions = async (selectedQuid) => {
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "downloadReportedQuestions",
      search: selectedQuid,
      qid: 0,
    };
    try {
      this.setState({ isLoading: true });

      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data);
      this.setState({
        isLoading: false,
        // exportResultInfo: data.data[0],
      });
      exportToCSVD(data.data[0], selectedQuid + "_Reported_Questions");
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
      this.setState({ isLoading: false });
    }
  };
  categoriesTable = () => {
    const { categoryData, categoryCount, searchClicked, search } = this.state;
    // console.log(categoryData);
    const style = `table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width:100%;
      }
      
      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 10px;
        height: "100%";
      }
      
      tr:nth-child(even) {
        background-color: #dddddd;
      }`;
    return (
      <div className="paiduserdiv">
        <style>{style}</style>
        <div className="adminTableButtons">
          <h3>All Categories</h3>

          <div
            style={{
              display: "flex",
              flexDirection: "column",
              justifyContent: "center",
            }}
          >
            <form
              onSubmit={this.searchCategory}
              style={{ cursor: "pointer", display: "flex", marginBottom: 10 }}
            >
              <input
                type={"text"}
                value={search}
                id="search"
                onChange={(e) => this.setState({ search: e.target.value })}
                placeholder="Enter Category Name"
              />
              <div
                style={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  // marginLeft: 5,
                  backgroundColor: "green",
                  padding: 5,
                  color: "#fff",
                  borderRadius: 10,
                }}
              >
                <button
                  type="submit"
                  style={{
                    backgroundColor: "transparent",
                    borderWidth: 0,
                    color: "#fff",
                  }}
                >
                  <i className="bi bi-search"></i>
                </button>
              </div>
            </form>
            {searchClicked && (
              <i
                className="bi bi-search"
                style={{
                  border: "2px solid orange",
                  padding: 5,
                  cursor: "pointer",
                  marginBottom: 10,
                  textAlign: "center",
                }}
                onClick={() => {
                  this.setState(
                    {
                      search: "",
                      searchClicked: false,
                      isLoading: true,
                    },
                    this.getData
                  );
                }}
              >
                Clear Filter
              </i>
            )}
          </div>
          <div>
            <Button
              className="btn exportbtn"
              onClick={() => {
                this.setState((p) => ({
                  popUpOpen: !p.popUpOpen,
                  popupType: "ADD NEW CATEGORY",
                }));
              }}
            >
              Add New Category
            </Button>
          </div>
        </div>
        <table>
          <thead>
            <tr>
              <th>Category Name</th>
              <th>No of Questions</th>
              <th>Action</th>
              <th>
                Enable <br /> AI
              </th>
              <th>Translated</th>
              <th>
                Download <br />
                Questions
              </th>
            </tr>
          </thead>
          <tbody>
            {categoryData.length > 0 ? (
              categoryData.map((e, i) => {
                return (
                  <tr key={"category" + e.cid}>
                    <td>
                      <div style={{ display: "flex" }}>
                        <p>{e.cid + ") " + e.category_name}</p>
                      </div>
                    </td>

                    <td style={{ textAlign: "center" }}>{e.Qcount}</td>

                    <td>
                      <i
                        className="bi bi-pencil-fill"
                        style={{ marginRight: 10, cursor: "pointer" }}
                        onClick={() =>
                          this.setState({
                            popUpOpen: true,
                            editorLoading: true,
                            cateogryId: e.cid,
                            cateogryName: e.category_name,
                            cCount: e.Qcount,
                            popupType: "EDIT CATEGORY",
                          })
                        }
                      ></i>
                      <i
                        className="bi bi-trash-fill"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          this.setState(
                            {
                              cateogryId: e.cid,
                              openCategoryPop: true,
                              popupType: e.category_name,
                            }
                            // () => ()
                          )
                        }
                      ></i>
                    </td>
                    <td>
                      <i
                        className={
                          e.translate_ai === "1"
                            ? "bi bi-bookmark-check-fill"
                            : "bi bi-bookmark-check"
                        }
                        style={{
                          cursor: "pointer",
                          textAlign: "center",
                          marginLeft: 15,
                        }}
                        onClick={() =>
                          this.setState({ cateogryId: e.cid }, () =>
                            this.changeUserMedium(
                              e.translate_ai === "1" ? "0" : "1"
                            )
                          )
                        }
                      ></i>
                    </td>
                    <td style={{ textAlign: "center" }}>{e.Qcount2}</td>
                    <td>
                      <i
                        className="bi bi-download"
                        style={{ marginRight: 15, cursor: "pointer" }}
                        onClick={() => this.downloadTeluguQuestions(e.cid)}
                      >
                        Telugu
                      </i>
                      <i
                        className="bi bi-download"
                        style={{ cursor: "pointer", marginLeft: 10 }}
                        onClick={() => this.downloadReportedQuestions(e.cid)}

                      >
                        Reported
                      </i>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr
                style={{
                  display: "flex",
                  justifyContent: "center",
                  marginTop: 20,
                }}
              >
                <td colSpan={4}>
                  <p style={{ color: "black", textAlign: "center" }}>
                    No Categories are Available...
                  </p>
                </td>
              </tr>
            )}
          </tbody>
        </table>
        {this.renderPaginationButtons(categoryCount)}
      </div>
    );
  };
  handleOpen = () => {
    this.setState((p) => ({
      popUpOpen: false,
      cateogryId: "",
      cateogryName: "",
      popupType: "",
      openCategoryPop: false,
    }));
  };

  renderPopUp = () => {
    const { popUpOpen, cateogryName, popupType } = this.state;
    // console.log(bonus);
    return (
      <Dialog
        open={popUpOpen}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>{popupType}</p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <TextField
            required
            className="input-box register catgroy"
            label="Category Name"
            variant="filled"
            style={{ marginTop: 15 }}
            value={cateogryName}
            onChange={(e) => this.setState({ cateogryName: e.target.value })}
            focused
          />
        </DialogContent>
        <DialogActions
          style={{ display: "flex", justifyContent: "center", marginLeft: -10 }}
        >
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={
              popupType === "EDIT CATEGORY"
                ? this.saveCategory
                : this.addCategory
            }
          >
            Save changes
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  removeCategoryPopUp = () => {
    const { openCategoryPop, categoryData, cateogryId, popupType } = this.state;
    // console.log(bonus);
    const newCategoriesInfo = categoryData.filter((e) => e.cid !== cateogryId);
    return (
      <Dialog
        open={openCategoryPop}
        onClose={this.handleOpen}
        maxWidth={"sm"}
        fullWidth
      >
        <DialogTitle id="alert-dialog-title" className="supportdailog ">
          <div style={{ display: "flex", justifyContent: "space-between" }}>
            <div className="popupdata">
              <p>Remove Category ({popupType})</p>
            </div>
          </div>
        </DialogTitle>
        <DialogContent className="dailogContent">
          <Autocomplete
            id="free-solo-demo"
            freeSolo
            required
            getOptionLabel={(option) => option || ""} 
            style={{ marginTop: 15 }}
            onChange={(event, newValue) => {
              console.log(
                "🚀 ~ file: CategoryList.js ~ line 355 ~ CategoryList ~ newValue",
                newValue
              );
              // console.log(newValue);
              this.setState({
                newCategoryMoveTo: parseInt(newValue.split(")")[0]),
              });
            }}
            options={newCategoriesInfo.map(
              (option) => option["cid"] + ")" + option["category_name"]
            )}
            renderInput={(params) => (
              <TextField {...params} label="Select New Category to Move.." />
            )}
          />
        </DialogContent>
        <DialogActions
          style={{ display: "flex", justifyContent: "center", marginLeft: -10 }}
        >
          <Button
            className="btn header-btns attemptbtn attempt-btns submit popbtn"
            onClick={this.handleOpen}
          >
            Cancel
          </Button>
          <Button
            className="btn header-btns attemptbtn attempt-btns popbtn"
            onClick={this.deleteCategory}
          >
            Move and Remove
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  deleteCategory = async () => {
    const { cateogryId, categoryData, newCategoryMoveTo } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CategoryDelete",
      search: newCategoryMoveTo,
      qid: cateogryId,
    };
    if (newCategoryMoveTo !== 0) {
      try {
        const data = await axios.post(
          `${commonData["api"]}/admin/qbankdata`,
          body,
          { headers }
        );
        // console.log(categoryData);
        this.setState(
          {
            categoryData: categoryData.filter((e) => e.cid != cateogryId),
          },
          NotificationManager.success(`Category Deleted Succesfully...`)
        );
      } catch (err) {
        NotificationManager.error(`Something Went Wrong`);
      }
    } else {
      NotificationManager.error(`Please Select Category to Move data .`);
    }
  };

  renderPaginationButtons = (totalCount) => {
    const paidcount = Math.ceil(totalCount);
    const { page } = this.state;
    console.log(paidcount);
    return (
      <div className="pagination">
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page - 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === 0}
        >
          Back
        </Button>
        <Button
          className="btn navigate"
          onClick={() => {
            this.setState(
              (prev) => ({ page: prev.page + 1, isLoading: true }),
              () => this.getData()
            );
          }}
          disabled={page === paidcount - 1}
        >
          Next
        </Button>
      </div>
    );
  };
  saveCategory = async () => {
    const { cateogryId, categoryData, cateogryName, cCount } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CategoryEdit",
      search: cateogryName,
      qid: cateogryId,
    };

    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      const newCategory = {
        cid: cateogryId,
        category_name: cateogryName,
        Qcount: cCount,
      };
      console.log(data.data[0][0].result);
      if (data.data[0][0].result === "success") {
        const index = categoryData.findIndex(
          (ek) => ek == categoryData.filter((e) => e.cid === cateogryId)[0]
        );

        const removedData = categoryData.filter((e) => e.cid !== cateogryId);
        const newQUestionsList = removedData.splice(index, 1, newCategory);
        this.setState(
          {
            popUpOpen: false,
            categoryData: removedData,
          },
          NotificationManager.success(`Category Updated Succesfully...`)
        );
      } else {
        NotificationManager.error(`Category Name Already Exists..`);
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };

  addCategory = async () => {
    const { categoryData, cateogryName } = this.state;
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const body = {
      type: "CategoryAdd",
      search: cateogryName,
      qid: 0,
    };

    try {
      const data = await axios.post(
        `${commonData["api"]}/admin/qbankdata`,
        body,
        { headers }
      );
      console.log(data);
      const newCategory = {
        cid: data.data[1][0].cid,
        category_name: cateogryName,
        Qcount: data.data[1][0].Qcount,
      };

      if (data.data[0][0].result === "success") {
        let newCategories = categoryData;
        newCategories.unshift(newCategory);
        this.setState(
          {
            popUpOpen: false,
            categoryData: newCategories,
            cateogryId: "",
            cateogryName: "",
            cCount: 0,
          },
          NotificationManager.success(`Category Added Succesfully...`)
        );
      } else {
        NotificationManager.error(`Category Name Already Exists..`);
      }
    } catch (err) {
      NotificationManager.error(`Something Went Wrong`);
    }
  };

  render() {
    const { isLoading, login, popUpOpen } = this.state;
    return (
      <>
        {!isLoading && login === "valid" && (
          <>
            <div className="desktopsidebar">
              <div className="desktopsidebarmenuexamdetailsAdmin">
                <AdminMenu />
              </div>
              <Header />

              <Divider color="white" />
              <div className="viewresultsdesktop admin">
                {this.categoriesTable()}
                {popUpOpen && this.renderPopUp()}
                {this.removeCategoryPopUp()}
              </div>
            </div>
          </>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
        {!isLoading && login === "invalid" && (
          <div className="not-found-div">
            <img
              src={invalid}
              className="not-found-img"
              alt="not-found-image"
            />
            <Link to="/" className="linkto">
              <Button
                variant="contained"
                className="btn"
                style={{ marginTop: 20 }}
              >
                Go to HomePage
              </Button>
            </Link>
          </div>
        )}
        <div>
          <NotificationContainer />
        </div>
      </>
    );
  }
}

export default CategoryList;
