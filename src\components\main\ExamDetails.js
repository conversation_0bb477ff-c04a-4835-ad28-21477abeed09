import react from "react";
import Header from "../Header";
import Loader from "../Loader";
import Divider from "@mui/material/Divider";
import DesktopMenu from "../DesktopMenu";
import axios from "axios";
import <PERSON><PERSON> from "js-cookie";
import Button from "@mui/material/Button";
import { Link } from "react-router-dom";
import commonData from "../../importanValue";
import "./styles.css";

class ExamDetails extends react.Component {
  state = {
    isLoading: true,
    data: [],
    groupName: "",
    attempts: {},
    access: false,
    allowNegativeMarking: false,
    only_for_practice: false,
  };

  componentDidMount() {
    this.getData();
  }

  getData = async () => {
    const { match } = this.props;
    const { params } = match;
    const { quid } = params;
    console.log(quid);
    const token = Cookie.get("jwt_token");
    const headers = {
      "Content-Type": "application/json",
      authorization: token,
      "Access-Control-Allow-Origin": "*",
    };
    const quizInf = window.atob(quid);
    console.log("🚀 ~ ExamDetails ~ getData= ~ quizInf:", quizInf.split("$$_"));

    const quizList = await axios.get(
      `${commonData["api"]}/quiz_details/${quizInf}`,
      {
        headers,
      }
    );
    console.log("quizList", quizList.data[4][0].result);
    this.setState({
      groupName: "",
      isLoading: false,
      data: quizList.data[0][0],
      attempts: quizList.data[3][0] === undefined ? {} : quizList.data[3][0],
      access: quizList.data[4][0].result === 1 ? true : false,
      allowNegativeMarking:
        quizInf.split("$$_").slice(-2)[0] === "1" ||
        quizInf.split("$$_").slice(-2)[0] === "true",
      only_for_practice:
        quizInf.split("$$_").slice(-1)[0] === "1" ||
        quizInf.split("$$_").slice(-1)[0] === "true",
    });
  };

  /* Function to open fullscreen mode */
  openFullscreen = () => {
    var elem = document.documentElement;
    if (elem.requestFullscreen) {
      elem.requestFullscreen();
    } else if (elem.mozRequestFullScreen) {
      /* Firefox */
      elem.mozRequestFullScreen();
    } else if (elem.webkitRequestFullscreen) {
      /* Chrome, Safari & Opera */
      elem.webkitRequestFullscreen();
    } else if (elem.msRequestFullscreen) {
      /* IE/Edge */
      elem = window.top.document.body; //To break out of frame in IE
      elem.msRequestFullscreen();
    }
  };

  render() {
    const { match } = this.props;
    const { params } = match;
    const { quid } = params;
    const { isLoading, data, attempts, access, allowNegativeMarking } =
      this.state;
    let count =
      Object.values(attempts).length !== 0 ? attempts.attemptCount : 0;
    const {
      quiz_name,
      description,
      duration,
      maximum_attempts,
      pass_percentage,
    } = data;
    return (
      <>
        {!isLoading && (
          <div className="desktopsidebar">
            <div className="desktopsidebarmenuexamdetails">
              <DesktopMenu />
            </div>
            <Header />

            <Divider color="white" />
            <div className="examdetailsdesktop">
              <div className="title-quiz">
                <p className="homepage-package-title examlist-ttle">
                  Attempt Exam
                </p>
              </div>
              <div className="main-table-container">
                <table className="table">
                  <thead>
                    <tr>
                      <th className="tablehead examdetails">Exam Name</th>
                      <th className="tablehead examdetails">{quiz_name}</th>
                    </tr>
                  </thead>
                  <thead>
                    <tr>
                      <th className="tablehead examdetails" colSpan="2">
                        Description :
                        <div
                          dangerouslySetInnerHTML={{ __html: description }}
                        ></div>
                      </th>
                    </tr>
                  </thead>
                  <thead>
                    <tr>
                      <th className="tablehead examdetails">
                        Duration (in min.)
                      </th>
                      <th className="tablehead examdetails">{duration}</th>
                    </tr>
                  </thead>
                  <thead>
                    <tr>
                      <th className="tablehead examdetails">
                        Maximum Attempts
                      </th>
                      <th className="tablehead examdetails">
                        {maximum_attempts} ({" "}
                        {maximum_attempts - parseInt(count) > 0
                          ? maximum_attempts -
                            parseInt(count) +
                            " Attempts remaining"
                          : " You have reached Max.Attempt"}
                        )
                      </th>
                    </tr>
                  </thead>
                  <thead>
                    <tr>
                      <th className="tablehead examdetails">
                        Minimum Percentage Require to pass
                      </th>
                      <th className="tablehead examdetails">
                        {pass_percentage}
                      </th>
                    </tr>
                  </thead>
                </table>
              </div>
              <div className="main-table-container exam-details-instructions">
                <div>
                  <p className="instr-title">సూచనలు</p>
                  <p>
                    ఆన్ లైన్ పరీక్షలు రాయడంపై అవగాహన కలిగించేందుకు, ఆన్ లైన్
                    పరీక్షలను అభ్యాసం చేసేందుకు ఈ పరీక్షలు ఉద్దేశించబడ్డాయి.
                    వివిధ పోటీ పరీక్షలకు ఉపయుక్తమైన ఆన్ లైన్ టెస్ట్ లను అందించే
                    ఈ వెబ్ సైట్ లో ఆన్ లైన్ టెస్ట్ ను రాసే ముందు సూచనలను తప్పక
                    చదవండి.
                  </p>
                  <p>
                    * ముందుగా వెబ్ సైట్ లో రిజిస్టర్ చేసుకుని, లాగిన్ కావడం
                    ద్వారా ప్రవేశించాలి.
                  </p>
                  <p>* కోరిన టెస్ట్ ను రాయడానికి సిద్ధమవ్వాలి.</p>
                  <p>
                    * ప్రతి టెస్ట్ కు టైమ్ లిమిట్ ఉంటుంది. కనుక కేటాయించిన
                    సమయంలోనే సమాధానాలను గుర్తించేందుకు ప్రయత్నించండి. సమయం
                    ముగిసిన తరువాత ప్రశ్నాపత్రం దానంతట అదే ముగించబడుతుంది. కనుక
                    సమయపాలన ముఖ్యం.
                  </p>
                  <p>* ప్రతి ప్రశ్నకు నాలుగు ఐచ్చికాలు ఇవ్వబడతాయి. </p>
                  <p>* ప్రశ్నను శ్రద్దగా చదవండి.</p>
                  <p>
                    * ఐచ్చికాలలో సరైనది ఎంచుకుండి దానికి ఎదురుగా గల రేడియో బటన్
                    పై క్లిక్ చేయడం ద్వారా సమాధానం ఎంచుకోండి.
                  </p>
                  <p>
                    * సమాధానం ఎంచుకోవడం పూర్తయితే{" "}
                    <button className="savebtn detailsbtn">
                      Save &amp; Next
                    </button>
                    పై క్లిక్ చేయండి.
                  </p>
                  <p>
                    * ఇలా సమాధానం గుర్తించబడిన ప్రశ్న యొక్క సంఖ్య ఆకుపచ్చ రంగులో
                    కనిపిస్తుంది.
                  </p>
                  <p>
                    * ఒకవేళ సమాధానం తరువాత గుర్తించాలనుకుంటే{" "}
                    <button className="reviewbtn detailsbtn">
                      Review Later
                    </button>{" "}
                    పై క్లిక్ చేయాలి.
                  </p>
                  <p>
                    * తరువాత రివ్యూ చేయాలనుకున్న ప్రశ్న సంఖ్య నారింజ రంగులో
                    కనిపిస్తుంది.
                  </p>
                  <p>
                    * సమాధానాన్ని గుర్తించని ప్రశ్న యొక్క సంఖ్య ఎరుపు రంగులోనూ,
                    ఇంకా వీక్షించని ప్రశ్న యొక్క సంఖ్య నలుపు రంగులోనూ
                    కనిపిస్తుంది.
                  </p>
                  <p>
                    * వరుసగా అన్ని ప్రశ్నలకు సమాధానాలను గుర్తించిన తరువాత{" "}
                    <button className="submitbtn detailsbtn">Submit</button> పై
                    క్లిక్ చేయడం ద్వారా టెస్ట్ రాయడాన్ని పూర్తి చేయవచ్చు.
                  </p>
                  <p>
                    * పరీక్ష రాసిన తరువాత ఫలితాలు విశ్లేషణాత్మకంగా కనిపిస్తాయి.
                    దీనిలో మీరు గుర్తించిన సమాధానాలు, సరైన సమాధానాలు, మీరు
                    సాధించిన మార్కులు, ఇప్పటివరకూ ఆ టెస్ట్ రాసిన వారిలో మీ
                    ర్యాంక్, మీ స్కోరు పొజిషన్, టాప్ ర్యాంకర్ల వివరాలు అక్కడ
                    పొందుపరచబడతాయి.
                  </p>
                  <p>
                    * పరీక్షలో ప్రశ్నలు ఎంతో జాగ్రత్త వహిస్తూ రూపొందించినప్పటికీ
                    తప్పులు దొర్లే అవకాశం లేకపోలేదు.
                  </p>
                  <p>
                    * కనుక వీటిని ప్రామాణికంగా తీసుకోక, కేవలం అభ్యాసం కోసం
                    మాత్రమే వీటిని రూపొందించామని గమనించగలరు.
                  </p>
                  <p>
                    * ఈ టెస్ట్లలో అడిగే ప్రశ్నలలో దొర్లే తప్పులకు మేము
                    న్యాయపరమైన బాధ్యత వహించలేము.
                  </p>
                  <p>
                    * వీలున్నంత వరకూ తప్పులు లేని ప్రశ్నాపత్రాన్ని మీకు
                    అందించడమే మా లక్ష్యం. ప్రభుత్వం నిర్వహించే పరీక్షలలోనూ
                    తప్పులు దొర్లడం, ప్రాధమిక, ఫైనల్ కీలను ఇవ్వడం వంటి విషయాలను
                    దృష్టిలో ఉంచుకుని మీరు గుర్తించిన తప్పులను మాకు ఈమెయిల్
                    ద్వారా తెలియపరిచినట్లయితే సరిచేయగలము.
                  </p>
                  <p>
                    <font color="red">
                      * పెయిడ్ టెస్ట్ ల విషయంలో కానీ, అమౌంట్ పేమెంట్స్ విషయంలో
                      కానీ ఏదైనా లోపం ఎదురైనట్లయితే మీ అమౌంట్ ను తిరిగి పంపించే
                      బాధ్యత మాది.
                    </font>
                  </p>
                  <p>
                    <i>
                      * లాభాపేక్ష, ధనార్జన కాకుండా అందరికీ అందుబాటులో సదుపాయాలను{" "}
                      <b>అతి తక్కువ రుసుముతో లేదా ఉచితంగా</b> అందించాలన్న
                      సత్సంకల్పంతో ప్రారంభమైన నవచైతన్య కాంపిటీషన్స్ - ఆన్ లైన్
                      ఎగ్జామ్స్ పోర్టల్ కు మీ సలహాలు, సూచనలు అందించండి.
                    </i>
                  </p>
                </div>
                {maximum_attempts - count > 0 && access ? (
                  <Link
                    to={`/attempt-exam/${
                      window.atob(quid).split("$$_")[0]
                    }$$$_${window.btoa(
                      String(allowNegativeMarking)
                    )}$$$_${window.btoa(String(this.state.only_for_practice))}`}
                    className="linkto"
                  >
                    <div
                      className="gotoexamsbtnhome"
                      style={{ marginTop: -20 }}
                      onClick={this.openFullscreen}
                    >
                      <Button
                        variant="contained"
                        className="btn header-btns attemptbtn startexam"
                      >
                        Start Exam
                      </Button>
                    </div>
                  </Link>
                ) : !access ? (
                  <Button
                    variant="contained"
                    className="btn header-btns attemptbtn startexam"
                  >
                    You Dont Have access to Write this Exam
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    className="btn header-btns attemptbtn startexam"
                  >
                    You have reached maximum attempt ({maximum_attempts})
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="loader-main-container">
            <Loader />
          </div>
        )}
      </>
    );
  }
}

export default ExamDetails;
